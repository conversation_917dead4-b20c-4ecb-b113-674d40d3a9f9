<?php

namespace Modules\ModelAI\Models;

use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ModelCategorization extends Pivot
{
    /**
     * The table associated with the model.
     */
    protected $table = 'model_categorizations';

    /**
     * Indicates if the IDs are auto-incrementing.
     */
    public $incrementing = false;

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'model_ai_id',
        'model_category_id',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'model_ai_id' => 'integer',
        'model_category_id' => 'integer',
    ];

    /**
     * Get the AI model that owns the categorization.
     */
    public function modelAI(): BelongsTo
    {
        return $this->belongsTo(ModelAI::class, 'model_ai_id');
    }

    /**
     * Get the category that owns the categorization.
     */
    public function modelCategory(): BelongsTo
    {
        return $this->belongsTo(ModelCategory::class, 'model_category_id');
    }
}
