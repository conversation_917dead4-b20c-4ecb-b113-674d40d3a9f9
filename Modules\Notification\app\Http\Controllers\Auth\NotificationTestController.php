<?php

namespace Modules\Notification\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Core\Traits\ResponseTrait;
use Modules\Notification\Broadcasting\EmailChannel;
use Modules\Notification\Http\Requests\NotificationTestRequest;

class NotificationTestController extends Controller
{
    use ResponseTrait;

    /**
     * Test email configuration.
     */
    public function testEmail(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email|max:255',
        ]);

        $emailChannel = app(EmailChannel::class);
        $result = $emailChannel->testConfiguration($request->email);

        if ($result['success']) {
            return $this->successResponse($result, __('Test email sent successfully.'));
        } else {
            return $this->errorResponse($result, __('Test email failed.'), 422);
        }
    }

    /**
     * Get email configuration.
     */
    public function getEmailConfig(): JsonResponse
    {
        $emailChannel = app(EmailChannel::class);
        $config = $emailChannel->getConfiguration();

        return $this->successResponse($config, __('Email configuration retrieved successfully.'));
    }

    /**
     * Test notification sending with custom data.
     */
    public function testNotification(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'title' => 'nullable|string|max:255',
            'content' => 'required|string|max:5000',
            'priority' => 'nullable|string|in:low,normal,high',
        ]);

        try {
            $emailChannel = app(EmailChannel::class);
            
            $content = [
                'subject' => $request->subject,
                'title' => $request->title ?? $request->subject,
                'content' => $request->content,
                'settings' => [
                    'priority' => $request->priority ?? 'normal',
                ],
            ];

            $emailChannel->send($request->email, $content, [
                'test_mode' => true,
                'sent_by' => auth()->user()->name ?? 'System',
                'sent_at' => now()->toDateTimeString(),
            ]);

            return $this->successResponse([
                'message' => 'Test notification sent successfully',
                'details' => [
                    'to' => $request->email,
                    'subject' => $request->subject,
                    'sent_at' => now()->toISOString(),
                ]
            ], __('Test notification sent successfully.'));

        } catch (\Exception $e) {
            return $this->errorResponse([
                'error' => $e->getMessage(),
                'details' => [
                    'to' => $request->email,
                    'failed_at' => now()->toISOString(),
                ]
            ], __('Test notification failed: :error', ['error' => $e->getMessage()]), 422);
        }
    }

    /**
     * Get notification system status.
     */
    public function getSystemStatus(): JsonResponse
    {
        $status = [
            'notification_enabled' => setting('notification.enabled', false),
            'email_enabled' => setting('email.enabled', false),
            'queue_enabled' => setting('queue.enabled', false),
            'channels' => [
                'email' => [
                    'enabled' => setting('email.enabled', false),
                    'queue' => setting('email.queue', false),
                    'configured' => !empty(setting('smtp.host')) && !empty(setting('email.from.address')),
                ],
                'sms' => [
                    'enabled' => setting('sms.enabled', false),
                    'queue' => setting('sms.queue', false),
                    'configured' => !empty(setting('sms.provider')),
                ],
                'push' => [
                    'enabled' => setting('push.enabled', false),
                    'queue' => setting('push.queue', false),
                    'configured' => !empty(setting('fcm.server.key')),
                ],
            ],
            'smtp_config' => [
                'host' => setting('smtp.host', 'Not configured'),
                'port' => setting('smtp.port', 'Not configured'),
                'encryption' => setting('smtp.encryption', 'Not configured'),
                'username_configured' => !empty(setting('smtp.username')),
                'password_configured' => !empty(setting('smtp.password')),
            ],
            'last_check' => now()->toISOString(),
        ];

        return $this->successResponse($status, __('Notification system status retrieved successfully.'));
    }

    /**
     * Send test notification to multiple recipients.
     */
    public function bulkTest(Request $request): JsonResponse
    {
        $request->validate([
            'emails' => 'required|array|min:1|max:10',
            'emails.*' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'content' => 'required|string|max:5000',
        ]);

        $results = [];
        $emailChannel = app(EmailChannel::class);

        foreach ($request->emails as $email) {
            try {
                $content = [
                    'subject' => $request->subject,
                    'title' => $request->subject,
                    'content' => $request->content,
                    'settings' => ['priority' => 'normal'],
                ];

                $emailChannel->send($email, $content, [
                    'bulk_test' => true,
                    'sent_by' => auth()->user()->name ?? 'System',
                ]);

                $results[] = [
                    'email' => $email,
                    'status' => 'success',
                    'message' => 'Sent successfully',
                ];

            } catch (\Exception $e) {
                $results[] = [
                    'email' => $email,
                    'status' => 'failed',
                    'message' => $e->getMessage(),
                ];
            }
        }

        $successCount = count(array_filter($results, fn($r) => $r['status'] === 'success'));
        $failedCount = count($results) - $successCount;

        return $this->successResponse([
            'summary' => [
                'total' => count($results),
                'success' => $successCount,
                'failed' => $failedCount,
            ],
            'results' => $results,
            'sent_at' => now()->toISOString(),
        ], __('Bulk test completed. Success: :success, Failed: :failed', [
            'success' => $successCount,
            'failed' => $failedCount,
        ]));
    }
}
