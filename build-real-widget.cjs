#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const distDir = path.join(__dirname, 'dist', 'widget');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

const realWidgetTemplate = `
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ProcmsChatbot = {}));
})(this, (function (exports) {
  'use strict';

  // Debug logging
  function debugLog(message, data = null) {
    console.log('[ProcMS Widget]', message, data || '');
  }

  class EventEmitter {
    constructor() { 
      this.events = {};
    }
    on(event, callback) {
      if (!this.events[event]) this.events[event] = [];
      this.events[event].push(callback);
    }
    off(event, callback) {
      if (!this.events[event]) return;
      if (callback) {
        this.events[event] = this.events[event].filter(cb => cb !== callback);
      } else {
        this.events[event] = [];
      }
    }
    emit(event, ...args) {
      if (!this.events[event]) return;
      this.events[event].forEach(callback => {
        try { 
          callback(...args); 
        } catch (error) { 
          console.error('Event callback error:', error);
        }
      });
    }
  }

  class ProcmsAPI {
    constructor(config) {
      this.config = config;
      this.baseUrl = config.apiBaseUrl || window.location.origin + '/api/v1/widget';
      this.conversationId = null;
      this.botConfig = null;
      
      debugLog('ProcmsAPI initialized', { 
        baseUrl: this.baseUrl, 
        botUuid: config.botUuid 
      });
    }

    async makeRequest(endpoint, options = {}) {
      const url = \`\${this.baseUrl}\${endpoint}\`;
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers
      };

      // Add authorization header
      if (this.config.apiKey) {
        headers['Authorization'] = \`Bearer \${this.config.apiKey}\`;
      }

      debugLog('Making API request', { url, method: options.method || 'GET' });

      try {
        const response = await fetch(url, {
          method: options.method || 'GET',
          headers,
          body: options.body ? JSON.stringify(options.body) : undefined,
          ...options
        });

        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.message || \`HTTP \${response.status}\`);
        }

        debugLog('API response received', { status: response.status, data });
        return data;
      } catch (error) {
        console.error('API request failed:', error);
        throw error;
      }
    }

    async validateAccess() {
      try {
        const response = await this.makeRequest(\`/bot/\${this.config.botUuid}/validate\`);
        debugLog('Bot access validated', response);
        return response.success;
      } catch (error) {
        console.error('Bot access validation failed:', error);
        return false;
      }
    }

    async getBotConfig() {
      try {
        const response = await this.makeRequest(\`/bot/\${this.config.botUuid}/config\`);
        this.botConfig = response.data;
        debugLog('Bot config loaded', this.botConfig);
        return this.botConfig;
      } catch (error) {
        console.error('Failed to load bot config:', error);
        throw error;
      }
    }

    async createOrGetConversation() {
      try {
        const response = await this.makeRequest('/conversations', {
          method: 'POST',
          body: {
            bot_uuid: this.config.botUuid,
            user_id: this.config.userId || null,
            title: 'Widget Conversation'
          }
        });

        this.conversationId = response.data.uuid;
        debugLog('Conversation created/retrieved', { conversationId: this.conversationId });
        return response.data;
      } catch (error) {
        console.error('Failed to create conversation:', error);
        throw error;
      }
    }

    async getMessages() {
      if (!this.conversationId) {
        throw new Error('No active conversation');
      }

      try {
        const response = await this.makeRequest(\`/conversations/\${this.conversationId}/messages\`);
        debugLog('Messages retrieved', { count: response.data.messages.length });
        return response.data.messages;
      } catch (error) {
        console.error('Failed to get messages:', error);
        throw error;
      }
    }

    async sendMessage(content) {
      if (!this.conversationId) {
        throw new Error('No active conversation');
      }

      try {
        const response = await this.makeRequest(\`/conversations/\${this.conversationId}/messages\`, {
          method: 'POST',
          body: {
            message: content,
            messageType: 'text'
          }
        });

        debugLog('Message sent', { 
          userMessage: response.data.messages[0],
          botMessage: response.data.messages[1]
        });

        return {
          userMessage: response.data.messages[0],
          botMessage: response.data.messages[1],
          isProcessing: response.data.isProcessing
        };
      } catch (error) {
        console.error('Failed to send message:', error);
        throw error;
      }
    }

    async checkMessageStatus(messageId) {
      if (!this.conversationId) {
        throw new Error('No active conversation');
      }

      try {
        const response = await this.makeRequest(\`/conversations/\${this.conversationId}/messages/\${messageId}/status\`);
        return response.data;
      } catch (error) {
        console.error('Failed to check message status:', error);
        throw error;
      }
    }
  }

  class ChatWidget {
    constructor(config, container) {
      this.config = config;
      this.container = container;
      this.api = new ProcmsAPI(config);
      this.isTyping = false;
      this.messages = [];
      this.widget = null;
      this.messagesContainer = null;
      this.inputField = null;
      this.sendButton = null;
      this.isInitialized = false;
      
      debugLog('ChatWidget created');
    }

    async initialize() {
      try {
        debugLog('Initializing widget...');
        
        // Validate access
        const hasAccess = await this.api.validateAccess();
        if (!hasAccess) {
          throw new Error('Access denied to bot');
        }

        // Load bot configuration
        await this.api.getBotConfig();

        // Create or get conversation
        await this.api.createOrGetConversation();

        // Load existing messages
        const existingMessages = await this.api.getMessages();
        this.messages = existingMessages;

        this.isInitialized = true;
        debugLog('Widget initialized successfully');
        
      } catch (error) {
        console.error('Widget initialization failed:', error);
        throw error;
      }
    }

    async render() {
      debugLog('Rendering widget...');
      
      this.widget = document.createElement('div');
      this.widget.className = 'procms-chatbot-widget';
      this.widget.setAttribute('data-procms-theme', this.config.theme || 'light');
      this.widget.setAttribute('data-minimized', 'false');

      // Get bot info
      const botName = this.api.botConfig?.name || 'ProcMS Assistant';
      const greetingMessage = this.api.botConfig?.greeting_message || 'Xin chào! Tôi có thể giúp gì cho bạn?';

      this.widget.innerHTML = \`
        <div class="procms-widget-header" style="
          display: flex; align-items: center; justify-content: space-between;
          height: 60px; padding: 0 16px; background: var(--procms-primary, #3b82f6);
          color: white; cursor: pointer; user-select: none; flex-shrink: 0;
        ">
          <div style="display: flex; align-items: center; gap: 12px;">
            <div style="
              width: 32px; height: 32px; border-radius: 50%;
              background: rgba(255,255,255,0.2);
              display: flex; align-items: center; justify-content: center;
            ">🤖</div>
            <div>
              <div style="font-weight: 600;">\${botName}</div>
              <div style="font-size: 12px; opacity: 0.8;" class="status-indicator">Trực tuyến</div>
            </div>
          </div>
          <div style="display: flex; gap: 8px;">
            <button class="procms-minimize-btn" style="
              background: none; border: none; color: white; cursor: pointer;
              padding: 6px 10px; border-radius: 4px; font-size: 18px;
              font-weight: bold; transition: all 0.2s; line-height: 1;
            " title="Thu nhỏ">−</button>
            <button class="procms-close-btn" style="
              background: none; border: none; color: white; cursor: pointer;
              padding: 6px 10px; border-radius: 4px; font-size: 18px;
              font-weight: bold; transition: all 0.2s; line-height: 1;
            " title="Đóng">×</button>
          </div>
        </div>
        
        <div class="procms-messages-container" style="
          flex: 1; padding: 16px; background: var(--procms-widget-bg, #f9fafb);
          overflow-y: auto; display: flex; flex-direction: column; gap: 12px;
          min-height: 0; max-height: 400px;
        ">
          <!-- Messages will be loaded here -->
        </div>
        
        <div class="procms-widget-footer" style="
          padding: 16px; background: var(--procms-widget-surface, white);
          border-top: 1px solid var(--procms-widget-border, #e5e7eb);
          flex-shrink: 0;
        ">
          <div style="display: flex; gap: 8px; align-items: flex-end;">
            <div style="flex: 1;">
              <textarea class="procms-input" placeholder="Nhập tin nhắn của bạn..." style="
                width: 100%; padding: 12px 16px; border: 1px solid var(--procms-widget-border, #e5e7eb);
                border-radius: 24px; outline: none; font-size: 14px; resize: none;
                background: var(--procms-widget-bg, #f9fafb); color: var(--procms-text-primary, #111827);
                font-family: inherit; line-height: 1.4; min-height: 44px; max-height: 120px;
                box-sizing: border-box;
              "></textarea>
            </div>
            <button class="procms-send-btn" style="
              width: 44px; height: 44px; border-radius: 50%;
              background: var(--procms-primary, #3b82f6); color: white;
              border: none; cursor: pointer; display: flex;
              align-items: center; justify-content: center;
              font-size: 16px; transition: all 0.2s; flex-shrink: 0;
            " disabled>
              <span class="send-icon">→</span>
              <span class="loading-icon" style="display: none;">⏳</span>
            </button>
          </div>
        </div>
      \`;

      this.widget.style.cssText = \`
        display: flex; flex-direction: column; height: 100%;
        border-radius: 8px; overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      \`;

      this.container.appendChild(this.widget);
      this.setupEventListeners();
      this.setupAutoResize();
      
      // Load existing messages
      await this.loadMessages();
      
      debugLog('Widget rendered successfully');
      return this.widget;
    }

    setupEventListeners() {
      this.messagesContainer = this.widget.querySelector('.procms-messages-container');
      this.inputField = this.widget.querySelector('.procms-input');
      this.sendButton = this.widget.querySelector('.procms-send-btn');

      // Send message on button click
      this.sendButton.addEventListener('click', () => this.sendMessage());

      // Send message on Enter
      this.inputField.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          this.sendMessage();
        }
      });

      // Enable/disable send button
      this.inputField.addEventListener('input', () => {
        const hasText = this.inputField.value.trim().length > 0;
        this.sendButton.disabled = !hasText || this.isTyping;
        this.sendButton.style.opacity = hasText && !this.isTyping ? '1' : '0.5';
      });

      // Minimize/close functionality
      const minimizeBtn = this.widget.querySelector('.procms-minimize-btn');
      const closeBtn = this.widget.querySelector('.procms-close-btn');
      const header = this.widget.querySelector('.procms-widget-header');

      minimizeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggleMinimize();
      });

      header.addEventListener('click', () => this.toggleMinimize());

      closeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        if (this.onClose) this.onClose();
      });
    }

    setupAutoResize() {
      this.inputField.addEventListener('input', () => {
        this.inputField.style.height = 'auto';
        this.inputField.style.height = Math.min(this.inputField.scrollHeight, 120) + 'px';
      });
    }

    async loadMessages() {
      debugLog('Loading existing messages...');
      
      // Clear container
      this.messagesContainer.innerHTML = '';
      
      // Add greeting if no messages
      if (this.messages.length === 0 && this.api.botConfig?.greeting_message) {
        this.addMessage({
          type: 'bot',
          content: this.api.botConfig.greeting_message,
          timestamp: new Date().toISOString()
        });
      } else {
        // Add existing messages
        this.messages.forEach(message => this.addMessage(message));
      }
    }

    async sendMessage() {
      const content = this.inputField.value.trim();
      debugLog('Sending message:', content);
      
      if (!content || this.isTyping) {
        return;
      }

      // Clear input and disable send button
      this.inputField.value = '';
      this.inputField.style.height = '44px';
      this.isTyping = true;
      this.updateSendButton();

      try {
        // Show typing indicator
        this.showTypingIndicator();

        // Send to API
        const result = await this.api.sendMessage(content);

        // Hide typing indicator
        this.hideTypingIndicator();

        // Add messages to UI
        this.addMessage(result.userMessage);
        this.addMessage(result.botMessage);

        // If message is still processing, poll for updates
        if (result.isProcessing) {
          this.pollMessageStatus(result.botMessage.id);
        }

      } catch (error) {
        console.error('Send message error:', error);
        this.hideTypingIndicator();
        this.addMessage({
          type: 'bot',
          content: 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại.',
          timestamp: new Date().toISOString()
        });
      } finally {
        this.isTyping = false;
        this.updateSendButton();
        this.inputField.focus();
      }
    }

    async pollMessageStatus(messageId) {
      const maxAttempts = 30; // 30 seconds max
      let attempts = 0;

      const poll = async () => {
        try {
          const status = await this.api.checkMessageStatus(messageId);
          
          if (status.status === 'completed') {
            // Update message content
            this.updateMessageContent(messageId, status.content);
            return;
          }
          
          if (attempts < maxAttempts && status.status === 'pending') {
            attempts++;
            setTimeout(poll, 1000);
          }
        } catch (error) {
          console.error('Failed to poll message status:', error);
        }
      };

      poll();
    }

    updateMessageContent(messageId, newContent) {
      const messageEl = this.messagesContainer.querySelector(\`[data-message-id="\${messageId}"]\`);
      if (messageEl) {
        const contentEl = messageEl.querySelector('.message-content');
        if (contentEl) {
          contentEl.textContent = newContent;
        }
      }
    }

    addMessage(message) {
      const messageEl = document.createElement('div');
      messageEl.className = \`procms-message procms-message--\${message.type}\`;
      messageEl.setAttribute('data-message-id', message.id);
      
      const timestamp = new Date(message.timestamp).toLocaleTimeString();
      
      if (message.type === 'user') {
        messageEl.innerHTML = \`
          <div style="display: flex; gap: 8px; justify-content: flex-end;">
            <div style="
              background: var(--procms-primary, #3b82f6); color: white;
              padding: 12px 16px; border-radius: 18px; border-bottom-right-radius: 4px;
              max-width: 80%; word-wrap: break-word; white-space: pre-wrap;
            ">
              <div class="message-content">\${this.escapeHtml(message.content)}</div>
              <div style="font-size: 11px; opacity: 0.8; margin-top: 4px;">
                \${timestamp}
              </div>
            </div>
            <div style="
              width: 32px; height: 32px; border-radius: 50%;
              background: var(--procms-primary, #3b82f6); color: white;
              display: flex; align-items: center; justify-content: center;
              font-size: 14px; flex-shrink: 0;
            ">👤</div>
          </div>
        \`;
      } else {
        messageEl.innerHTML = \`
          <div style="display: flex; gap: 8px;">
            <div style="
              width: 32px; height: 32px; border-radius: 50%;
              background: var(--procms-primary, #3b82f6); color: white;
              display: flex; align-items: center; justify-content: center;
              font-size: 14px; flex-shrink: 0;
            ">🤖</div>
            <div style="
              background: white; padding: 12px 16px; border-radius: 18px;
              border-bottom-left-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);
              max-width: 80%; color: var(--procms-text-primary, #111827);
              word-wrap: break-word; white-space: pre-wrap;
            ">
              <div class="message-content">\${this.escapeHtml(message.content)}</div>
              <div style="font-size: 11px; color: #666; margin-top: 4px;">
                \${timestamp}
              </div>
            </div>
          </div>
        \`;
      }

      this.messagesContainer.appendChild(messageEl);
      this.scrollToBottom();
    }

    showTypingIndicator() {
      const typingEl = document.createElement('div');
      typingEl.className = 'procms-typing-indicator';
      typingEl.innerHTML = \`
        <div style="display: flex; gap: 8px;">
          <div style="
            width: 32px; height: 32px; border-radius: 50%;
            background: var(--procms-primary, #3b82f6); color: white;
            display: flex; align-items: center; justify-content: center;
            font-size: 14px; flex-shrink: 0;
          ">🤖</div>
          <div style="
            background: white; padding: 12px 16px; border-radius: 18px;
            border-bottom-left-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            color: var(--procms-text-primary, #111827);
          ">
            <div style="display: flex; gap: 4px; align-items: center;">
              <span>Đang nhập</span>
              <div style="display: flex; gap: 2px;">
                <div style="width: 4px; height: 4px; border-radius: 50%; background: #666; animation: typing-bounce 1.4s infinite ease-in-out;"></div>
                <div style="width: 4px; height: 4px; border-radius: 50%; background: #666; animation: typing-bounce 1.4s infinite ease-in-out 0.2s;"></div>
                <div style="width: 4px; height: 4px; border-radius: 50%; background: #666; animation: typing-bounce 1.4s infinite ease-in-out 0.4s;"></div>
              </div>
            </div>
          </div>
        </div>
      \`;

      this.messagesContainer.appendChild(typingEl);
      this.scrollToBottom();

      const statusIndicator = this.widget.querySelector('.status-indicator');
      if (statusIndicator) {
        statusIndicator.textContent = 'Đang nhập...';
      }
    }

    hideTypingIndicator() {
      const typingEl = this.widget.querySelector('.procms-typing-indicator');
      if (typingEl) {
        typingEl.remove();
      }

      const statusIndicator = this.widget.querySelector('.status-indicator');
      if (statusIndicator) {
        statusIndicator.textContent = 'Trực tuyến';
      }
    }

    updateSendButton() {
      const sendIcon = this.sendButton.querySelector('.send-icon');
      const loadingIcon = this.sendButton.querySelector('.loading-icon');
      
      if (this.isTyping) {
        sendIcon.style.display = 'none';
        loadingIcon.style.display = 'block';
        this.sendButton.disabled = true;
      } else {
        sendIcon.style.display = 'block';
        loadingIcon.style.display = 'none';
        this.sendButton.disabled = this.inputField.value.trim().length === 0;
      }
      
      this.sendButton.style.opacity = this.sendButton.disabled ? '0.5' : '1';
    }

    scrollToBottom() {
      setTimeout(() => {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
      }, 100);
    }

    escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    toggleMinimize() {
      const isMinimized = this.widget.getAttribute('data-minimized') === 'true';
      const body = this.widget.querySelector('.procms-messages-container');
      const footer = this.widget.querySelector('.procms-widget-footer');
      const minimizeBtn = this.widget.querySelector('.procms-minimize-btn');
      
      if (isMinimized) {
        // Maximize
        this.widget.setAttribute('data-minimized', 'false');
        body.style.display = 'flex';
        footer.style.display = 'block';
        minimizeBtn.textContent = '−';
        this.widget.style.height = '100%';
        if (this.container) this.container.style.height = this.originalHeight || '600px';
      } else {
        // Minimize
        this.widget.setAttribute('data-minimized', 'true');
        body.style.display = 'none';
        footer.style.display = 'none';
        minimizeBtn.textContent = '□';
        this.widget.style.height = '60px';
        if (this.container) this.container.style.height = '60px';
      }
    }
  }

  class ProcmsChatbotWidget extends EventEmitter {
    constructor(config) {
      super();
      if (!config.botUuid) throw new Error('Bot UUID is required');
      if (!config.apiKey) throw new Error('API Key is required');
      this.config = config;
      this.mounted = false;
      this.chatWidget = null;
      debugLog('ProcmsChatbotWidget created');
    }

    async mount(selector) {
      debugLog('Mounting widget to selector:', selector);
      
      const container = typeof selector === 'string' ? document.querySelector(selector) : selector;
      if (!container) {
        throw new Error('Container not found: ' + selector);
      }

      this.container = container;
      this.chatWidget = new ChatWidget(this.config, container);
      this.chatWidget.onClose = () => this.unmount();
      this.chatWidget.originalHeight = container.style.height || '600px';
      
      // Initialize and render
      await this.chatWidget.initialize();
      await this.chatWidget.render();
      
      this.mounted = true;

      debugLog('Widget mounted successfully');
      this.emit('ready');
      if (this.config.onReady) this.config.onReady();
    }

    unmount() {
      debugLog('Unmounting widget...');
      if (this.chatWidget && this.chatWidget.widget) {
        this.chatWidget.widget.remove();
      }
      this.mounted = false;
      this.emit('unmounted');
    }

    isMounted() { return this.mounted; }
    minimize() { if (this.chatWidget) this.chatWidget.toggleMinimize(); }
    maximize() { if (this.chatWidget) this.chatWidget.toggleMinimize(); }
  }

  class ProcmsChatbot {
    static async create(config, selector) {
      debugLog('ProcmsChatbot.create called');
      const widget = new ProcmsChatbotWidget(config);
      await widget.mount(selector);
      return widget;
    }

    static async createFloatingWidget(config) {
      debugLog('ProcmsChatbot.createFloatingWidget called');
      const container = document.createElement('div');
      container.id = 'procms-floating-widget';
      container.style.cssText = \`
        position: fixed; bottom: 20px; right: 20px;
        width: 400px; height: 600px; z-index: 9999;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        border-radius: 8px; overflow: hidden;
      \`;
      document.body.appendChild(container);

      const widget = new ProcmsChatbotWidget(config);
      await widget.mount(container);
      return widget;
    }
  }

  // Add CSS animations
  const style = document.createElement('style');
  style.textContent = \`
    @keyframes typing-bounce {
      0%, 60%, 100% { transform: translateY(0); }
      30% { transform: translateY(-10px); }
    }
  \`;
  document.head.appendChild(style);

  exports.ProcmsChatbotWidget = ProcmsChatbotWidget;
  exports.ProcmsChatbot = ProcmsChatbot;

  if (typeof window !== 'undefined') {
    window.ProcmsChatbotWidget = ProcmsChatbotWidget;
    window.ProcmsChatbot = ProcmsChatbot;
  }

  debugLog('ProcMS Real Chatbot library loaded');

}));
`;

fs.writeFileSync(path.join(distDir, 'procms-chatbot.umd.js'), realWidgetTemplate);
console.log('✅ Real API Widget built!');
console.log('🔗 Features:');
console.log('  - Real API integration with backend');
console.log('  - Bot access validation');
console.log('  - Conversation management');
console.log('  - Message sending/receiving');
console.log('  - Message status polling');
console.log('  - Existing message loading');
console.log('  - Error handling');
console.log('  - Authentication with API keys');
