<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       // Bảng notification_types
        Schema::create('notification_types', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('channels')->nullable();
            $table->json('default_settings')->nullable();
            $table->boolean('is_system')->default(false);
            $table->string('status')->default('active');
            $table->timestamps();
            $table->softDeletes();
            $table->index('status');
            $table->index('is_system');
        });

        // Bảng notification_templates
        Schema::create('notification_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('notification_type_id')->constrained()->onDelete('cascade');
            $table->string('channel');
            $table->string('locale', 10)->default('en');
            $table->string('subject')->nullable();
            $table->text('title')->nullable();
            $table->longText('content');
            $table->json('variables')->nullable();
            $table->json('settings')->nullable();
            $table->string('status')->default('active');
            $table->timestamps();
            $table->softDeletes();
            $table->unique(['notification_type_id', 'channel', 'locale'], 'notif_template_unique');
            $table->index(['channel', 'locale']);
            $table->index('status');
        });

        // Bảng notifications
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('notification_type_id')->constrained()->onDelete('cascade');
            $table->morphs('notifiable');
            $table->string('channel');
            $table->string('title');
            $table->text('content');
            $table->json('data')->nullable();
            $table->json('metadata')->nullable();
            $table->string('priority')->default('normal');
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->text('failure_reason')->nullable();
            $table->integer('retry_count')->default(0);
            $table->string('status')->default('pending');
            $table->timestamps();
            $table->softDeletes();
            $table->index(['channel', 'status']);
            $table->index(['status', 'scheduled_at']);
            $table->index(['priority', 'created_at']);
            $table->index('read_at');
            $table->index('sent_at');
        });

        // Bảng notification_preferences
        Schema::create('notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->morphs('notifiable');
            $table->foreignId('notification_type_id')->constrained()->onDelete('cascade');
            $table->string('channel');
            $table->boolean('enabled')->default(true);
            $table->json('settings')->nullable();
            $table->time('quiet_hours_start')->nullable();
            $table->time('quiet_hours_end')->nullable();
            $table->string('timezone')->nullable();
            $table->timestamps();
            $table->unique([
                'notifiable_type',
                'notifiable_id',
                'notification_type_id',
                'channel'
            ], 'notif_preference_unique');
            $table->index(['notification_type_id', 'channel']);
            $table->index('enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_preferences');
        Schema::dropIfExists('notifications');
        Schema::dropIfExists('notification_templates');
        Schema::dropIfExists('notification_types');
    }
};
