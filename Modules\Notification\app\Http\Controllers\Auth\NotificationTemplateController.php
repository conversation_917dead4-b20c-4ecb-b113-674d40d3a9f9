<?php

namespace Modules\Notification\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Core\Traits\ResponseTrait;
use Modules\Notification\Models\NotificationTemplate;
use Modules\Notification\Http\Requests\NotificationTemplateRequest;
use Modules\Notification\Http\Requests\BulkNotificationRequest;
use Modules\Notification\Http\Filters\NotificationTemplateFilter;

class NotificationTemplateController extends Controller
{
    use ResponseTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $templates = NotificationTemplate::query()
            ->with(['notificationType:id,key,name'])
            ->filter(new NotificationTemplateFilter($request))
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($templates, __('Notification templates retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(NotificationTemplateRequest $request): JsonResponse
    {
        $template = NotificationTemplate::create($request->validated());
        $template->load('notificationType:id,key,name');

        return $this->successResponse($template, __('Notification template created successfully.'), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(NotificationTemplate $notificationTemplate): JsonResponse
    {
        $notificationTemplate->load('notificationType:id,key,name');
        
        return $this->successResponse($notificationTemplate, __('Notification template retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(NotificationTemplateRequest $request, NotificationTemplate $notificationTemplate): JsonResponse
    {
        $notificationTemplate->update($request->validated());
        $notificationTemplate->load('notificationType:id,key,name');

        return $this->successResponse($notificationTemplate->fresh(), __('Notification template updated successfully.'));
    }

    /**
     * Remove the specified resource from storage (soft delete).
     */
    public function destroy(NotificationTemplate $notificationTemplate): JsonResponse
    {
        $notificationTemplate->delete();

        return $this->successResponse($notificationTemplate->id, __('Notification template deleted successfully.'));
    }

    /**
     * Bulk soft delete multiple resources.
     */
    public function bulkDestroy(BulkNotificationRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');
        $deletedCount = NotificationTemplate::whereIn('id', $ids)->delete();

        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count notification templates deleted successfully.', ['count' => $deletedCount])
        );
    }

    /**
     * Restore the specified soft-deleted resource.
     */
    public function restore(int $id): JsonResponse
    {
        $template = NotificationTemplate::onlyTrashed()->findOrFail($id);
        $template->restore();
        $template->load('notificationType:id,key,name');

        return $this->successResponse($template->fresh(), __('Notification template restored successfully.'));
    }

    /**
     * Bulk restore multiple soft-deleted resources.
     */
    public function bulkRestore(BulkNotificationRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');
        $restoredCount = NotificationTemplate::onlyTrashed()->whereIn('id', $ids)->restore();

        return $this->successResponse(
            ['restored_count' => $restoredCount],
            __(':count notification templates restored successfully.', ['count' => $restoredCount])
        );
    }

    /**
     * Permanently delete the specified resource.
     */
    public function forceDelete(int $id): JsonResponse
    {
        $template = NotificationTemplate::onlyTrashed()->findOrFail($id);
        $template->forceDelete();

        return $this->successResponse([$id], __('Notification template permanently deleted.'));
    }

    /**
     * Bulk permanently delete multiple resources.
     */
    public function bulkForceDelete(BulkNotificationRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');
        $deletedCount = NotificationTemplate::onlyTrashed()->whereIn('id', $ids)->forceDelete();

        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count notification templates permanently deleted.', ['count' => $deletedCount])
        );
    }

    /**
     * Activate multiple templates.
     */
    public function bulkActivate(BulkNotificationRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');
        $activatedCount = NotificationTemplate::whereIn('id', $ids)->update(['status' => 'active']);

        return $this->successResponse(
            ['activated_count' => $activatedCount],
            __(':count notification templates activated successfully.', ['count' => $activatedCount])
        );
    }

    /**
     * Deactivate multiple templates.
     */
    public function bulkDeactivate(BulkNotificationRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');
        $deactivatedCount = NotificationTemplate::whereIn('id', $ids)->update(['status' => 'inactive']);

        return $this->successResponse(
            ['deactivated_count' => $deactivatedCount],
            __(':count notification templates deactivated successfully.', ['count' => $deactivatedCount])
        );
    }

    /**
     * Preview template rendering with sample data.
     */
    public function preview(NotificationTemplate $notificationTemplate, Request $request): JsonResponse
    {
        $sampleData = $request->input('data', $this->getSampleData($notificationTemplate));
        
        try {
            $rendered = $notificationTemplate->render($sampleData);
            
            return $this->successResponse([
                'template' => $notificationTemplate->only(['id', 'channel', 'locale', 'subject', 'title', 'content']),
                'sample_data' => $sampleData,
                'rendered' => $rendered,
                'missing_variables' => $notificationTemplate->getMissingVariables($sampleData),
            ], __('Template preview generated successfully.'));
        } catch (\Exception $e) {
            return $this->errorResponse(
                ['error' => $e->getMessage()],
                __('Failed to generate template preview.'),
                422
            );
        }
    }

    /**
     * Get dropdown list of templates for a specific notification type.
     */
    public function dropdown(Request $request): JsonResponse
    {
        $query = NotificationTemplate::query()->active();
        
        if ($request->has('notification_type_id')) {
            $query->where('notification_type_id', $request->notification_type_id);
        }
        
        if ($request->has('channel')) {
            $query->where('channel', $request->channel);
        }
        
        if ($request->has('locale')) {
            $query->where('locale', $request->locale);
        }

        $templates = $query->select(['id', 'notification_type_id', 'channel', 'locale', 'subject', 'title'])
            ->with('notificationType:id,key,name')
            ->get();

        return $this->successResponse(
            $templates->makeHidden(['created_at', 'updated_at', 'status']),
            __('Notification templates retrieved successfully.')
        );
    }

    /**
     * Get sample data for template preview.
     */
    protected function getSampleData(NotificationTemplate $notificationTemplate): array
    {
        $variables = $notificationTemplate->getVariables();
        $sampleData = [];

        foreach ($variables as $variable) {
            $sampleData[$variable] = $this->getSampleValueForVariable($variable);
        }

        return $sampleData;
    }

    /**
     * Get sample value for a specific variable.
     */
    protected function getSampleValueForVariable(string $variable): string
    {
        $sampleValues = [
            'app_name' => 'Laravel ProCMS',
            'user_name' => 'John Doe',
            'user_email' => '<EMAIL>',
            'verification_url' => 'https://example.com/verify/abc123',
            'reset_url' => 'https://example.com/reset/xyz789',
            'expires_in' => '24 hours',
            'alert_message' => 'Suspicious login detected from new device',
            'alert_time' => now()->format('Y-m-d H:i:s'),
            'alert_type' => 'Login Alert',
            'maintenance_date' => now()->addDays(7)->format('Y-m-d'),
            'start_time' => '02:00 AM',
            'end_time' => '06:00 AM',
            'maintenance_description' => 'System upgrade and security patches',
        ];

        return $sampleValues[$variable] ?? ucwords(str_replace('_', ' ', $variable));
    }
}
