<?php

namespace Modules\Currency\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use Modules\Currency\Models\Currency;
use Modules\Currency\Models\ExchangeRate;
use PHPUnit\Framework\Attributes\Test;

class CurrencyModelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Currency module
        $this->artisan('migrate', ['--path' => 'Modules/Currency/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_can_create_a_currency()
    {
        $currencyData = [
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
            'sort_order' => 1,
        ];

        $currency = Currency::create($currencyData);

        $this->assertInstanceOf(Currency::class, $currency);
        $this->assertDatabaseHas('currencies', [
            'id' => $currency->id,
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'status' => 'active',
        ]);
        $this->assertEquals('USD', $currency->code);
        $this->assertEquals('$', $currency->symbol);
        $this->assertEquals('US Dollar', $currency->name);
        $this->assertEquals(2, $currency->decimal_digits);
        $this->assertEquals('active', $currency->status);
    }

    #[Test]
    public function it_has_fillable_attributes()
    {
        $currency = new Currency();
        $expectedFillable = [
            'code',
            'symbol',
            'name',
            'decimal_digits',
            'decimal_separator',
            'thousands_separator',
            'status',
            'sort_order',
        ];

        $this->assertEquals($expectedFillable, $currency->getFillable());
    }

    #[Test]
    public function it_has_correct_casts()
    {
        $currency = new Currency();
        $expectedCasts = [
            'decimal_digits' => 'integer',
            'sort_order' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];

        foreach ($expectedCasts as $attribute => $cast) {
            $this->assertEquals($cast, $currency->getCasts()[$attribute]);
        }
    }

    #[Test]
    public function it_can_scope_active_currencies()
    {
        $activeCurrency = Currency::factory()->create(['status' => 'active']);
        $inactiveCurrency = Currency::factory()->create(['status' => 'inactive']);

        $activeCurrencies = Currency::active()->get();

        $this->assertTrue($activeCurrencies->contains($activeCurrency));
        $this->assertFalse($activeCurrencies->contains($inactiveCurrency));
    }

    #[Test]
    public function it_can_scope_by_status()
    {
        $activeCurrency = Currency::factory()->create(['status' => 'active']);
        $inactiveCurrency = Currency::factory()->create(['status' => 'inactive']);

        $activeCurrencies = Currency::status('active')->get();
        $inactiveCurrencies = Currency::status('inactive')->get();

        $this->assertTrue($activeCurrencies->contains($activeCurrency));
        $this->assertFalse($activeCurrencies->contains($inactiveCurrency));
        $this->assertTrue($inactiveCurrencies->contains($inactiveCurrency));
        $this->assertFalse($inactiveCurrencies->contains($activeCurrency));
    }

    #[Test]
    public function it_can_scope_ordered_currencies()
    {
        $currency1 = Currency::factory()->create(['sort_order' => 3, 'name' => 'Z Currency']);
        $currency2 = Currency::factory()->create(['sort_order' => 1, 'name' => 'A Currency']);
        $currency3 = Currency::factory()->create(['sort_order' => 2, 'name' => 'B Currency']);

        $orderedCurrencies = Currency::ordered()->get();

        $this->assertEquals($currency2->id, $orderedCurrencies->first()->id);
        $this->assertEquals($currency1->id, $orderedCurrencies->last()->id);
    }

    #[Test]
    public function it_has_base_exchange_rates_relationship()
    {
        $currency = Currency::factory()->create(['code' => 'USD']);
        $exchangeRate = ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
        ]);

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\HasMany::class, $currency->baseExchangeRates());
        $this->assertTrue($currency->baseExchangeRates->contains($exchangeRate));
    }

    #[Test]
    public function it_has_target_exchange_rates_relationship()
    {
        $currency = Currency::factory()->create(['code' => 'EUR']);
        $exchangeRate = ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
        ]);

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\HasMany::class, $currency->targetExchangeRates());
        $this->assertTrue($currency->targetExchangeRates->contains($exchangeRate));
    }

    #[Test]
    public function it_can_format_amount()
    {
        $currency = Currency::factory()->create([
            'symbol' => '$',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
        ]);

        $formatted = $currency->formatAmount(1234.56);

        $this->assertEquals('$ 1,234.56', $formatted);
    }

    #[Test]
    public function it_can_format_amount_with_different_settings()
    {
        $currency = Currency::factory()->create([
            'symbol' => '€',
            'decimal_digits' => 2,
            'decimal_separator' => ',',
            'thousands_separator' => '.',
        ]);

        $formatted = $currency->formatAmount(1234.56);

        $this->assertEquals('€ 1.234,56', $formatted);
    }

    #[Test]
    public function it_can_format_amount_with_zero_decimal_digits()
    {
        $currency = Currency::factory()->create([
            'symbol' => '₫',
            'decimal_digits' => 0,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
        ]);

        $formatted = $currency->formatAmount(1234.56);

        $this->assertEquals('₫ 1,235', $formatted);
    }

    #[Test]
    public function it_has_display_name_attribute()
    {
        $currency = Currency::factory()->create([
            'name' => 'US Dollar',
            'symbol' => '$',
        ]);

        $this->assertEquals('US Dollar ($)', $currency->display_name);
    }

    #[Test]
    public function it_can_check_if_currency_is_active()
    {
        $activeCurrency = Currency::factory()->create(['status' => 'active']);
        $inactiveCurrency = Currency::factory()->create(['status' => 'inactive']);

        $this->assertTrue($activeCurrency->isActive());
        $this->assertFalse($inactiveCurrency->isActive());
    }

    #[Test]
    public function it_can_check_if_currency_is_default()
    {
        // Mock setting function to return USD as default
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_default') {
                    return 'USD';
                }
                return $default;
            };
        });

        $usdCurrency = Currency::factory()->create(['code' => 'USD']);
        $eurCurrency = Currency::factory()->create(['code' => 'EUR']);

        $this->assertTrue($usdCurrency->isDefault());
        $this->assertFalse($eurCurrency->isDefault());
    }

    #[Test]
    public function it_can_find_currency_by_code()
    {
        $currency = Currency::factory()->create(['code' => 'USD', 'status' => 'active']);
        Currency::factory()->create(['code' => 'EUR', 'status' => 'inactive']);

        $foundCurrency = Currency::findByCode('USD');
        $notFoundCurrency = Currency::findByCode('EUR'); // inactive
        $nonExistentCurrency = Currency::findByCode('GBP');

        $this->assertNotNull($foundCurrency);
        $this->assertEquals($currency->id, $foundCurrency->id);
        $this->assertNull($notFoundCurrency);
        $this->assertNull($nonExistentCurrency);
    }

    #[Test]
    public function it_can_get_active_currencies()
    {
        Currency::factory()->create(['status' => 'active', 'sort_order' => 2]);
        Currency::factory()->create(['status' => 'active', 'sort_order' => 1]);
        Currency::factory()->create(['status' => 'inactive']);

        $activeCurrencies = Currency::getActiveCurrencies();

        $this->assertCount(2, $activeCurrencies);
        $this->assertEquals(['code', 'symbol', 'name', 'decimal_digits', 'decimal_separator', 'thousands_separator'], array_keys($activeCurrencies->first()->toArray()));
    }

    #[Test]
    public function it_can_scope_default_currency()
    {
        // Mock setting function
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_default') {
                    return 'USD';
                }
                return $default;
            };
        });

        $usdCurrency = Currency::factory()->create(['code' => 'USD']);
        Currency::factory()->create(['code' => 'EUR']);

        $defaultCurrency = Currency::default()->first();

        $this->assertNotNull($defaultCurrency);
        $this->assertEquals($usdCurrency->id, $defaultCurrency->id);
    }

    #[Test]
    public function it_has_correct_table_structure()
    {
        $this->assertTrue(Schema::hasTable('currencies'));

        $expectedColumns = [
            'id', 'code', 'symbol', 'name', 'decimal_digits', 'decimal_separator',
            'thousands_separator', 'status', 'sort_order', 'created_at', 'updated_at'
        ];

        foreach ($expectedColumns as $column) {
            $this->assertTrue(Schema::hasColumn('currencies', $column));
        }
    }

    #[Test]
    public function it_uses_currency_factory()
    {
        $currency = Currency::factory()->create();

        $this->assertInstanceOf(Currency::class, $currency);
        $this->assertNotNull($currency->code);
        $this->assertNotNull($currency->symbol);
        $this->assertNotNull($currency->name);
    }
}
