<?php

namespace Modules\Currency\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Currency\Models\Currency;

/**
 * @extends Factory<Currency>
 */
class CurrencyFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Currency::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $currencies = [
            ['code' => 'USD', 'symbol' => '$', 'name' => 'US Dollar', 'decimal_digits' => 2],
            ['code' => 'EUR', 'symbol' => '€', 'name' => 'Euro', 'decimal_digits' => 2],
            ['code' => 'GBP', 'symbol' => '£', 'name' => 'British Pound', 'decimal_digits' => 2],
            ['code' => 'JPY', 'symbol' => '¥', 'name' => 'Japanese Yen', 'decimal_digits' => 0],
            ['code' => 'VND', 'symbol' => '₫', 'name' => 'Vietnamese Dong', 'decimal_digits' => 0],
            ['code' => 'CNY', 'symbol' => '¥', 'name' => 'Chinese Yuan', 'decimal_digits' => 2],
            ['code' => 'KRW', 'symbol' => '₩', 'name' => 'South Korean Won', 'decimal_digits' => 0],
            ['code' => 'THB', 'symbol' => '฿', 'name' => 'Thai Baht', 'decimal_digits' => 2],
            ['code' => 'SGD', 'symbol' => 'S$', 'name' => 'Singapore Dollar', 'decimal_digits' => 2],
            ['code' => 'MYR', 'symbol' => 'RM', 'name' => 'Malaysian Ringgit', 'decimal_digits' => 2],
            ['code' => 'INR', 'symbol' => '₹', 'name' => 'Indian Rupee', 'decimal_digits' => 2],
            ['code' => 'AUD', 'symbol' => 'A$', 'name' => 'Australian Dollar', 'decimal_digits' => 2],
            ['code' => 'CAD', 'symbol' => 'C$', 'name' => 'Canadian Dollar', 'decimal_digits' => 2],
            ['code' => 'NZD', 'symbol' => '$', 'name' => 'New Zealand Dollar', 'decimal_digits' => 2],
            ['code' => 'RUB', 'symbol' => '₽', 'name' => 'Russian Ruble', 'decimal_digits' => 2],
            ['code' => 'ZAR', 'symbol' => 'R', 'name' => 'South African Rand', 'decimal_digits' => 2],
            ['code' => 'BRL', 'symbol' => 'R$', 'name' => 'Brazilian Real', 'decimal_digits' => 2],
            ['code' => 'MXN', 'symbol' => '$', 'name' => 'Mexican Peso', 'decimal_digits' => 2],
            ['code' => 'IDR', 'symbol' => 'Rp', 'name' => 'Indonesian Rupiah', 'decimal_digits' => 0],
            ['code' => 'PHP', 'symbol' => '₱', 'name' => 'Philippine Peso', 'decimal_digits' => 2],
            ['code' => 'AED', 'symbol' => 'د.إ', 'name' => 'United Arab Emirates Dirham', 'decimal_digits' => 2],
            ['code' => 'SAR', 'symbol' => 'ر.س', 'name' => 'Saudi Riyal', 'decimal_digits' => 2],
            ['code' => 'TRY', 'symbol' => '₺', 'name' => 'Turkish Lira', 'decimal_digits' => 2],
            ['code' => 'PKR', 'symbol' => '₨', 'name' => 'Pakistani Rupee', 'decimal_digits' => 2],
            ['code' => 'BDT', 'symbol' => '৳', 'name' => 'Bangladeshi Taka', 'decimal_digits' => 2],
            ['code' => 'NOK', 'symbol' => 'kr', 'name' => 'Norwegian Krone', 'decimal_digits' => 2],
            ['code' => 'SEK', 'symbol' => 'kr', 'name' => 'Swedish Krona', 'decimal_digits' => 2],
            ['code' => 'DKK', 'symbol' => 'kr.', 'name' => 'Danish Krone', 'decimal_digits' => 2],
            ['code' => 'PLN', 'symbol' => 'zł', 'name' => 'Polish Zloty', 'decimal_digits' => 2],
            ['code' => 'CZK', 'symbol' => 'Kč', 'name' => 'Czech Koruna', 'decimal_digits' => 2],
            ['code' => 'HUF', 'symbol' => 'Ft', 'name' => 'Hungarian Forint', 'decimal_digits' => 2],
            ['code' => 'ILS', 'symbol' => '₪', 'name' => 'Israeli New Shekel', 'decimal_digits' => 2],
            ['code' => 'CLP', 'symbol' => '$', 'name' => 'Chilean Peso', 'decimal_digits' => 0],
            ['code' => 'COP', 'symbol' => '$', 'name' => 'Colombian Peso', 'decimal_digits' => 2],
            ['code' => 'PEN', 'symbol' => 'S/', 'name' => 'Peruvian Sol', 'decimal_digits' => 2],
            ['code' => 'RON', 'symbol' => 'lei', 'name' => 'Romanian Leu', 'decimal_digits' => 2],
            ['code' => 'HRK', 'symbol' => 'kn', 'name' => 'Croatian Kuna', 'decimal_digits' => 2],
            ['code' => 'BHD', 'symbol' => '.د.ب', 'name' => 'Bahraini Dinar', 'decimal_digits' => 3],
            ['code' => 'OMR', 'symbol' => '.ع.ر', 'name' => 'Omani Rial', 'decimal_digits' => 3],
            ['code' => 'QAR', 'symbol' => '.ر.ق', 'name' => 'Qatari Riyal', 'decimal_digits' => 2],
            ['code' => 'KWD', 'symbol' => '.د.ك', 'name' => 'Kuwaiti Dinar', 'decimal_digits' => 3],
        ];

        $currency = $this->faker->randomElement($currencies);



        return [
            'code' => $currency['code'],
            'symbol' => $currency['symbol'],
            'name' => $currency['name'],
            'decimal_digits' => $currency['decimal_digits'],
            'decimal_separator' => $this->faker->randomElement(['.', ',']),
            'thousands_separator' => $this->faker->randomElement([',', '.', ' ']),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'sort_order' => $this->faker->numberBetween(1, 100),
        ];
    }

    /**
     * Indicate that the currency is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the currency is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }



    /**
     * Create USD currency.
     */
    public function usd(): static
    {
        return $this->state(fn (array $attributes) => [
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ]);
    }

    /**
     * Create EUR currency.
     */
    public function eur(): static
    {
        return $this->state(fn (array $attributes) => [
            'code' => 'EUR',
            'symbol' => '€',
            'name' => 'Euro',
            'decimal_digits' => 2,
            'decimal_separator' => ',',
            'thousands_separator' => '.',
            'status' => 'active',
        ]);
    }

    /**
     * Create VND currency.
     */
    public function vnd(): static
    {
        return $this->state(fn (array $attributes) => [
            'code' => 'VND',
            'symbol' => '₫',
            'name' => 'Vietnamese Dong',
            'decimal_digits' => 0,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ]);
    }

    /**
     * Create JPY currency.
     */
    public function jpy(): static
    {
        return $this->state(fn (array $attributes) => [
            'code' => 'JPY',
            'symbol' => '¥',
            'name' => 'Japanese Yen',
            'decimal_digits' => 0,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ]);
    }
}
