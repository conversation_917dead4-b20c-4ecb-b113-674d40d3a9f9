<?php

namespace Modules\Language\Tests\Feature;

use Tests\TestCase;
use Modules\Language\Models\Language;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;

class AuthLanguageControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Language module
        $this->artisan('migrate', ['--path' => 'Modules/Language/database/migrations']);
    }

    #[Test]
    public function it_can_list_languages_with_pagination()
    {
        // Create test languages
        Language::factory()->count(15)->create();

        $response = $this->getJson('/api/v1/auth/languages?limit=10');

        $response->assertStatus(200);
        $this->assertPaginatedResponseStructure($response);

        $this->assertCount(10, $response->json('data'));
        $this->assertEquals(15, $response->json('total'));
        $this->assertEquals(10, $response->json('limit'));
    }

    #[Test]
    public function it_can_filter_languages_by_status()
    {
        Language::factory()->create(['status' => 'active', 'name' => 'English']);
        Language::factory()->create(['status' => 'active', 'name' => 'Spanish']);
        Language::factory()->create(['status' => 'inactive', 'name' => 'French']);

        // Filter by active status
        $response = $this->getJson('/api/v1/auth/languages?status=active');

        $response->assertStatus(200);
        $this->assertPaginatedResponseStructure($response);

        $data = $response->json('data');
        $this->assertCount(2, $data);
        $this->assertTrue(collect($data)->every(fn($lang) => $lang['status'] === 'active'));
    }

    #[Test]
    public function it_can_filter_languages_by_code()
    {
        Language::factory()->create(['code' => 'en', 'name' => 'English']);
        Language::factory()->create(['code' => 'es', 'name' => 'Spanish']);
        Language::factory()->create(['code' => 'fr', 'name' => 'French']);

        $response = $this->getJson('/api/v1/auth/languages?code=en');

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals('en', $data[0]['code']);
    }

    #[Test]
    public function it_can_filter_languages_by_name()
    {
        Language::factory()->create(['name' => 'English']);
        Language::factory()->create(['name' => 'Spanish']);
        Language::factory()->create(['name' => 'French']);

        $response = $this->getJson('/api/v1/auth/languages?name=Eng');

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertStringContainsString('English', $data[0]['name']);
    }

    #[Test]
    public function it_can_create_a_language()
    {
        $languageData = [
            'code' => 'en',
            'name' => 'English',
            'native_name' => 'English',
            'flag' => 'us',
            'direction' => 'ltr',
            'is_default' => 1,
            'status' => 'active'
        ];

        $response = $this->postJson('/api/v1/auth/languages', $languageData);

        $response->assertStatus(201);
        $this->assertJsonResponseStructure($response);

        $this->assertDatabaseHas('languages', $languageData);

        $data = $response->json('data');
        $this->assertEquals('en', $data['code']);
        $this->assertEquals('English', $data['name']);
        $this->assertEquals(1, $data['is_default']);
    }

    #[Test]
    public function it_validates_required_fields_when_creating()
    {
        $response = $this->postJson('/api/v1/auth/languages', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['code', 'name', 'direction', 'is_default', 'status']);
    }

    #[Test]
    public function it_validates_unique_code_when_creating()
    {
        Language::factory()->create(['code' => 'en']);

        $languageData = [
            'code' => 'en', // Duplicate code
            'name' => 'English',
            'direction' => 'ltr',
            'is_default' => 0,
            'status' => 'active'
        ];

        $response = $this->postJson('/api/v1/auth/languages', $languageData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['code']);
    }

    #[Test]
    public function it_can_show_a_language()
    {
        $language = Language::factory()->create([
            'code' => 'en',
            'name' => 'English',
            'native_name' => 'English'
        ]);

        $response = $this->getJson("/api/v1/auth/languages/{$language->id}");

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $data = $response->json('data');
        $this->assertEquals($language->id, $data['id']);
        $this->assertEquals('en', $data['code']);
        $this->assertEquals('English', $data['name']);
    }

    #[Test]
    public function it_returns_404_for_nonexistent_language()
    {
        $response = $this->getJson('/api/v1/auth/languages/999');

        $response->assertStatus(404);
    }

    #[Test]
    public function it_can_update_a_language()
    {
        $language = Language::factory()->create([
            'code' => 'en',
            'name' => 'English',
            'is_default' => 0
        ]);

        $updateData = [
            'code' => 'en-US',
            'name' => 'English (United States)',
            'native_name' => 'English (United States)',
            'flag' => 'us',
            'direction' => 'ltr',
            'is_default' => 1,
            'status' => 'active'
        ];

        $response = $this->putJson("/api/v1/auth/languages/{$language->id}", $updateData);

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $this->assertDatabaseHas('languages', array_merge(['id' => $language->id], $updateData));

        $data = $response->json('data');
        $this->assertEquals('en-US', $data['code']);
        $this->assertEquals('English (United States)', $data['name']);
        $this->assertEquals(1, $data['is_default']);
    }

    #[Test]
    public function it_can_update_language_without_changing_code()
    {
        $language = Language::factory()->create(['code' => 'en', 'name' => 'English']);

        $updateData = [
            'id' => $language->id,
            'code' => 'en', // Same code
            'name' => 'English (Updated)',
            'direction' => 'ltr',
            'is_default' => 0,
            'status' => 'active'
        ];

        $response = $this->putJson("/api/v1/auth/languages/{$language->id}", $updateData);

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertEquals('English (Updated)', $data['name']);
    }

    #[Test]
    public function it_enforces_only_one_default_language()
    {
        $language1 = Language::factory()->create(['is_default' => 1]);
        $language2 = Language::factory()->create(['is_default' => 0]);

        // Update second language to be default
        $updateData = [
            'id' => $language2->id,
            'code' => $language2->code,
            'name' => $language2->name,
            'direction' => 'ltr',
            'is_default' => 1,
            'status' => 'active'
        ];

        $response = $this->putJson("/api/v1/auth/languages/{$language2->id}", $updateData);

        $response->assertStatus(200);

        // First language should no longer be default
        $language1->refresh();
        $this->assertEquals(0, $language1->is_default);

        // Second language should now be default
        $language2->refresh();
        $this->assertEquals(1, $language2->is_default);
    }

    #[Test]
    public function it_can_soft_delete_a_language()
    {
        $language = Language::factory()->create();

        $response = $this->deleteJson("/api/v1/auth/languages/{$language->id}");

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $this->assertSoftDeleted('languages', ['id' => $language->id]);
    }

    #[Test]
    public function it_can_restore_a_soft_deleted_language()
    {
        $language = Language::factory()->create();
        $language->delete(); // Soft delete

        $response = $this->putJson("/api/v1/auth/languages/{$language->id}/restore");

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $this->assertDatabaseHas('languages', [
            'id' => $language->id,
            'deleted_at' => null
        ]);
    }

    #[Test]
    public function it_can_force_delete_a_language()
    {
        $language = Language::factory()->create();
        $language->delete(); // Soft delete

        $response = $this->deleteJson("/api/v1/auth/languages/{$language->id}/force-delete");

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $this->assertDatabaseMissing('languages', ['id' => $language->id]);
    }

    #[Test]
    public function it_can_bulk_destroy_languages()
    {
        $languages = Language::factory()->count(3)->create();
        $ids = $languages->pluck('id')->toArray();

        $response = $this->deleteJson('/api/v1/auth/languages/bulk-destroy', [
            'ids' => $ids
        ]);

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        foreach ($ids as $id) {
            $this->assertSoftDeleted('languages', ['id' => $id]);
        }
    }

    #[Test]
    public function it_can_bulk_restore_languages()
    {
        $languages = Language::factory()->count(3)->create();
        $ids = $languages->pluck('id')->toArray();

        // Soft delete all languages
        foreach ($languages as $language) {
            $language->delete();
        }

        $response = $this->putJson('/api/v1/auth/languages/bulk-restore', [
            'ids' => $ids
        ]);

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        foreach ($ids as $id) {
            $this->assertDatabaseHas('languages', [
                'id' => $id,
                'deleted_at' => null
            ]);
        }
    }

    #[Test]
    public function it_can_bulk_force_delete_languages()
    {
        $languages = Language::factory()->count(3)->create();
        $ids = $languages->pluck('id')->toArray();
        Language::whereIn('id', $ids)->delete(); // Soft delete

        $response = $this->deleteJson('/api/v1/auth/languages/bulk-delete', [
            'ids' => $ids
        ]);

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        foreach ($ids as $id) {
            $this->assertDatabaseMissing('languages', ['id' => $id]);
        }
    }

    #[Test]
    public function it_validates_bulk_operations_require_ids()
    {
        $response = $this->deleteJson('/api/v1/auth/languages/bulk-destroy', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['ids']);
    }

    #[Test]
    public function it_can_get_dropdown_data()
    {
        Language::factory()->create(['status' => 'active', 'name' => 'English']);
        Language::factory()->create(['status' => 'active', 'name' => 'Spanish']);
        Language::factory()->create(['status' => 'inactive', 'name' => 'French']);

        $response = $this->getJson('/api/v1/auth/languages/dropdown');

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $data = $response->json('data');
        $this->assertCount(2, $data); // Only active languages

        // Check structure for dropdown
        $this->assertArrayHasKey('code', $data[0]);
        $this->assertArrayHasKey('name', $data[0]);
    }
}
