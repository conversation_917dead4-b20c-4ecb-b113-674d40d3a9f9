<?php

namespace Modules\ModelAI\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseFormRequest;

/**
 * @property mixed $id
 * @property mixed $model_tool_id
 */
class ModelToolRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'key' => [
                'required',
                'string',
                'max:255',
                'alpha_dash',
                Rule::unique('model_tools')->ignore($this->id)->whereNull('deleted_at'),
            ],
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'type' => [
                'required',
                'string',
                Rule::in(['ai_native', 'custom', 'model']),
            ],
            'provider' => [
                'nullable',
                'string',
                'max:255',
            ],
            'version' => [
                'nullable',
                'string',
                'max:50',
            ],
            'api_endpoint' => [
                'nullable',
                'string',
                'url',
                'max:500',
            ],
            'configuration' => [
                'nullable',
                'array',
            ],
            'input_schema' => [
                'nullable',
                'array',
            ],
            'output_schema' => [
                'nullable',
                'array',
            ],
            'is_enabled' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'is_public' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'sort_order' => [
                'sometimes',
                'required',
                'integer',
                'min:1',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(['active', 'inactive', 'draft']),
            ],
            'categories' => [
                'nullable',
                'array',
            ],
            'categories.*' => [
                'integer',
                'exists:model_categories,id',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'key' => __('Tool Key'),
            'name' => __('Tool Name'),
            'description' => __('Description'),
            'type' => __('Tool Type'),
            'provider' => __('Provider'),
            'version' => __('Version'),
            'api_endpoint' => __('API Endpoint'),
            'configuration' => __('Configuration'),
            'input_schema' => __('Input Schema'),
            'output_schema' => __('Output Schema'),
            'is_enabled' => __('Is Enabled'),
            'is_public' => __('Is Public'),
            'sort_order' => __('Sort Order'),
            'status' => __('Status'),
            'categories' => __('Categories'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'key.alpha_dash' => __('The tool key may only contain letters, numbers, dashes and underscores.'),
            'api_endpoint.url' => __('The API endpoint must be a valid URL.'),
            'type.in' => __('The tool type must be one of: ai_native, custom, model.'),
            'status.in' => __('The status must be one of: active, inactive, draft.'),
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // If type is custom, api_endpoint should be provided
            if ($this->type === 'custom' && empty($this->api_endpoint)) {
                $validator->errors()->add('api_endpoint', __('API endpoint is required for custom tools.'));
            }

            // If type is ai_native, provider should be provided
            if ($this->type === 'ai_native' && empty($this->provider)) {
                $validator->errors()->add('provider', __('Provider is required for AI native tools.'));
            }
        });
    }
}
