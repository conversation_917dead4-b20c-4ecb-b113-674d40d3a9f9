<?php

namespace Modules\ModelAI\Observers;

use Illuminate\Support\Facades\Cache;
use Modules\ModelAI\Models\ModelCategory;

class ModelCategoryObserver
{
    /**
     * Cache tags for model categories
     */
    private const CACHE_TAGS = ['model-categories', 'model-ai'];

    /**
     * Handle the ModelCategory "created" event.
     */
    public function created(ModelCategory $modelCategory): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelCategory "updated" event.
     */
    public function updated(ModelCategory $modelCategory): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelCategory "deleted" event.
     */
    public function deleted(ModelCategory $modelCategory): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelCategory "restored" event.
     */
    public function restored(ModelCategory $modelCategory): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelCategory "force deleted" event.
     */
    public function forceDeleted(ModelCategory $modelCategory): void
    {
        $this->clearCache();
    }

    /**
     * Clear model category related cache.
     */
    private function clearCache(): void
    {
        try {
            if (function_exists('enabledCache') && enabledCache()) {
                Cache::tags(self::CACHE_TAGS)->flush();
            }
        } catch (\Exception $e) {
            // Silently handle cache errors
        }
    }
}
