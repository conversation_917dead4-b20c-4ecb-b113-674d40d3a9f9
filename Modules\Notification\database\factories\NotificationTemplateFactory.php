<?php

namespace Modules\Notification\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Notification\Models\NotificationTemplate;
use Modules\Notification\Models\NotificationType;

class NotificationTemplateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = NotificationTemplate::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $channels = ['database', 'email', 'sms', 'push', 'telegram'];
        $channel = $this->faker->randomElement($channels);
        
        return [
            'notification_type_id' => NotificationType::factory(),
            'channel' => $channel,
            'locale' => 'en',
            'subject' => $channel === 'email' ? $this->faker->sentence(4) : null,
            'title' => $this->faker->sentence(3),
            'content' => $this->generateContent($channel),
            'variables' => $this->generateVariables(),
            'settings' => $this->generateSettings($channel),
            'status' => 'active',
        ];
    }

    /**
     * Generate content based on channel type.
     */
    protected function generateContent(string $channel): string
    {
        switch ($channel) {
            case 'email':
                return "Dear {user_name},\n\n" . $this->faker->paragraphs(3, true) . "\n\nBest regards,\nThe {app_name} Team";
            case 'sms':
                return $this->faker->sentence(10);
            case 'database':
            case 'push':
                return $this->faker->sentence(15);
            case 'telegram':
                return $this->faker->paragraphs(2, true);
            default:
                return $this->faker->paragraph();
        }
    }

    /**
     * Generate variables array.
     */
    protected function generateVariables(): array
    {
        $baseVariables = ['app_name', 'user_name', 'user_email'];
        $additionalVariables = $this->faker->randomElements([
            'verification_url', 'reset_url', 'expires_in', 'alert_message',
            'alert_time', 'maintenance_date', 'start_time', 'end_time'
        ], $this->faker->numberBetween(0, 3));

        return array_merge($baseVariables, $additionalVariables);
    }

    /**
     * Generate settings based on channel.
     */
    protected function generateSettings(string $channel): array
    {
        $settings = [];

        if ($channel === 'email') {
            $settings = [
                'from_name' => '{app_name} Team',
                'reply_to' => '<EMAIL>',
                'priority' => $this->faker->randomElement(['low', 'normal', 'high']),
            ];
        } elseif ($channel === 'sms') {
            $settings = [
                'sender_id' => $this->faker->company,
            ];
        } elseif ($channel === 'push') {
            $settings = [
                'icon' => 'notification-icon.png',
                'sound' => 'default',
                'badge' => true,
            ];
        }

        return $settings;
    }

    /**
     * Indicate that the template is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Create an email template.
     */
    public function email(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'email',
            'subject' => $this->faker->sentence(4),
            'content' => "Dear {user_name},\n\n" . $this->faker->paragraphs(3, true) . "\n\nBest regards,\nThe {app_name} Team",
            'settings' => [
                'from_name' => '{app_name} Team',
                'reply_to' => '<EMAIL>',
                'priority' => 'normal',
            ],
        ]);
    }

    /**
     * Create a database template.
     */
    public function database(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'database',
            'subject' => null,
            'content' => $this->faker->sentence(15),
            'settings' => [],
        ]);
    }

    /**
     * Create an SMS template.
     */
    public function sms(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'sms',
            'subject' => null,
            'content' => $this->faker->sentence(10),
            'settings' => [
                'sender_id' => $this->faker->company,
            ],
        ]);
    }

    /**
     * Create a push notification template.
     */
    public function push(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'push',
            'subject' => null,
            'content' => $this->faker->sentence(12),
            'settings' => [
                'icon' => 'notification-icon.png',
                'sound' => 'default',
                'badge' => true,
            ],
        ]);
    }

    /**
     * Create a Telegram template.
     */
    public function telegram(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'telegram',
            'subject' => null,
            'content' => $this->faker->paragraphs(2, true),
            'settings' => [
                'parse_mode' => 'HTML',
                'disable_web_page_preview' => false,
            ],
        ]);
    }

    /**
     * Create template with specific locale.
     */
    public function locale(string $locale): static
    {
        return $this->state(fn (array $attributes) => [
            'locale' => $locale,
        ]);
    }

    /**
     * Create welcome email template.
     */
    public function welcomeEmail(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'email',
            'subject' => 'Welcome to {app_name}!',
            'title' => 'Welcome to {app_name}!',
            'content' => "Dear {user_name},\n\nWelcome to {app_name}! We're thrilled to have you join our community.\n\nYour account has been successfully created with the email address: {user_email}\n\nBest regards,\nThe {app_name} Team",
            'variables' => ['app_name', 'user_name', 'user_email'],
            'settings' => [
                'from_name' => '{app_name} Team',
            ],
        ]);
    }

    /**
     * Create password reset email template.
     */
    public function passwordResetEmail(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'email',
            'subject' => 'Reset Your Password',
            'title' => 'Password Reset Request',
            'content' => "Dear {user_name},\n\nYou have requested to reset your password for your {app_name} account.\n\nClick the link below to reset your password:\n{reset_url}\n\nThis link will expire in {expires_in} minutes.\n\nIf you did not request this password reset, please ignore this email.\n\nBest regards,\nThe {app_name} Team",
            'variables' => ['app_name', 'user_name', 'reset_url', 'expires_in'],
            'settings' => [
                'from_name' => '{app_name} Security',
            ],
        ]);
    }
}
