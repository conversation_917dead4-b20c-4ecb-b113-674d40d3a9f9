<?php

namespace Modules\Currency\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Currency\Models\Currency;
use PHPUnit\Framework\Attributes\Test;

class AuthCurrencyControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Currency module
        $this->artisan('migrate', ['--path' => 'Modules/Currency/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_can_list_currencies_with_pagination()
    {
        // Create currencies with unique codes
        $codes = ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'KRW', 'VND', 'THB', 'SGD', 'MYR', 'IDR', 'PHP', 'INR', 'AUD', 'CAD'];
        foreach ($codes as $code) {
            Currency::factory()->create(['code' => $code]);
        }

        $response = $this->getJson('/api/v1/auth/currencies');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'code',
                    'symbol',
                    'name',
                    'decimal_digits',
                    'decimal_separator',
                    'thousands_separator',
                    'status',
                    'sort_order',
                    'created_at',
                    'updated_at',
                ]
            ],
            'total',
            'limit',
        ]);

        $this->assertEquals(10, count($response->json('data'))); // Default pagination
        $this->assertEquals(15, $response->json('total'));
    }

    #[Test]
    public function it_can_list_currencies_with_custom_limit()
    {
        // Create currencies with unique codes
        $codes = ['USD', 'EUR', 'GBP', 'JPY', 'CNY'];
        foreach ($codes as $code) {
            Currency::factory()->create(['code' => $code]);
        }

        $response = $this->getJson('/api/v1/auth/currencies?limit=5');

        $response->assertStatus(200);
        $this->assertEquals(5, count($response->json('data')));
    }

    #[Test]
    public function it_can_filter_currencies_by_status()
    {
        Currency::factory()->create(['code' => 'USD', 'status' => 'active']);
        Currency::factory()->create(['code' => 'EUR', 'status' => 'active']);
        Currency::factory()->create(['code' => 'GBP', 'status' => 'inactive']);

        $response = $this->getJson('/api/v1/auth/currencies?status=active');

        $response->assertStatus(200);
        $this->assertEquals(2, count($response->json('data')));

        foreach ($response->json('data') as $currency) {
            $this->assertEquals('active', $currency['status']);
        }
    }

    #[Test]
    public function it_can_filter_currencies_by_code()
    {
        Currency::factory()->create(['code' => 'USD']);
        Currency::factory()->create(['code' => 'EUR']);
        Currency::factory()->create(['code' => 'GBP']);

        $response = $this->getJson('/api/v1/auth/currencies?code=USD');

        $response->assertStatus(200);
        $this->assertEquals(1, count($response->json('data')));
        $this->assertEquals('USD', $response->json('data.0.code'));
    }

    #[Test]
    public function it_can_show_a_currency()
    {
        $currency = Currency::factory()->create();

        $response = $this->getJson("/api/v1/auth/currencies/{$currency->id}");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'id',
                'code',
                'symbol',
                'name',
                'decimal_digits',
                'decimal_separator',
                'thousands_separator',
                'status',
                'sort_order',
                'created_at',
                'updated_at',
            ]
        ]);
        $this->assertEquals($currency->id, $response->json('data.id'));
    }

    #[Test]
    public function it_returns_404_when_showing_non_existent_currency()
    {
        $response = $this->getJson('/api/v1/auth/currencies/99999');

        $response->assertStatus(404);
    }

    #[Test]
    public function it_can_create_a_currency()
    {
        $currencyData = [
            'code' => 'GBP',
            'symbol' => '£',
            'name' => 'British Pound',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
            'sort_order' => 3,
        ];

        $response = $this->postJson('/api/v1/auth/currencies', $currencyData);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'id',
                'code',
                'symbol',
                'name',
                'decimal_digits',
                'decimal_separator',
                'thousands_separator',
                'status',
                'sort_order',
                'created_at',
                'updated_at',
            ]
        ]);

        $this->assertDatabaseHas('currencies', [
            'code' => 'GBP',
            'symbol' => '£',
            'name' => 'British Pound',
            'status' => 'active',
        ]);
    }

    #[Test]
    public function it_validates_required_fields_when_creating()
    {
        $response = $this->postJson('/api/v1/auth/currencies', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'code',
            'symbol',
            'name',
            'decimal_digits',
            'decimal_separator',
            'thousands_separator',
            'status',
        ]);
    }

    #[Test]
    public function it_validates_code_format_when_creating()
    {
        $currencyData = [
            'code' => 'us', // Invalid: lowercase and too short
            'symbol' => '$',
            'name' => 'US Dollar',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ];

        $response = $this->postJson('/api/v1/auth/currencies', $currencyData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['code']);
    }

    #[Test]
    public function it_validates_code_uniqueness_when_creating()
    {
        Currency::factory()->create(['code' => 'USD']);

        $currencyData = [
            'code' => 'USD', // Duplicate code
            'symbol' => '$',
            'name' => 'US Dollar',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ];

        $response = $this->postJson('/api/v1/auth/currencies', $currencyData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['code']);
    }

    #[Test]
    public function it_can_update_a_currency()
    {
        $currency = Currency::factory()->create([
            'code' => 'USD',
            'name' => 'US Dollar',
            'status' => 'active',
        ]);

        $updateData = [
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'United States Dollar',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'inactive',
            'sort_order' => 5,
        ];

        $response = $this->putJson("/api/v1/auth/currencies/{$currency->id}", $updateData);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'id',
                'code',
                'symbol',
                'name',
                'status',
            ]
        ]);

        $this->assertDatabaseHas('currencies', [
            'id' => $currency->id,
            'name' => 'United States Dollar',
            'status' => 'inactive',
        ]);
    }

    #[Test]
    public function it_returns_404_when_updating_non_existent_currency()
    {
        $updateData = [
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ];

        $response = $this->putJson('/api/v1/auth/currencies/99999', $updateData);

        $response->assertStatus(404);
    }

    #[Test]
    public function it_can_delete_a_currency()
    {
        $currency = Currency::factory()->create();

        $response = $this->deleteJson("/api/v1/auth/currencies/{$currency->id}");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
        ]);

        $this->assertDatabaseMissing('currencies', [
            'id' => $currency->id,
        ]);
    }

    #[Test]
    public function it_returns_404_when_deleting_non_existent_currency()
    {
        $response = $this->deleteJson('/api/v1/auth/currencies/99999');

        $response->assertStatus(404);
    }

    #[Test]
    public function it_can_bulk_delete_currencies()
    {
        $currency1 = Currency::factory()->create();
        $currency2 = Currency::factory()->create();
        $currency3 = Currency::factory()->create();

        $response = $this->deleteJson('/api/v1/auth/currencies/bulk-destroy', [
            'ids' => [$currency1->id, $currency2->id],
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'deleted_count',
            ]
        ]);

        $this->assertEquals(2, $response->json('data.deleted_count'));

        $this->assertDatabaseMissing('currencies', ['id' => $currency1->id]);
        $this->assertDatabaseMissing('currencies', ['id' => $currency2->id]);
        $this->assertDatabaseHas('currencies', ['id' => $currency3->id]);
    }

    #[Test]
    public function it_validates_bulk_delete_request()
    {
        $response = $this->deleteJson('/api/v1/auth/currencies/bulk-destroy', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['ids']);
    }

    #[Test]
    public function it_validates_bulk_delete_ids_exist()
    {
        $currency = Currency::factory()->create();

        $response = $this->deleteJson('/api/v1/auth/currencies/bulk-destroy', [
            'ids' => [$currency->id, 99999], // 99999 doesn't exist
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['ids.1']);
    }

    #[Test]
    public function it_can_get_currencies_dropdown()
    {
        Currency::factory()->create(['status' => 'active', 'code' => 'USD', 'name' => 'US Dollar']);
        Currency::factory()->create(['status' => 'active', 'code' => 'EUR', 'name' => 'Euro']);
        Currency::factory()->create(['status' => 'inactive', 'code' => 'GBP', 'name' => 'British Pound']);

        $response = $this->getJson('/api/v1/auth/currencies/dropdown');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'code',
                    'symbol',
                    'name',
                    'decimal_digits',
                    'decimal_separator',
                    'thousands_separator',
                    'sort_order',
                ]
            ]
        ]);

        // Should only return active currencies
        $this->assertEquals(2, count($response->json('data')));
        
        // Should not include id, created_at, updated_at, status
        foreach ($response->json('data') as $currency) {
            $this->assertArrayNotHasKey('id', $currency);
            $this->assertArrayNotHasKey('created_at', $currency);
            $this->assertArrayNotHasKey('updated_at', $currency);
            $this->assertArrayNotHasKey('status', $currency);
        }
    }

    #[Test]
    public function it_returns_consistent_response_structure()
    {
        $currency = Currency::factory()->create([
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'status' => 'active',
        ]);

        $response = $this->getJson("/api/v1/auth/currencies/{$currency->id}");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'id',
                'code',
                'symbol',
                'name',
                'decimal_digits',
                'decimal_separator',
                'thousands_separator',
                'status',
                'sort_order',
                'created_at',
                'updated_at',
            ]
        ]);

        $this->assertTrue($response->json('success'));
        $this->assertIsString($response->json('message'));
    }
}
