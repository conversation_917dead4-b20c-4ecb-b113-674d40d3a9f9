<?php

namespace Modules\ModelAI\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Modules\Core\Traits\ResponseTrait;
use Modules\ModelAI\Facades\ModelAIFacade;
use Modules\ModelAI\Models\ModelTool;

class ModelToolController extends Controller
{
    use ResponseTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $tools = ModelTool::query()
            ->with(['categories'])
            ->where('is_public', true)
            ->active()
            ->enabled()
            ->ordered()
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($tools, __('Public tools retrieved successfully.'));
    }

    /**
     * Display the specified resource.
     */
    public function show(ModelTool $modelTool): JsonResponse
    {
        if (!$modelTool->is_public || !$modelTool->is_enabled || !$modelTool->isActive()) {
            return $this->errorResponse(__('Tool not found or not accessible.'), 404);
        }

        $modelTool->load(['categories']);

        return $this->successResponse($modelTool, __('Tool retrieved successfully.'));
    }

    /**
     * Get tools for dropdown.
     */
    public function toolsDropdown(Request $request): JsonResponse
    {
        $locale = $request->input('locale', App::getLocale());
        $tools = ModelAIFacade::getToolsForDropdown($locale);

        return $this->successResponse($tools, __('Tools retrieved successfully.'));
    }

    /**
     * Get categories with their associated tools.
     */
    public function categoriesWithTools(Request $request): JsonResponse
    {
        $locale = $request->input('locale', App::getLocale());
        $data = ModelAIFacade::getCategoriesWithTools($locale);

        return $this->successResponse($data, __('Categories with tools retrieved successfully.'));
    }

    /**
     * Get AI models with their associated tools.
     */
    public function modelsWithTools(Request $request): JsonResponse
    {
        $locale = $request->input('locale', App::getLocale());
        $data = ModelAIFacade::getModelsWithTools($locale);

        return $this->successResponse($data, __('Models with tools retrieved successfully.'));
    }

    /**
     * Get tools for a specific model.
     */
    public function modelTools(Request $request, string $modelKey): JsonResponse
    {
        $model = \Modules\ModelAI\Models\ModelAI::where('key', $modelKey)
                    ->where('status', 'active')
                    ->first();

        if (!$model) {
            return $this->errorResponse(__('Model not found.'), 404);
        }

        $tools = $model->getAllAvailableTools();

        return $this->successResponse($tools, __('Model tools retrieved successfully.'));
    }
}
