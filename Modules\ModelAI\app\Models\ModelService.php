<?php

namespace Modules\ModelAI\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\ModelAI\Database\Factories\ModelServiceFactory;

class ModelService extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'model_ai_id',
        'input_types',
        'output_types',
        'supported_sources',
        'cost_per_request',
        'cost_per1k_tokens',
        'billing_type',
        'cost_per1k_input',
        'cost_per1k_output',
        'max_tokens',
        'context_window',
        'rate_limit_rpm',
        'timeout_seconds',
        'default_parameters',
        'allowed_parameters',
        'priority',
        'notes',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'model_ai_id' => 'integer',
        'input_types' => 'array',
        'output_types' => 'array',
        'supported_sources' => 'array',
        'cost_per_request' => 'integer',
        'cost_per1k_tokens' => 'decimal:4',
        'cost_per1k_input' => 'decimal:6',
        'cost_per1k_output' => 'decimal:6',
        'max_tokens' => 'integer',
        'context_window' => 'integer',
        'rate_limit_rpm' => 'integer',
        'timeout_seconds' => 'integer',
        'default_parameters' => 'array',
        'allowed_parameters' => 'array',
        'priority' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = ['deleted_at'];

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        // Validate parameter consistency before saving
        static::saving(function ($modelService) {
            $allowedParams = $modelService->allowed_parameters ?? [];
            $defaultParams = $modelService->default_parameters ?? [];

            // Allow null or empty array - some models don't allow parameter customization
            if (!empty($allowedParams) && !empty($defaultParams)) {
                $defaultKeys = array_keys($defaultParams);
                $missingDefaults = array_diff($allowedParams, $defaultKeys);

                if (!empty($missingDefaults)) {
                    throw new \InvalidArgumentException(
                        'Parameter validation failed: The following allowed parameters are missing from default_parameters: ' .
                        implode(', ', $missingDefaults)
                    );
                }
            }
        });
    }

    /**
     * Scope a query to only include active services.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive services.
     */
    public function scopeInactive(Builder $query): Builder
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by billing type.
     */
    public function scopeBillingType(Builder $query, string $billingType): Builder
    {
        return $query->where('billing_type', $billingType);
    }

    /**
     * Scope a query to filter by billing type (alias).
     */
    public function scopeByBillingType(Builder $query, string $billingType): Builder
    {
        return $this->scopeBillingType($query, $billingType);
    }

    /**
     * Scope a query to order services.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('priority', 'desc')->orderBy('created_at', 'desc');
    }

    /**
     * Scope a query to order by priority.
     */
    public function scopeByPriority(Builder $query): Builder
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Get the AI model that owns the service.
     */
    public function modelAI(): BelongsTo
    {
        return $this->belongsTo(ModelAI::class, 'model_ai_id');
    }

    /**
     * Check if service is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if service is draft.
     */
    public function isDraft(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Check if service is inactive.
     */
    public function isInactive(): bool
    {
        return $this->status === 'inactive';
    }

    /**
     * Check if service uses per-request billing.
     */
    public function isPerRequestBilling(): bool
    {
        return $this->billing_type === 'per_request';
    }

    /**
     * Check if service uses per-token billing.
     */
    public function isPerTokenBilling(): bool
    {
        return $this->billing_type === 'per_token';
    }

    /**
     * Check if service uses hybrid billing.
     */
    public function isHybridBilling(): bool
    {
        return $this->billing_type === 'hybrid';
    }

    /**
     * Check if service supports input type.
     */
    public function supportsInputType(string $type): bool
    {
        return in_array($type, $this->input_types ?? []);
    }

    /**
     * Check if service supports output type.
     */
    public function supportsOutputType(string $type): bool
    {
        return in_array($type, $this->output_types ?? []);
    }

    /**
     * Get cost per request.
     */
    public function getCostPerRequest(): int
    {
        return $this->cost_per_request;
    }

    /**
     * Get cost per 1k tokens.
     */
    public function getCostPer1kTokens(): float
    {
        return $this->cost_per1k_tokens;
    }

    /**
     * Calculate cost for given token count.
     */
    public function calculateTokenCost(int $tokens): float
    {
        return ($tokens / 1000) * $this->cost_per1k_tokens;
    }

    /**
     * Calculate cost for a request.
     */
    public function calculateRequestCost(): int
    {
        return $this->cost_per_request;
    }

    /**
     * Get max tokens.
     */
    public function getMaxTokens(): ?int
    {
        return $this->max_tokens;
    }

    /**
     * Get context window.
     */
    public function getContextWindow(): ?int
    {
        return $this->context_window;
    }

    /**
     * Get rate limit.
     */
    public function getRateLimit(): ?int
    {
        return $this->rate_limit_rpm;
    }

    /**
     * Get timeout seconds.
     */
    public function getTimeoutSeconds(): ?int
    {
        return $this->timeout_seconds;
    }

    /**
     * Get default parameters.
     */
    public function getDefaultParameters(): array
    {
        return $this->default_parameters ?? [];
    }

    /**
     * Get allowed parameters.
     */
    public function getAllowedParameters(): array
    {
        return $this->allowed_parameters ?? [];
    }

    /**
     * Check if parameter is allowed.
     */
    public function isParameterAllowed(string $parameter): bool
    {
        return in_array($parameter, $this->allowed_parameters ?? []);
    }

    /**
     * Get priority.
     */
    public function getPriority(): ?int
    {
        return $this->priority;
    }

    /**
     * Get billing type.
     */
    public function getBillingType(): string
    {
        return $this->billing_type;
    }

    /**
     * Check if billing is token-based.
     */
    public function isTokenBased(): bool
    {
        return $this->billing_type === 'per_token';
    }

    /**
     * Check if billing is request-based.
     */
    public function isRequestBased(): bool
    {
        return $this->billing_type === 'per_request';
    }

    /**
     * Get cost for a request based on billing type.
     */
    public function calculateCost(int $tokens = 0): float
    {
        return match ($this->billing_type) {
            'per_request' => $this->cost_per_request,
            'per_token' => ($tokens / 1000) * $this->cost_per1k_tokens,
            'hybrid' => $this->cost_per_request + (($tokens / 1000) * $this->cost_per1k_tokens),
            default => 0,
        };
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): ModelServiceFactory
    {
        return ModelServiceFactory::new();
    }
}
