<?php

namespace Modules\ModelAI\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\ModelAI\Models\ModelProvider;

class ModelProviderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = ModelProvider::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $providers = [
            'openai' => [
                'name' => 'OpenAI',
                'base_url' => 'https://api.openai.com/v1/',
                'description' => 'OpenAI GPT models and services',
            ],
            'anthropic' => [
                'name' => 'Anthropic',
                'base_url' => 'https://api.anthropic.com/',
                'description' => 'Anthropic Claude models',
            ],
            'google' => [
                'name' => 'Google AI',
                'base_url' => 'https://generativelanguage.googleapis.com/v1/',
                'description' => 'Google Gemini and PaLM models',
            ],
            'cohere' => [
                'name' => 'Cohere',
                'base_url' => 'https://api.cohere.ai/v1/',
                'description' => 'Cohere language models',
            ],
            'huggingface' => [
                'name' => 'Hugging Face',
                'base_url' => 'https://api-inference.huggingface.co/',
                'description' => 'Hugging Face model inference API',
            ],
        ];

        $providerKey = $this->faker->randomElement(array_keys($providers));
        $providerData = $providers[$providerKey];

        return [
            'key' => $providerKey,
            'name' => $providerData['name'],
            'description' => $providerData['description'],
            'base_url' => $providerData['base_url'],
            'api_key' => 'sk-' . $this->faker->regexify('[A-Za-z0-9]{48}'),
            'credentials' => [
                'organization_id' => $this->faker->optional()->regexify('org-[A-Za-z0-9]{24}'),
            ],
            'status' => $this->faker->randomElement(['active', 'inactive']),
        ];
    }

    /**
     * Indicate that the provider is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the provider is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Create an OpenAI provider.
     */
    public function openai(): static
    {
        return $this->state(fn (array $attributes) => [
            'key' => 'openai',
            'name' => 'OpenAI',
            'description' => 'OpenAI GPT models and services',
            'base_url' => 'https://api.openai.com/v1/',
            'api_key' => 'sk-' . $this->faker->regexify('[A-Za-z0-9]{48}'),
            'credentials' => [
                'organization_id' => 'org-' . $this->faker->regexify('[A-Za-z0-9]{24}'),
            ],
        ]);
    }

    /**
     * Create an Anthropic provider.
     */
    public function anthropic(): static
    {
        return $this->state(fn (array $attributes) => [
            'key' => 'anthropic',
            'name' => 'Anthropic',
            'description' => 'Anthropic Claude models',
            'base_url' => 'https://api.anthropic.com/',
            'api_key' => 'sk-ant-' . $this->faker->regexify('[A-Za-z0-9]{40}'),
            'credentials' => [],
        ]);
    }

    /**
     * Create a Google AI provider.
     */
    public function google(): static
    {
        return $this->state(fn (array $attributes) => [
            'key' => 'google',
            'name' => 'Google AI',
            'description' => 'Google Gemini and PaLM models',
            'base_url' => 'https://generativelanguage.googleapis.com/v1/',
            'api_key' => 'AIza' . $this->faker->regexify('[A-Za-z0-9]{35}'),
            'credentials' => [],
        ]);
    }

    /**
     * Create a provider without credentials.
     */
    public function withoutCredentials(): static
    {
        return $this->state(fn (array $attributes) => [
            'credentials' => null,
        ]);
    }

    /**
     * Create a provider with custom credentials.
     */
    public function withCredentials(array $credentials): static
    {
        return $this->state(fn (array $attributes) => [
            'credentials' => $credentials,
        ]);
    }
}
