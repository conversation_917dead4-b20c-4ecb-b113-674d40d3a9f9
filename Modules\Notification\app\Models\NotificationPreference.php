<?php

namespace Modules\Notification\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Carbon\Carbon;
use Modules\Notification\Database\Factories\NotificationPreferenceFactory;

class NotificationPreference extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'notifiable_type',
        'notifiable_id',
        'notification_type_id',
        'channel',
        'enabled',
        'settings',
        'quiet_hours_start',
        'quiet_hours_end',
        'timezone',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'enabled' => 'boolean',
        'settings' => 'array',
        'quiet_hours_start' => 'datetime:H:i',
        'quiet_hours_end' => 'datetime:H:i',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope a query to only include enabled preferences.
     */
    public function scopeEnabled(Builder $query): Builder
    {
        return $query->where('enabled', true);
    }

    /**
     * Scope a query to only include disabled preferences.
     */
    public function scopeDisabled(Builder $query): Builder
    {
        return $query->where('enabled', false);
    }

    /**
     * Scope a query to filter by channel.
     */
    public function scopeByChannel(Builder $query, string $channel): Builder
    {
        return $query->where('channel', $channel);
    }

    /**
     * Scope a query to filter by notification type.
     */
    public function scopeByType(Builder $query, int $typeId): Builder
    {
        return $query->where('notification_type_id', $typeId);
    }

    /**
     * Scope a query to filter by notifiable.
     */
    public function scopeForNotifiable(Builder $query, $notifiable): Builder
    {
        return $query->where('notifiable_type', get_class($notifiable))
                    ->where('notifiable_id', $notifiable->id);
    }

    /**
     * Get the owning notifiable model.
     */
    public function notifiable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the notification type that owns this preference.
     */
    public function notificationType(): BelongsTo
    {
        return $this->belongsTo(NotificationType::class);
    }

    /**
     * Check if current time is within quiet hours.
     */
    public function isWithinQuietHours(?Carbon $time = null): bool
    {
        if (!$this->quiet_hours_start || !$this->quiet_hours_end) {
            return false;
        }

        $time = $time ?? now();

        // Convert to user's timezone if specified
        if ($this->timezone) {
            $time = $time->setTimezone($this->timezone);
        }

        $start = Carbon::createFromFormat('H:i', $this->quiet_hours_start->format('H:i'));
        $end = Carbon::createFromFormat('H:i', $this->quiet_hours_end->format('H:i'));
        $current = Carbon::createFromFormat('H:i', $time->format('H:i'));

        // Handle overnight quiet hours (e.g., 22:00 to 06:00)
        if ($start->gt($end)) {
            return $current->gte($start) || $current->lte($end);
        }

        return $current->between($start, $end);
    }

    /**
     * Check if notifications should be sent based on preferences.
     */
    public function shouldSend(?Carbon $time = null): bool
    {
        if (!$this->enabled) {
            return false;
        }

        return !$this->isWithinQuietHours($time);
    }

    /**
     * Get preference settings as array.
     */
    public function getSettings(): array
    {
        return $this->settings ?? [];
    }

    /**
     * Get a specific setting value.
     */
    public function getSetting(string $key, $default = null)
    {
        return data_get($this->getSettings(), $key, $default);
    }

    /**
     * Set a specific setting value.
     */
    public function setSetting(string $key, $value): void
    {
        $settings = $this->getSettings();
        data_set($settings, $key, $value);
        $this->settings = $settings;
    }

    /**
     * Get or create preference for notifiable and type/channel.
     */
    public static function getOrCreateFor($notifiable, int $typeId, string $channel): self
    {
        return static::firstOrCreate([
            'notifiable_type' => get_class($notifiable),
            'notifiable_id' => $notifiable->id,
            'notification_type_id' => $typeId,
            'channel' => $channel,
        ], [
            'enabled' => true,
        ]);
    }

    /**
     * Bulk update preferences for a notifiable.
     */
    public static function updatePreferencesFor($notifiable, array $preferences): void
    {
        foreach ($preferences as $preference) {
            static::updateOrCreate([
                'notifiable_type' => get_class($notifiable),
                'notifiable_id' => $notifiable->id,
                'notification_type_id' => $preference['notification_type_id'],
                'channel' => $preference['channel'],
            ], $preference);
        }
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): NotificationPreferenceFactory
    {
        return NotificationPreferenceFactory::new();
    }
}
