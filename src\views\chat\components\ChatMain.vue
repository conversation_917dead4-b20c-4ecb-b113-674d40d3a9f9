<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted, watch, computed } from "vue";
import { Promotion } from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";
import MarkdownRenderer from "@/views/chat/components/MarkdownRenderer.vue";
import { useUserStore } from "@/store/modules/user";

interface Props {
  mainView?: string;
  chatBot: any;
}

const props = defineProps<Props>();

// Destructure only used properties from chatBot
const {
  chatMessagesContainer,
  newMessage,
  isTyping,
  attachedFiles,
  currentMessages,
  sendMessage,
  sendStarterPrompt,
  removeAttachment,
  selectedConversation,
  agents,
  selectedAgent,
  currentConversationId
} = props.chatBot;

// ===== LOCAL STATE =====
const clientHeight = ref(400);
const agent = ref();

// ===== COMPUTED PROPERTIES =====
const user = computed(() => {
  return useUserStore().userInfo;
});

// ===== EVENT HANDLERS =====
const handleEnter = (event: KeyboardEvent) => {
  if (!event.shiftKey) {
    sendMessage();
  }
};

// ===== UTILITY FUNCTIONS =====
const updateChatContainerHeight = () => {
  if (chatMessagesContainer.value) {
    const header = document.querySelector(".chat-header");
    const footer = document.querySelector(".chat-footer");
    const headerHeight = header ? header.getBoundingClientRect().height : 81;
    const footerHeight = footer ? footer.getBoundingClientRect().height : 125;
    const parentHeight = (chatMessagesContainer.value as HTMLElement)
      .parentElement.clientHeight;
    const newHeight = parentHeight - headerHeight - footerHeight;
    clientHeight.value = newHeight > 0 ? newHeight : 400;
  }
};

// ===== LIFECYCLE HOOKS =====
onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      updateChatContainerHeight();
    }, 1000);
  });
  window.addEventListener("resize", updateChatContainerHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateChatContainerHeight);
});

watch(selectedAgent, value => {
  agent.value = agents.value.find((item: any) => item.uuid == value);
});
</script>

<template>
  <div class="chat-area w-full h-full relative bg-white">
    <header class="chat-header flex items-center justify-between">
      <div class="flex items-center">
        <img
          :src="selectedConversation.bot?.logoUrl || '/bots/default.png'"
          alt="logo"
          class="w-10 h-10 rounded-full mr-4 object-cover"
        />
        <div>
          <h2 class="text-lg font-semibold text-gray-900">
            {{ selectedConversation.title || $t("New Conversation") }}
          </h2>
          <div v-if="selectedConversation?.uuid" class="text-sm text-gray-500">
            {{ $t("Currently using") }}:
            <span class="font-medium text-gray-700">
              {{ selectedConversation.bot?.name }}
            </span>
          </div>
          <div v-else class="flex">
            <div
              v-if="
                currentConversationId &&
                !currentConversationId.includes('temp-')
              "
              class="text-sm text-gray-500"
            >
              {{ $t("Currently using") }}:
              <span class="font-medium text-gray-700">
                {{ agent.name }}
              </span>
            </div>
            <el-select
              v-else
              v-model="selectedAgent"
              size="small"
              :placeholder="$t('Select an agent')"
              :style="{ width: '360px' }"
            >
              <el-option
                v-for="agent in agents"
                :key="agent.uuid"
                :label="agent.name"
                :value="agent.uuid"
              >
                <el-popover
                  placement="right-start"
                  :width="350"
                  trigger="hover"
                  :show-after="500"
                  popper-class="!p-0"
                >
                  <template #reference>
                    <div>
                      {{ agent.name }}
                    </div>
                  </template>
                  <div class="p-4">
                    <div class="flex items-center mb-4">
                      <img
                        :src="agent.logo"
                        class="w-12 h-12 rounded-full mr-4 object-cover flex-shrink-0"
                        alt="logo"
                      />
                      <div class="overflow-hidden">
                        <h4 class="font-bold text-lg text-gray-800 truncate">
                          {{ agent.name }}
                        </h4>
                        <el-tag
                          size="small"
                          :type="
                            agent.botType === 'personal' ? 'info' : 'success'
                          "
                        >
                          {{
                            agent.botType === "personal" ? "Cá nhân" : "Team"
                          }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="text-sm text-gray-600 mb-4 text-justify">
                      <div v-html="agent.description" />
                    </div>
                    <div
                      class="bg-emerald-200 p-3 rounded-lg border border-slate-200 text-xs space-y-2"
                    >
                      <div class="flex justify-between">
                        <span class="text-gray-500">Tác giả:</span>
                        <span class="font-medium text-gray-700">
                          {{ agent.owner?.name || agent.owner?.fullName }}
                        </span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-gray-500"> Phiên bản: </span>
                        <span class="font-medium text-gray-700">
                          {{ agent.version || "1.0.0" }}
                        </span>
                      </div>
                    </div>
                    <div
                      v-if="
                        agent.metadata?.tags && agent.metadata?.tags?.length > 0
                      "
                      class="mt-3"
                    >
                      <h5 class="text-xs font-semibold text-gray-500 mb-2">
                        Thẻ:
                      </h5>
                      <div class="flex flex-wrap gap-2">
                        <el-tag
                          v-for="tag in agent.metadata.tags"
                          :key="tag"
                          size="small"
                          effect="plain"
                        >
                          {{ tag }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </el-popover>
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
    </header>
    <div
      ref="chatMessagesContainer"
      class="chat-messages p-6 overflow-y-auto space-y-6"
      :style="{ height: clientHeight + 'px', maxHeight: clientHeight + 'px' }"
    >
      <template v-if="!currentConversationId">
        <div class="flex flex-col h-full justify-center items-center">
          <div class="text-5xl font-extrabold text-center">
            <span
              class="bg-gradient-to-r from-blue-500 to-violet-500 text-transparent bg-clip-text"
            >
              {{ $t("Hello") }} {{ user.nickname }}!
            </span>
          </div>
        </div>
      </template>
      <template v-else>
        <div
          v-for="message in currentMessages"
          :key="message.id"
          class="message flex"
          :class="message.role === 'user' ? 'justify-end' : 'justify-start'"
        >
          <div
            class="flex items-end max-w-lg"
            :class="{ 'flex-row-reverse': message.role === 'user' }"
          >
            <img
              :src="
                message.role === 'user'
                  ? 'https://placehold.co/40x40/E2E8F0/4A5568?text=U'
                  : selectedConversation.bot?.logoUrl
              "
              alt="logo"
              class="w-8 h-8 rounded-full object-cover"
              :class="message.role === 'user' ? 'ml-3' : 'mr-3'"
            />
            <div
              class="p-3 rounded-2xl shadow-sm"
              :class="{
                'bg-blue-500 text-white rounded-br-none':
                  message.role === 'user'
              }"
            >
              <MarkdownRenderer
                v-if="message.role === 'assistant'"
                class="bg-white text-gray-800 rounded-bl-none"
                :content="message.content"
              />
              <p v-else class="text-sm whitespace-pre-wrap">
                {{ message.content }}
              </p>
            </div>
          </div>
        </div>
        <div v-if="isTyping" class="flex justify-start">
          <div class="flex items-end max-w-lg">
            <img
              :src="selectedConversation.bot?.logoUrl"
              class="w-8 h-8 rounded-full object-cover mr-3"
              alt="logo"
            />
            <div
              class="p-3 rounded-2xl shadow-sm bg-white text-gray-800 rounded-bl-none"
            >
              <div class="flex items-center space-x-1 typing-indicator">
                <span
                  class="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
                  style="animation-delay: -0.3s"
                />
                <span
                  class="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
                  style="animation-delay: -0.15s"
                />
                <span class="h-2 w-2 bg-gray-400 rounded-full animate-bounce" />
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="selectedAgent && currentMessages.length <= 1"
          class="flex flex-col items-center justify-center pt-4"
        >
          <!-- Starter prompts -->
          <div class="flex flex-wrap justify-center gap-2 max-w-lg">
            <el-button
              v-for="(prompt, index) in agent.starterMessages"
              :key="`prompt-${index}`"
              round
              @click="sendStarterPrompt(prompt)"
            >
              {{ prompt }}
            </el-button>
          </div>
        </div>
      </template>
    </div>
    <footer class="chat-footer !absolute bottom-0 left-0 right-0">
      <div v-if="attachedFiles.length > 0" class="mb-2 flex flex-wrap gap-2">
        <el-tag
          v-for="(file, index) in attachedFiles"
          :key="file.uid"
          closable
          @close="removeAttachment(index)"
        >
          {{ file.name }}
        </el-tag>
      </div>
      <div class="composer-wrapper">
        <el-input
          v-model="newMessage"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 6 }"
          :placeholder="$t('Enter message...')"
          class="composer-textarea"
          :disabled="
            isTyping || (!selectedAgent && !selectedConversation?.uuid)
          "
          @keyup.enter.prevent="handleEnter"
        />
        <div class="flex justify-between items-center mt-2">
          <div class="flex items-center gap-2">
            <!-- File upload (hidden for now) -->
          </div>
          <el-button
            type="primary"
            :icon="Promotion"
            circle
            :disabled="
              (!newMessage.trim() && attachedFiles.length === 0) ||
              isTyping ||
              (!selectedAgent && !selectedConversation?.uuid)
            "
            @click="sendMessage"
          />
        </div>
      </div>
    </footer>
  </div>
</template>

<style lang="scss">
.chat-area {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.chat-header {
  flex-shrink: 0;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem;
  min-height: auto;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  max-height: 400px;
  scroll-behavior: smooth;

  // Custom scrollbar
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #94a3b8;
  }
}

.chat-footer {
  flex-shrink: 0;
  background: white;
  border-top: 1px solid #e2e8f0;
  padding: 1rem;
}

.composer-wrapper {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 1.25rem;
  padding: 0.75rem;
  transition:
    border-color 0.3s,
    box-shadow 0.3s;

  &:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
}

// Block 1
.composer-textarea {
  :deep(.el-textarea__inner) {
    border: none;
    box-shadow: none;
    resize: none;
    padding: 0;
  }
}

// Block 2 (duplicate)
.composer-textarea .el-textarea__inner {
  background-color: transparent;
  box-shadow: none !important;
  padding: 0;
  resize: none;
  line-height: 1.5;
}

.tool-button-group {
  display: inline-flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
  padding: 2px;

  .el-button.is-plain {
    border: none;
    margin: 0;
    padding-left: 10px;
    padding-right: 10px;
  }
}

// Typing indicator animation
.typing-indicator span {
  animation: bounce 1.4s infinite ease-in-out both;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

// Message animations
.flex {
  animation: fadeInUp 0.3s ease-out;
}

.el-textarea.is-disabled .el-textarea__inner {
  background-color: unset;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Dropdown menu styling
.el-dropdown-menu__item .el-icon {
  margin-right: 8px;
}

// Responsive
@media (max-width: 768px) {
  .chat-messages {
    padding: 1rem;
  }

  .chat-footer {
    padding: 0.75rem;
  }
}
</style>
