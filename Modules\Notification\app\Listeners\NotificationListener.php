<?php

namespace Modules\Notification\Listeners;

use Illuminate\Support\Facades\Log;
use Illuminate\Queue\InteractsWithQueue;
use Modules\Notification\Facades\NotificationFacade;

class NotificationListener
{
    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle($event): void
    {
        NotificationFacade::trigger($event->eventKey, $event->payload);
    }
}
