<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{{ $title ?? 'Notification' }}</title>
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
        }

        /* Base styles */
        body {
            margin: 0;
            padding: 0;
            width: 100% !important;
            min-width: 100%;
            background-color: #f4f4f4;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 16px;
            line-height: 1.6;
            color: #333333;
        }

        /* Container */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Header */
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px 20px;
            text-align: center;
        }

        .email-header h1 {
            margin: 0;
            color: #ffffff;
            font-size: 24px;
            font-weight: 600;
        }

        .logo {
            margin-bottom: 15px;
        }

        /* Content */
        .email-content {
            padding: 40px 30px;
        }

        .email-content h2 {
            margin: 0 0 20px 0;
            color: #333333;
            font-size: 20px;
            font-weight: 600;
        }

        .email-content p {
            margin: 0 0 15px 0;
            line-height: 1.6;
        }

        .email-content .content-text {
            white-space: pre-line;
            word-wrap: break-word;
        }

        /* Button */
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #667eea;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background-color: #5a6fd8;
        }

        /* Footer */
        .email-footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .email-footer p {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #6c757d;
        }

        .email-footer a {
            color: #667eea;
            text-decoration: none;
        }

        .email-footer a:hover {
            text-decoration: underline;
        }

        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                margin: 0 !important;
                border-radius: 0 !important;
            }
            
            .email-header {
                padding: 20px 15px;
            }
            
            .email-header h1 {
                font-size: 20px;
            }
            
            .email-content {
                padding: 30px 20px;
            }
            
            .email-footer {
                padding: 20px 15px;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .email-container {
                background-color: #1a1a1a;
            }
            
            .email-content {
                color: #e0e0e0;
            }
            
            .email-content h2 {
                color: #ffffff;
            }
            
            .email-footer {
                background-color: #2d2d2d;
                border-top-color: #404040;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="logo">
                <!-- You can add your logo here -->
                <h1>{{ $app_name ?? 'Laravel ProCMS' }}</h1>
            </div>
        </div>

        <!-- Content -->
        <div class="email-content">
            @if(!empty($title))
                <h2>{{ $title }}</h2>
            @endif

            <div class="content-text">
                {!! nl2br(e($content)) !!}
            </div>

            @if(!empty($app_url))
                <p style="margin-top: 30px;">
                    <a href="{{ $app_url }}" class="btn">Visit {{ $app_name ?? 'Our Website' }}</a>
                </p>
            @endif
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p>
                This email was sent by <strong>{{ $app_name ?? 'Laravel ProCMS' }}</strong>
            </p>
            
            @if(!empty($app_url))
                <p>
                    <a href="{{ $app_url }}">{{ $app_url }}</a>
                </p>
            @endif

            <p style="margin-top: 20px; font-size: 12px; color: #999;">
                If you have any questions, please don't hesitate to contact our support team.
            </p>

            <p style="font-size: 12px; color: #999;">
                © {{ date('Y') }} {{ $app_name ?? 'Laravel ProCMS' }}. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
