<?php

namespace Modules\ModelAI\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class ModelAIToolFilter extends AbstractFilter
{
    protected function filters(): array
    {
        return [
            'model_ai_id' => 'exact',
            'model_tool_id' => 'exact',
            'is_enabled' => 'exact',
            'priority' => 'exact',
            'max_usage_per_request' => 'exact',
            'rate_limit_per_minute' => 'exact',
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
            'updated_from' => ['type' => 'from', 'column' => 'updated_at'],
            'updated_to' => ['type' => 'to', 'column' => 'updated_at'],
        ];
    }
}
