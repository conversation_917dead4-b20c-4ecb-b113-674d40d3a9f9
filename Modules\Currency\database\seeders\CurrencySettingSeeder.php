<?php

namespace Modules\Currency\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class CurrencySettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        // 1. Get or create setting group
        $group = DB::table('setting_groups')->where('key', 'currency')->first();
        if (!$group) {
            $groupId = DB::table('setting_groups')->insertGetId([
                'key' => 'currency',
                'label' => 'Thiết lập Tiền tệ',
                'description' => 'Cấu hình đơn vị tiền tệ mặc định và định dạng hiển thị.',
                'icon' => 'fa-solid fa-coins',
                'sort_order' => 10,
                'created_at' => $now,
                'updated_at' => $now,
            ]);
        } else {
            $groupId = $group->id;
        }

        // 2. Insert or update settings
        $settings = [
            [
                'key' => 'currency.default',
                'value' => 'VND',
                'type' => 'string',
                'input_type' => 'select',
                'label' => 'Tiền tệ mặc định',
                'description' => 'Đơn vị tiền tệ chính được sử dụng trong toàn hệ thống.',
                'options' => json_encode(['VND', 'USD', 'EUR']),
                'is_public' => false,
                'sort_order' => 1,
                'validation_rules' => 'required|string|size:3',
                'group_id' => $groupId,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'key' => 'currency.decimal_separator',
                'value' => '.',
                'type' => 'string',
                'input_type' => 'text',
                'label' => 'Dấu phân cách phần thập phân',
                'description' => 'Ví dụ: . hoặc ,',
                'options' => null,
                'is_public' => false,
                'sort_order' => 2,
                'validation_rules' => 'required|string|max:1',
                'group_id' => $groupId,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'key' => 'currency.thousands_separator',
                'value' => ',',
                'type' => 'string',
                'input_type' => 'text',
                'label' => 'Dấu phân cách hàng nghìn',
                'description' => 'Ví dụ: , hoặc .',
                'options' => null,
                'is_public' => false,
                'sort_order' => 3,
                'validation_rules' => 'required|string|max:1',
                'group_id' => $groupId,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        // Insert or update each setting
        foreach ($settings as $setting) {
            DB::table('settings')->updateOrInsert(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
