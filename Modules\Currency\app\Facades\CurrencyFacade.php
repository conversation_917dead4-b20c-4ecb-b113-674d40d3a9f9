<?php

namespace Modules\Currency\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static \Illuminate\Support\Collection getActiveCurrencies()
 * @method static \Modules\Currency\Models\Currency|null getDefaultCurrency()
 * @method static \Modules\Currency\Models\Currency|null findByCode(string $code)
 * @method static float|null convertAmount(float $amount, string $fromCurrency, string $toCurrency)
 * @method static float|null getExchangeRate(string $fromCurrency, string $toCurrency)
 * @method static string formatAmount(float $amount, string $currencyCode)
 * @method static array getSupportedCurrencyCodes()
 * @method static \Illuminate\Support\Collection getCurrenciesForDropdown()
 * @method static \Modules\Currency\Models\ExchangeRate updateExchangeRate(string $baseCurrency, string $targetCurrency, float $rate)
 *
 * @see \Modules\Currency\Services\CurrencyService
 */
class CurrencyFacade extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return 'currency.service'; // This is the key we'll bind in the service provider
    }
}
