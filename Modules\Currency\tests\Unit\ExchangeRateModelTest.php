<?php

namespace Modules\Currency\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use Modules\Currency\Models\Currency;
use Modules\Currency\Models\ExchangeRate;
use PHPUnit\Framework\Attributes\Test;

class ExchangeRateModelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Currency module
        $this->artisan('migrate', ['--path' => 'Modules/Currency/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_can_create_an_exchange_rate()
    {
        $baseCurrency = Currency::factory()->create(['code' => 'USD']);
        $targetCurrency = Currency::factory()->create(['code' => 'EUR']);

        $exchangeRateData = [
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'rate' => 0.85,
            'fetched_at' => now(),
        ];

        $exchangeRate = ExchangeRate::create($exchangeRateData);

        $this->assertInstanceOf(ExchangeRate::class, $exchangeRate);
        $this->assertDatabaseHas('exchange_rates', [
            'id' => $exchangeRate->id,
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'rate' => 0.85,
        ]);
        $this->assertEquals('USD', $exchangeRate->base_currency);
        $this->assertEquals('EUR', $exchangeRate->target_currency);
        $this->assertEquals(0.85, $exchangeRate->rate);
    }

    #[Test]
    public function it_has_fillable_attributes()
    {
        $exchangeRate = new ExchangeRate();
        $expectedFillable = [
            'base_currency',
            'target_currency',
            'rate',
            'fetched_at',
        ];

        $this->assertEquals($expectedFillable, $exchangeRate->getFillable());
    }

    #[Test]
    public function it_has_correct_casts()
    {
        $exchangeRate = new ExchangeRate();
        $expectedCasts = [
            'rate' => 'decimal:8',
            'fetched_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];

        foreach ($expectedCasts as $attribute => $cast) {
            $this->assertEquals($cast, $exchangeRate->getCasts()[$attribute]);
        }
    }

    #[Test]
    public function it_has_base_currency_relationship()
    {
        $baseCurrency = Currency::factory()->create(['code' => 'USD']);
        $targetCurrency = Currency::factory()->create(['code' => 'EUR']);
        
        $exchangeRate = ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
        ]);

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsTo::class, $exchangeRate->baseCurrency());
        $this->assertEquals($baseCurrency->id, $exchangeRate->baseCurrency->id);
    }

    #[Test]
    public function it_has_target_currency_relationship()
    {
        $baseCurrency = Currency::factory()->create(['code' => 'USD']);
        $targetCurrency = Currency::factory()->create(['code' => 'EUR']);
        
        $exchangeRate = ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
        ]);

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsTo::class, $exchangeRate->targetCurrency());
        $this->assertEquals($targetCurrency->id, $exchangeRate->targetCurrency->id);
    }

    #[Test]
    public function it_can_scope_for_currency_pair_using_combined_scopes()
    {
        $exchangeRate1 = ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
        ]);
        $exchangeRate2 = ExchangeRate::factory()->create([
            'base_currency' => 'EUR',
            'target_currency' => 'USD',
        ]);
        $exchangeRate3 = ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'GBP',
        ]);

        $results = ExchangeRate::baseCurrency('USD')->targetCurrency('EUR')->get();

        $this->assertCount(1, $results);
        $this->assertTrue($results->contains($exchangeRate1));
        $this->assertFalse($results->contains($exchangeRate2));
        $this->assertFalse($results->contains($exchangeRate3));
    }

    #[Test]
    public function it_can_scope_fresh_rates()
    {
        $freshRate = ExchangeRate::factory()->create([
            'fetched_at' => now()->subMinutes(30),
        ]);
        $staleRate = ExchangeRate::factory()->create([
            'fetched_at' => now()->subHours(2),
        ]);

        // Mock setting function to return 1 hour threshold
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_fresh_threshold') {
                    return 3600; // 1 hour
                }
                return $default;
            };
        });

        $freshRates = ExchangeRate::fresh()->get();

        $this->assertTrue($freshRates->contains($freshRate));
        $this->assertFalse($freshRates->contains($staleRate));
    }

    #[Test]
    public function it_can_scope_stale_rates()
    {
        $freshRate = ExchangeRate::factory()->create([
            'fetched_at' => now()->subMinutes(30),
        ]);
        $staleRate = ExchangeRate::factory()->create([
            'fetched_at' => now()->subDays(2),
        ]);

        // Mock setting function to return 24 hours threshold
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_stale_threshold') {
                    return 86400; // 24 hours
                }
                return $default;
            };
        });

        $staleRates = ExchangeRate::stale()->get();

        $this->assertTrue($staleRates->contains($staleRate));
        $this->assertFalse($staleRates->contains($freshRate));
    }

    #[Test]
    public function it_can_get_rate_for_currency_pair()
    {
        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'rate' => 0.85,
            'fetched_at' => now(),
        ]);

        $rate = ExchangeRate::getRate('USD', 'EUR');

        $this->assertEquals(0.85, $rate);
    }

    #[Test]
    public function it_returns_one_for_same_currency_pair()
    {
        $rate = ExchangeRate::getRate('USD', 'USD');

        $this->assertEquals(1.0, $rate);
    }

    #[Test]
    public function it_returns_null_for_non_existent_rate()
    {
        $rate = ExchangeRate::getRate('USD', 'XYZ');

        $this->assertNull($rate);
    }

    #[Test]
    public function it_returns_latest_rate_for_currency_pair()
    {
        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'rate' => 0.80,
            'fetched_at' => now()->subHour(),
        ]);
        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'rate' => 0.85,
            'fetched_at' => now(),
        ]);

        $rate = ExchangeRate::getRate('USD', 'EUR');

        $this->assertEquals(0.85, $rate);
    }

    #[Test]
    public function it_can_convert_amount()
    {
        $exchangeRate = ExchangeRate::factory()->create(['rate' => 0.85]);

        $convertedAmount = $exchangeRate->convertAmount(100);

        $this->assertEquals(85.0, $convertedAmount);
    }

    #[Test]
    public function it_can_check_if_rate_is_fresh()
    {
        // Mock setting function
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_fresh_threshold') {
                    return 3600; // 1 hour
                }
                return $default;
            };
        });

        $freshRate = ExchangeRate::factory()->create([
            'fetched_at' => now()->subMinutes(30),
        ]);
        $staleRate = ExchangeRate::factory()->create([
            'fetched_at' => now()->subHours(2),
        ]);
        $nullRate = ExchangeRate::factory()->create([
            'fetched_at' => null,
        ]);

        $this->assertTrue($freshRate->isFresh());
        $this->assertFalse($staleRate->isFresh());
        $this->assertFalse($nullRate->isFresh());
    }

    #[Test]
    public function it_can_check_if_rate_is_stale()
    {
        // Mock setting function
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_stale_threshold') {
                    return 86400; // 24 hours
                }
                return $default;
            };
        });

        $freshRate = ExchangeRate::factory()->create([
            'fetched_at' => now()->subMinutes(30),
        ]);
        $staleRate = ExchangeRate::factory()->create([
            'fetched_at' => now()->subDays(2),
        ]);
        $nullRate = ExchangeRate::factory()->create([
            'fetched_at' => null,
        ]);

        $this->assertFalse($freshRate->isStale());
        $this->assertTrue($staleRate->isStale());
        $this->assertTrue($nullRate->isStale());
    }

    #[Test]
    public function it_has_correct_table_structure()
    {
        $this->assertTrue(Schema::hasTable('exchange_rates'));

        $expectedColumns = [
            'id', 'base_currency', 'target_currency', 'rate', 'fetched_at',
            'created_at', 'updated_at'
        ];

        foreach ($expectedColumns as $column) {
            $this->assertTrue(Schema::hasColumn('exchange_rates', $column));
        }
    }

    #[Test]
    public function it_uses_exchange_rate_factory()
    {
        $exchangeRate = ExchangeRate::factory()->create();

        $this->assertInstanceOf(ExchangeRate::class, $exchangeRate);
        $this->assertNotNull($exchangeRate->base_currency);
        $this->assertNotNull($exchangeRate->target_currency);
        $this->assertNotNull($exchangeRate->rate);
    }

    #[Test]
    public function it_can_create_factory_with_specific_currency_pair()
    {
        Currency::factory()->create(['code' => 'USD']);
        Currency::factory()->create(['code' => 'EUR']);

        $exchangeRate = ExchangeRate::factory()->forCurrencyPair('USD', 'EUR')->create();

        $this->assertEquals('USD', $exchangeRate->base_currency);
        $this->assertEquals('EUR', $exchangeRate->target_currency);
    }

    #[Test]
    public function it_can_create_factory_with_specific_rate()
    {
        $exchangeRate = ExchangeRate::factory()->withRate(1.25)->create();

        $this->assertEquals(1.25, $exchangeRate->rate);
    }

    #[Test]
    public function it_can_create_fresh_factory_state()
    {
        $exchangeRate = ExchangeRate::factory()->fresh()->create();

        $this->assertTrue($exchangeRate->fetched_at->gt(now()->subHour()));
    }

    #[Test]
    public function it_can_create_stale_factory_state()
    {
        $exchangeRate = ExchangeRate::factory()->stale()->create();

        $this->assertTrue($exchangeRate->fetched_at->lt(now()->subDay()));
    }

    #[Test]
    public function it_can_scope_latest_rates()
    {
        $oldRate = ExchangeRate::factory()->create([
            'fetched_at' => now()->subHours(2),
        ]);
        $newRate = ExchangeRate::factory()->create([
            'fetched_at' => now()->subHour(),
        ]);

        $results = ExchangeRate::latest()->get();

        $this->assertEquals($newRate->id, $results->first()->id);
        $this->assertEquals($oldRate->id, $results->last()->id);
    }

    #[Test]
    public function it_can_scope_by_base_currency()
    {
        $usdRate = ExchangeRate::factory()->create(['base_currency' => 'USD']);
        $eurRate = ExchangeRate::factory()->create(['base_currency' => 'EUR']);

        $results = ExchangeRate::baseCurrency('USD')->get();

        $this->assertTrue($results->contains($usdRate));
        $this->assertFalse($results->contains($eurRate));
    }

    #[Test]
    public function it_can_scope_by_target_currency()
    {
        $toEurRate = ExchangeRate::factory()->create(['target_currency' => 'EUR']);
        $toGbpRate = ExchangeRate::factory()->create(['target_currency' => 'GBP']);

        $results = ExchangeRate::targetCurrency('EUR')->get();

        $this->assertTrue($results->contains($toEurRate));
        $this->assertFalse($results->contains($toGbpRate));
    }
}
