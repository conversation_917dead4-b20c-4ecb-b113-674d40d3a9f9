<?php

namespace Modules\Location\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

/**
 * @property mixed $id
 */
class CountryRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'native_name' => [
                'nullable',
                'string',
                'max:255',
            ],
            'iso_code_2' => [
                'required',
                'string',
                'size:2',
                'alpha',
                Rule::unique('countries')->ignore($this->id)->whereNull('deleted_at'),
            ],
            'iso_code_3' => [
                'nullable',
                'string',
                'size:3',
                'alpha',
                Rule::unique('countries')->ignore($this->id)->whereNull('deleted_at'),
            ],
            'iso_numeric' => [
                'nullable',
                'string',
                'size:3',
                'regex:/^\d{3}$/',
            ],
            'phone_code' => [
                'nullable',
                'string',
                'max:10',
                'regex:/^\+?\d+$/',
            ],
            'region' => [
                'nullable',
                'string',
                'max:255',
            ],
            'subregion' => [
                'nullable',
                'string',
                'max:255',
            ],
            'latitude' => [
                'nullable',
                'numeric',
                'between:-90,90',
            ],
            'longitude' => [
                'nullable',
                'numeric',
                'between:-180,180',
            ],
            'emoji' => [
                'nullable',
                'string',
                'max:10',
            ],
            'emoji_unicode' => [
                'nullable',
                'string',
                'max:20',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(['active', 'inactive']),
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => __('Country Name'),
            'native_name' => __('Native Name'),
            'iso_code_2' => __('ISO Code (2 letters)'),
            'iso_code_3' => __('ISO Code (3 letters)'),
            'iso_numeric' => __('ISO Numeric Code'),
            'phone_code' => __('Phone Code'),
            'region' => __('Region'),
            'subregion' => __('Subregion'),
            'latitude' => __('Latitude'),
            'longitude' => __('Longitude'),
            'emoji' => __('Emoji'),
            'emoji_unicode' => __('Emoji Unicode'),
            'status' => __('Status'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'iso_code_2.size' => __('The ISO code must be exactly 2 letters.'),
            'iso_code_3.size' => __('The ISO code must be exactly 3 letters.'),
            'iso_numeric.regex' => __('The ISO numeric code must be exactly 3 digits.'),
            'phone_code.regex' => __('The phone code must contain only numbers and may start with +.'),
            'latitude.between' => __('The latitude must be between -90 and 90 degrees.'),
            'longitude.between' => __('The longitude must be between -180 and 180 degrees.'),
        ];
    }
}
