# Location Module

The Location Module provides comprehensive geographic location management functionality for Laravel ProCMS, enabling creation, management, and retrieval of countries and geographic divisions (states, cities, districts) with hierarchical relationships and multi-language support.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [Database Schema](#database-schema)
- [Installation](#installation)
- [API Documentation](#api-documentation)
- [Usage Examples](#usage-examples)
- [Filtering](#filtering)
- [Multi-Language Support](#multi-language-support)
- [Testing](#testing)
- [Dependencies](#dependencies)
- [Contributing](#contributing)

## Overview

The Location Module is a core component of Laravel ProCMS that handles geographic location data management. It supports hierarchical location structures, multi-language content, and provides comprehensive APIs for location-based functionality.

### Key Components

- **Country Model**: Main country entity with ISO codes and geographic data
- **GeoDivision Model**: Hierarchical geographic divisions (states, cities, districts)
- **LocationController**: Public location data retrieval
- **AuthCountryController**: Administrative country CRUD operations
- **AuthGeoDivisionController**: Administrative geographic division CRUD operations
- **LocationService**: Business logic and data retrieval
- **LocationFilter**: Advanced filtering and search capabilities
- **LocationFacade**: Convenient access to location functionality

## Features

### Core Features
- ✅ **Hierarchical Structure**: Countries → States → Cities → Districts
- ✅ **Multi-language Support**: Native names and localized content
- ✅ **Status Management**: Active/inactive status for all location entities
- ✅ **Soft Delete**: Safe deletion with restore capabilities
- ✅ **ISO Code Support**: ISO 3166-1 alpha-2 and alpha-3 country codes
- ✅ **Geographic Coordinates**: Latitude and longitude support
- ✅ **Emoji Support**: Country flag emojis and Unicode support

### Advanced Features
- ✅ **Hierarchical Queries**: Parent-child relationships with level-based queries
- ✅ **Advanced Filtering**: Search across names, codes, and geographic data
- ✅ **Bulk Operations**: Mass delete, restore, and force delete operations
- ✅ **API-First Design**: Complete REST API with comprehensive endpoints
- ✅ **Comprehensive Testing**: 80%+ test coverage with unit and feature tests
- ✅ **Cascading Dropdowns**: Hierarchical data for frontend forms

### Data Management
- ✅ **Country Management**: Complete country data with ISO codes
- ✅ **Geographic Divisions**: Flexible division types and levels
- ✅ **Path Tracking**: Hierarchical path storage for efficient queries
- ✅ **Sort Ordering**: Custom sort orders for display purposes

## Architecture

The Location Module follows Laravel ProCMS architectural patterns:

```
Modules/Location/
├── app/
│   ├── Facades/
│   │   └── LocationFacade.php       # Service facade
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── LocationController.php # Public location data
│   │   │   └── Auth/
│   │   │       ├── CountryController.php # Admin country CRUD
│   │   │       └── GeoDivisionController.php # Admin division CRUD
│   │   ├── Filters/
│   │   │   ├── CountryFilter.php    # Country filtering
│   │   │   └── GeoDivisionFilter.php # Division filtering
│   │   └── Requests/
│   │       ├── CountryRequest.php   # Country validation
│   │       ├── GeoDivisionRequest.php # Division validation
│   │       └── BulkLocationRequest.php # Bulk operation validation
│   ├── Models/
│   │   ├── Country.php              # Country model
│   │   └── GeoDivision.php          # Geographic division model
│   ├── Providers/
│   │   ├── LocationServiceProvider.php # Service registration
│   │   └── RouteServiceProvider.php # Route registration
│   └── Services/
│       └── LocationService.php      # Business logic
├── database/
│   ├── factories/
│   │   ├── CountryFactory.php       # Country factory
│   │   └── GeoDivisionFactory.php   # Division factory
│   ├── migrations/
│   │   ├── 2024_01_01_000001_create_countries_table.php
│   │   └── 2024_01_01_000002_create_geo_divisions_table.php
│   └── seeders/
│       ├── CountrySeeder.php        # Country data seeder
│       └── GeoDivisionSeeder.php    # Division data seeder
├── routes/
│   ├── api.php                      # API routes
│   └── web.php                      # Web routes
└── tests/
    ├── Feature/                     # Integration tests
    └── Unit/                        # Unit tests
```

## Database Schema

### Countries Table
```sql
CREATE TABLE countries (
    id BIGINT UNSIGNED PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    native_name VARCHAR(255) NULL,
    iso_code_2 CHAR(2) NOT NULL UNIQUE,
    iso_code_3 CHAR(3) NOT NULL UNIQUE,
    iso_numeric VARCHAR(3) NULL,
    phone_code VARCHAR(10) NULL,
    region VARCHAR(255) NULL,
    subregion VARCHAR(255) NULL,
    latitude DECIMAL(10,8) NULL,
    longitude DECIMAL(11,8) NULL,
    emoji VARCHAR(10) NULL,
    emoji_unicode VARCHAR(20) NULL,
    status VARCHAR(255) DEFAULT 'active',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_countries_status (status),
    INDEX idx_countries_region (region),
    INDEX idx_countries_iso_codes (iso_code_2, iso_code_3)
);
```

### Geographic Divisions Table
```sql
CREATE TABLE geo_divisions (
    id BIGINT UNSIGNED PRIMARY KEY,
    code VARCHAR(255) NULL,
    name VARCHAR(255) NOT NULL,
    native_name VARCHAR(255) NULL,
    type VARCHAR(255) NOT NULL,
    country_id BIGINT UNSIGNED NOT NULL,
    parent_id BIGINT UNSIGNED NULL,
    level INTEGER NOT NULL DEFAULT 1,
    path VARCHAR(500) NULL,
    latitude DECIMAL(10,8) NULL,
    longitude DECIMAL(11,8) NULL,
    postal_code VARCHAR(20) NULL,
    sort_order INTEGER DEFAULT 0,
    status VARCHAR(255) DEFAULT 'active',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (country_id) REFERENCES countries(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES geo_divisions(id) ON DELETE CASCADE,
    INDEX idx_geo_divisions_country (country_id),
    INDEX idx_geo_divisions_parent (parent_id),
    INDEX idx_geo_divisions_type (type),
    INDEX idx_geo_divisions_level (level),
    INDEX idx_geo_divisions_status (status)
);
```

### Relationships
- **Country** has many **GeoDivisions**
- **GeoDivision** belongs to **Country**
- **GeoDivision** belongs to **GeoDivision** (parent)
- **GeoDivision** has many **GeoDivisions** (children)

## Installation

The Location Module is included in Laravel ProCMS by default. To manually install or reinstall:

### 1. Run Migrations
```bash
php artisan migrate --path=Modules/Location/database/migrations
```

### 2. Seed Data (Optional)
```bash
php artisan db:seed --class=Modules\\Location\\Database\\Seeders\\CountrySeeder
php artisan db:seed --class=Modules\\Location\\Database\\Seeders\\GeoDivisionSeeder
```

### 3. Publish Configuration (Optional)
```bash
php artisan vendor:publish --tag=location-config
```

### 4. Register Service Provider
The module is auto-registered via Laravel's package discovery. Manual registration in `config/app.php`:

```php
'providers' => [
    // Other providers...
    Modules\Location\Providers\LocationServiceProvider::class,
],
```

### 5. Register Facade (Optional)
```php
'aliases' => [
    // Other aliases...
    'LocationFacade' => Modules\Location\Facades\LocationFacade::class,
],
```

## API Documentation

### Public API Endpoints

#### Get Countries
```http
GET /api/v1/locations/countries
```

**Description**: Retrieve a list of active countries for dropdowns and selection.

**Response Structure**:
```json
{
    "success": true,
    "message": "Countries retrieved successfully.",
    "data": [
        {
            "id": 1,
            "name": "United States",
            "native_name": "United States",
            "iso_code_2": "US",
            "iso_code_3": "USA",
            "emoji": "🇺🇸"
        }
    ]
}
```

#### Get States for Country
```http
GET /api/v1/locations/countries/{countryId}/states
```

**Description**: Retrieve states/provinces for a specific country.

**Parameters**:
- `countryId` (integer, required): The country ID

**Response Structure**:
```json
{
    "success": true,
    "message": "States retrieved successfully.",
    "data": [
        {
            "id": 1,
            "name": "California",
            "native_name": "California",
            "code": "CA",
            "country_id": 1
        }
    ]
}
```

#### Get Cities for State
```http
GET /api/v1/locations/states/{stateId}/cities
```

**Description**: Retrieve cities for a specific state/province.

**Parameters**:
- `stateId` (integer, required): The state ID

#### Get Districts for City
```http
GET /api/v1/locations/cities/{cityId}/districts
```

**Description**: Retrieve districts for a specific city.

**Parameters**:
- `cityId` (integer, required): The city ID

#### Get Hierarchical Data
```http
GET /api/v1/locations/hierarchical
```

**Description**: Get hierarchical location data for cascading dropdowns.

**Query Parameters**:
- `country_id` (integer, optional): Filter by country
- `state_id` (integer, optional): Filter by state
- `city_id` (integer, optional): Filter by city

**Response Structure**:
```json
{
    "success": true,
    "message": "Hierarchical data retrieved successfully.",
    "data": {
        "countries": [...],
        "states": [...],
        "cities": [...],
        "districts": [...]
    }
}
```

#### Search Locations
```http
GET /api/v1/locations/search
```

**Description**: Search locations by name across countries and divisions.

**Query Parameters**:
- `query` (string, required): Search term
- `type` (string, optional): Filter by type (country, state, city, district)
- `country_id` (integer, optional): Filter by country
- `limit` (integer, optional): Number of results (default: 20)

#### Get Country by ISO Code
```http
GET /api/v1/locations/country/{isoCode}
```

**Description**: Retrieve a country by its ISO code (2 or 3 letter).

**Parameters**:
- `isoCode` (string, required): ISO code (e.g., "US" or "USA")

#### Get Division by ID
```http
GET /api/v1/locations/division/{id}
```

**Description**: Retrieve a geographic division by its ID.

**Parameters**:
- `id` (integer, required): The division ID

### Administrative API Endpoints

#### List Countries
```http
GET /api/v1/auth/countries
```

**Description**: Retrieve a paginated list of countries with filtering options.

**Query Parameters**:
- `limit` (integer, optional): Number of items per page (default: 10)
- `page` (integer, optional): Page number (default: 1)
- `status` (string, optional): Filter by status (active, inactive)
- `region` (string, optional): Filter by region
- `search` (string, optional): Search in name, native_name, ISO codes
- `is_trashed` (boolean, optional): Include only trashed countries

**Response Structure**:
```json
{
    "current_page": 1,
    "data": [
        {
            "id": 1,
            "name": "United States",
            "native_name": "United States",
            "iso_code_2": "US",
            "iso_code_3": "USA",
            "iso_numeric": "840",
            "phone_code": "+1",
            "region": "Americas",
            "subregion": "Northern America",
            "latitude": 37.0902,
            "longitude": -95.7129,
            "emoji": "🇺🇸",
            "emoji_unicode": "U+1F1FA U+1F1F8",
            "status": "active",
            "created_at": "2024-01-01T10:00:00.000000Z",
            "updated_at": "2024-01-01T11:00:00.000000Z"
        }
    ],
    "first_page_url": "http://api.example.com/api/v1/auth/countries?page=1",
    "from": 1,
    "last_page": 5,
    "last_page_url": "http://api.example.com/api/v1/auth/countries?page=5",
    "links": [...],
    "next_page_url": "http://api.example.com/api/v1/auth/countries?page=2",
    "path": "http://api.example.com/api/v1/auth/countries",
    "per_page": 10,
    "prev_page_url": null,
    "to": 10,
    "total": 50
}
```

#### Create Country
```http
POST /api/v1/auth/countries
```

**Description**: Create a new country.

**Request Body**:
```json
{
    "name": "New Country",
    "native_name": "Native Name",
    "iso_code_2": "NC",
    "iso_code_3": "NCO",
    "iso_numeric": "999",
    "phone_code": "+999",
    "region": "Region",
    "subregion": "Subregion",
    "latitude": 0.0,
    "longitude": 0.0,
    "emoji": "🏳️",
    "emoji_unicode": "U+1F3F3 U+FE0F",
    "status": "active"
}
```

**Validation Rules**:
- `name`: required, string, max 255 characters, unique
- `native_name`: nullable, string, max 255 characters
- `iso_code_2`: required, string, exactly 2 characters, unique
- `iso_code_3`: required, string, exactly 3 characters, unique
- `iso_numeric`: nullable, string, max 3 characters
- `phone_code`: nullable, string, max 10 characters
- `region`: nullable, string, max 255 characters
- `subregion`: nullable, string, max 255 characters
- `latitude`: nullable, numeric, between -90 and 90
- `longitude`: nullable, numeric, between -180 and 180
- `emoji`: nullable, string, max 10 characters
- `emoji_unicode`: nullable, string, max 20 characters
- `status`: required, in: active, inactive

#### Update Country
```http
PUT /api/v1/auth/countries/{id}
```

**Description**: Update an existing country.

**Parameters**:
- `id` (integer, required): The country ID

**Request Body**: Same structure as Create Country

#### Delete Country (Soft Delete)
```http
DELETE /api/v1/auth/countries/{id}
```

**Description**: Soft delete a country (can be restored).

#### Restore Country
```http
PUT /api/v1/auth/countries/{id}/restore
```

**Description**: Restore a soft-deleted country.

#### Force Delete Country
```http
DELETE /api/v1/auth/countries/{id}/force-delete
```

**Description**: Permanently delete a country (cannot be restored).

#### Bulk Operations for Countries

##### Bulk Delete
```http
DELETE /api/v1/auth/countries/bulk-delete
```

**Request Body**:
```json
{
    "ids": [1, 2, 3, 4, 5]
}
```

##### Bulk Restore
```http
PUT /api/v1/auth/countries/bulk-restore
```

##### Bulk Force Delete
```http
DELETE /api/v1/auth/countries/bulk-force-delete
```

#### Geographic Divisions API

The Geographic Divisions API follows the same patterns as Countries API with these endpoints:

- `GET /api/v1/auth/geo-divisions` - List divisions
- `POST /api/v1/auth/geo-divisions` - Create division
- `GET /api/v1/auth/geo-divisions/{id}` - Show division
- `PUT /api/v1/auth/geo-divisions/{id}` - Update division
- `DELETE /api/v1/auth/geo-divisions/{id}` - Delete division
- `PUT /api/v1/auth/geo-divisions/{id}/restore` - Restore division
- `DELETE /api/v1/auth/geo-divisions/{id}/force-delete` - Force delete
- `DELETE /api/v1/auth/geo-divisions/bulk-delete` - Bulk delete
- `PUT /api/v1/auth/geo-divisions/bulk-restore` - Bulk restore
- `DELETE /api/v1/auth/geo-divisions/bulk-force-delete` - Bulk force delete

**Geographic Division Validation Rules**:
- `code`: nullable, string, max 255 characters
- `name`: required, string, max 255 characters
- `native_name`: nullable, string, max 255 characters
- `type`: required, in: state, city, district
- `country_id`: required, integer, exists in countries table
- `parent_id`: nullable, integer, exists in geo_divisions table
- `level`: required, integer, min 1
- `path`: nullable, string, max 500 characters
- `latitude`: nullable, numeric, between -90 and 90
- `longitude`: nullable, numeric, between -180 and 180
- `postal_code`: nullable, string, max 20 characters
- `sort_order`: nullable, integer
- `status`: required, in: active, inactive

## Usage Examples

### Using the Location Service

```php
use Modules\Location\Services\LocationService;

// Resolve service from container
$locationService = app(LocationService::class);

// Get active countries for dropdown
$countries = $locationService->getActiveCountriesForDropdown();

// Get states for a country
$states = $locationService->getStatesForCountry(1);

// Get cities for a state
$cities = $locationService->getCitiesForState(1);

// Get districts for a city
$districts = $locationService->getDistrictsForCity(1);

// Get hierarchical data
$hierarchicalData = $locationService->getHierarchicalData(
    countryId: 1,
    stateId: 2,
    cityId: 3
);

// Search locations
$results = $locationService->searchLocations(
    query: 'New York',
    type: 'city',
    countryId: 1,
    limit: 10
);

// Find country by ISO code
$country = $locationService->findCountryByIsoCode('US');

// Find division by ID
$division = $locationService->findGeoDivisionById(1);
```

### Using the Location Facade

```php
use Modules\Location\Facades\LocationFacade;

// Get countries for dropdown
$countries = LocationFacade::getActiveCountriesForDropdown();

// Get hierarchical data
$data = LocationFacade::getHierarchicalData(countryId: 1);

// Search locations
$results = LocationFacade::searchLocations('California');
```

### Working with Location Models

```php
use Modules\Location\Models\Country;
use Modules\Location\Models\GeoDivision;

// Create a new country
$country = Country::create([
    'name' => 'New Country',
    'native_name' => 'Native Name',
    'iso_code_2' => 'NC',
    'iso_code_3' => 'NCO',
    'status' => 'active',
]);

// Create a state
$state = GeoDivision::create([
    'name' => 'New State',
    'type' => 'state',
    'country_id' => $country->id,
    'level' => 1,
    'status' => 'active',
]);

// Create a city under the state
$city = GeoDivision::create([
    'name' => 'New City',
    'type' => 'city',
    'country_id' => $country->id,
    'parent_id' => $state->id,
    'level' => 2,
    'status' => 'active',
]);

// Query countries
$activeCountries = Country::active()->get();
$countriesByRegion = Country::where('region', 'Americas')->get();

// Query geographic divisions
$statesInCountry = GeoDivision::country(1)->type('state')->get();
$citiesInState = GeoDivision::parent(1)->type('city')->get();
$divisionsAtLevel = GeoDivision::level(2)->get();

// Get hierarchical relationships
$country = Country::with(['states', 'cities', 'districts'])->find(1);
$state = GeoDivision::with(['children', 'parent', 'country'])->find(1);

// Check status
if ($country->isActive()) {
    echo "Country is active";
}

// Get full hierarchical path
$division = GeoDivision::find(1);
echo $division->full_name; // "Country > State > City"
```

## Filtering

The Location Module provides comprehensive filtering capabilities through filter classes:

### Available Filters

#### Country Filters
- `status`: Filter by status (active, inactive)
- `region`: Filter by region
- `subregion`: Filter by subregion
- `search`: Global search across name, native_name, ISO codes
- `is_trashed`: Include only soft-deleted countries

#### Geographic Division Filters
- `status`: Filter by status (active, inactive)
- `type`: Filter by type (state, city, district)
- `country_id`: Filter by country
- `parent_id`: Filter by parent division
- `level`: Filter by hierarchical level
- `search`: Global search across name, native_name, code
- `is_trashed`: Include only soft-deleted divisions

### Filter Usage Examples

```php
// Filter countries
GET /api/v1/auth/countries?status=active&region=Americas&search=United

// Filter geographic divisions
GET /api/v1/auth/geo-divisions?type=state&country_id=1&level=1

// Combined filters
GET /api/v1/auth/geo-divisions?status=active&type=city&parent_id=5&search=New
```

## Multi-Language Support

The Location Module provides multi-language support through native name fields:

### Translation Fields

- **Countries**: `name` and `native_name` fields
- **Geographic Divisions**: `name` and `native_name` fields

### Usage Examples

```php
// Create country with native name
$country = Country::create([
    'name' => 'Germany',
    'native_name' => 'Deutschland',
    'iso_code_2' => 'DE',
    'iso_code_3' => 'DEU',
]);

// Create division with native name
$state = GeoDivision::create([
    'name' => 'Bavaria',
    'native_name' => 'Bayern',
    'type' => 'state',
    'country_id' => $country->id,
]);

// Display names
echo $country->name; // "Germany"
echo $country->native_name; // "Deutschland"
```

## Testing

The Location Module includes comprehensive test coverage:

### Test Structure

```
tests/
├── Feature/
│   ├── CountryApiTest.php           # Country API endpoints
│   ├── GeoDivisionApiTest.php       # Division API endpoints
│   └── LocationApiTest.php          # Public location endpoints
└── Unit/
    ├── CountryModelTest.php         # Country model functionality
    ├── GeoDivisionModelTest.php     # Division model functionality
    ├── LocationServiceTest.php      # Service layer testing
    └── LocationRequestTest.php      # Form validation testing
```

### Running Tests

```bash
# Run all Location module tests
php artisan test Modules/Location/tests/

# Run specific test types
php artisan test Modules/Location/tests/Unit/
php artisan test Modules/Location/tests/Feature/

# Run specific test file
php artisan test Modules/Location/tests/Unit/LocationServiceTest.php

# Run with coverage
php artisan test Modules/Location/tests/ --coverage
```

### Test Categories

#### Unit Tests (4 test files, 100+ test methods)

**CountryModelTest** (25 test methods)
- Model relationships and scopes
- Status checking methods
- ISO code validation
- Geographic coordinate handling
- Factory integration

**GeoDivisionModelTest** (30 test methods)
- Hierarchical relationships
- Level and path management
- Type-specific scopes
- Parent-child relationships
- Path generation and validation

**LocationServiceTest** (25 test methods)
- Service method functionality
- Data retrieval methods
- Search functionality
- Hierarchical data building
- Error handling

**LocationRequestTest** (20 test methods)
- Validation rule testing
- Required field validation
- Unique constraint validation
- Custom validation rules
- Error message testing

#### Feature Tests (3 test files, 60+ test methods)

**CountryApiTest** (25 test methods)
- CRUD operations
- Pagination testing
- Filtering integration
- Bulk operations
- Response structure validation

**GeoDivisionApiTest** (25 test methods)
- Division CRUD operations
- Hierarchical queries
- Type-specific operations
- Bulk operations
- Validation testing

**LocationApiTest** (15 test methods)
- Public endpoint testing
- Hierarchical data retrieval
- Search functionality
- Error responses
- Response structure validation

## Dependencies

### Core Dependencies

- **Laravel Framework**: ^10.0 || ^11.0
- **PHP**: ^8.1
- **MySQL/PostgreSQL**: Database support

### Module Dependencies

- **Core Module**: For base request classes and traits
- **User Model**: For potential user relationships (future)

### Package Dependencies

- **nwidart/laravel-modules**: Module architecture
- **illuminate/database**: Eloquent ORM
- **illuminate/validation**: Form validation

### Development Dependencies

- **PHPUnit**: Testing framework
- **Laravel Testing**: Feature and unit testing
- **Faker**: Test data generation

## Contributing

We welcome contributions to the Location Module! Please follow these guidelines:

### Development Setup

1. **Clone the repository**
2. **Install dependencies**: `composer install`
3. **Run migrations**: `php artisan migrate`
4. **Seed data**: `php artisan db:seed --class=Modules\\Location\\Database\\Seeders\\CountrySeeder`
5. **Run tests**: `php artisan test Modules/Location/tests/`

### Coding Standards

- Follow PSR-12 coding standards
- Use meaningful variable and method names
- Add comprehensive docblocks
- Maintain 80%+ test coverage

### Pull Request Process

1. **Create feature branch**: `git checkout -b feature/new-feature`
2. **Write tests**: Ensure new functionality is tested
3. **Run test suite**: All tests must pass
4. **Update documentation**: Update README if needed
5. **Submit pull request**: With clear description

### Testing Requirements

- All new features must include unit tests
- Feature tests for API endpoints
- Maintain existing test coverage
- Follow existing test patterns

### Code Review Checklist

- [ ] Code follows PSR-12 standards
- [ ] All tests pass
- [ ] Documentation updated
- [ ] No breaking changes (or properly documented)
- [ ] Performance considerations addressed
- [ ] Security implications reviewed

---

For more information about Laravel ProCMS architecture and patterns, see the main project documentation.
