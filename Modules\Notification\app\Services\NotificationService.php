<?php

namespace Modules\Notification\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Modules\Notification\Jobs\NotificationJob;
use Modules\Notification\Models\NotificationType;
use Modules\User\Models\User;

class NotificationService
{
    /**
     * Trigger a notification event.
     *
     * @param string $eventKey The key of the notification event.
     * @param array $data Additional data to pass to the notification.
     */
    public function trigger(string $eventKey, array $data = []): void
    {
        try {
            if (!setting_bool('notifications.enabled')) {
                return;
            }

            $notificationType = Cache::tags('notifications')->remember(
                key: "notification_type:{$eventKey}",
                ttl: now()->addMinutes(60), // Cache for 1 hour
                callback: fn() => NotificationType::where([
                    'key' => $eventKey,
                    'status' => 'active'
                ])
                    ->with(['templates' => function ($query) {
                        $query->where('status', 'active');
                    }
                    ])->first()
            );

            if (!$notificationType) {
                return;
            }

            $recipients = $this->resolveRecipients($data);

            foreach ($recipients as $recipient) {
                foreach ($notificationType->channels as $channel) {
                    if (!setting_bool("notifications.{$channel}.enabled")) {
                        continue;
                    }
                    $job = new NotificationJob(
                        $notificationType,
                        $channel,
                        $recipient,
                        $data
                    );
                    $queueEnabled = setting_bool('notifications.queue.enabled');

                    $queueEnabled ? dispatch($job) : dispatch_sync($job);

                    Log::info("Dispatched notification job for {$eventKey} to {$channel} channel");
                }
            }

        } catch (\Throwable $e) {
            Log::error('Failed to trigger notification', [
                'event_key' => $eventKey,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    protected function resolveRecipients(array $data): array
    {
        $recipients = [];

        // Nếu có user object trong data
        if (isset($data['user']) ) {
            if (is_object($data['user'])) {
                $data['user'] = $data['user']->toArray();
            }
            $recipients[] = $data['user'];
        }

        // Nếu có user_id trong data
        if (isset($data['user_id'])) {
            $user = User::find($data['user_id'])->toArray();
            if ($user) {
                $recipients[] = $user;
            }
        }

        // Nếu chưa có người nhận và có email trong data (cho guest users)
        if (empty($recipients) && isset($data['email'])) {
            $recipients[] = [
                'email' => $data['email'],
                'username' => $data['name'] ?? $data['username'] ?? 'Guest',
                'first_name' => $data['first_name'] ?? $data['username'] ?? 'Guest',
                'last_name' => $data['last_name'] ?? '',
            ];
        }

        return $recipients;
    }
}
