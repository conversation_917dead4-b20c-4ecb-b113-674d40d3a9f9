<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('countries', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('native_name')->nullable();
            $table->string('iso_code_2', 2)->unique();
            $table->string('iso_code_3', 3)->nullable();
            $table->string('iso_numeric', 3)->nullable();
            $table->string('phone_code', 10)->nullable();
            $table->string('region')->nullable();
            $table->string('subregion')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->string('emoji', 10)->nullable();
            $table->string('emoji_unicode', 20)->nullable();
            $table->string('status')->default('active');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['status']);
            $table->index(['region']);
            $table->index(['iso_code_2']);
            $table->index(['iso_code_3']);
        });

        Schema::create('geo_divisions', function (Blueprint $table) {
            $table->id();
            $table->string('code')->nullable();
            $table->string('name');
            $table->string('native_name')->nullable();
            $table->string('type')->nullable(); // state, province, city, district, ward...
            $table->unsignedBigInteger('country_id');
            $table->foreign('country_id')->references('id')->on('countries')->cascadeOnDelete();
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->foreign('parent_id')->references('id')->on('geo_divisions')->nullOnDelete();
            $table->unsignedTinyInteger('level')->default(0);
            $table->string('path')->nullable(); // Materialized path: 1/23/456
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->string('postal_code')->nullable();
            $table->integer('sort_order')->default(1);
            $table->string('status')->default('active');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['code']);
            $table->index(['country_id', 'level']);
            $table->index(['parent_id']);
            $table->index(['type']);
            $table->index(['status']);
            $table->index(['postal_code']);
            $table->index(['path']);
            $table->unique(['country_id', 'parent_id', 'code']);
            // Full-text search index for search functionality (MySQL only)
            // Temporarily disabled for testing with SQLite
            // if (DB::getDriverName() === 'mysql') {
            //     $table->fullText(['name', 'native_name']);
            // }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('geo_divisions');
        Schema::dropIfExists('countries');
    }
};
