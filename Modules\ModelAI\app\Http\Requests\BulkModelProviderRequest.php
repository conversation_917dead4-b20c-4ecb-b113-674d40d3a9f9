<?php

namespace Modules\ModelAI\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BulkModelProviderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
                'max:100', // Limit bulk operations
            ],
            'ids.*' => [
                'required',
                'integer',
                'exists:model_providers,id',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => 'provider IDs',
            'ids.*' => 'provider ID',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => 'At least one provider ID is required.',
            'ids.array' => 'Provider IDs must be provided as an array.',
            'ids.min' => 'At least one provider ID is required.',
            'ids.max' => 'Cannot process more than 100 providers at once.',
            'ids.*.required' => 'Each provider ID is required.',
            'ids.*.integer' => 'Each provider ID must be a valid integer.',
            'ids.*.exists' => 'One or more selected providers do not exist.',
        ];
    }
}
