<?php

namespace Modules\ModelAI\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class ModelProviderFilter extends AbstractFilter
{
    protected function filters(): array
    {
        return [
            'key' => 'like',
            'name' => 'like',
            'description' => 'like',
            'status' => 'exact',
            'has_models' => 'custom',
            'is_trashed' => 'trashed',
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
            'updated_from' => ['type' => 'from', 'column' => 'updated_at'],
            'updated_to' => ['type' => 'to', 'column' => 'updated_at'],
        ];
    }

    /**
     * Custom filter for providers that have models.
     */
    protected function hasModels($query, $value)
    {
        $hasModels = filter_var($value, FILTER_VALIDATE_BOOLEAN);

        if ($hasModels) {
            return $query->has('modelAIs');
        } else {
            return $query->doesntHave('modelAIs');
        }
    }
}
