<?php

namespace Modules\ModelAI\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\ModelAI\Models\ModelCategory;

class ModelCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            // ModelAI Categories
            [
                'key' => 'text-generation',
                'name' => 'Text Generation',
                'description' => 'AI models for generating text content',
                'type' => 'ModelAI',
                'icon' => 'fas fa-pen',
                'color' => '#3B82F6',
                'sort_order' => 1,
                'status' => 'active',
            ],
            [
                'key' => 'image-generation',
                'name' => 'Image Generation',
                'description' => 'AI models for creating and editing images',
                'type' => 'ModelAI',
                'icon' => 'fas fa-image',
                'color' => '#10B981',
                'sort_order' => 2,
                'status' => 'active',
            ],
            [
                'key' => 'code-generation',
                'name' => 'Code Generation',
                'description' => 'AI models specialized in code generation and programming',
                'type' => 'ModelAI',
                'icon' => 'fas fa-code',
                'color' => '#8B5CF6',
                'sort_order' => 3,
                'status' => 'active',
            ],
            [
                'key' => 'multimodal',
                'name' => 'Multimodal',
                'description' => 'AI models that can process multiple types of input',
                'type' => 'ModelAI',
                'icon' => 'fas fa-layer-group',
                'color' => '#F59E0B',
                'sort_order' => 4,
                'status' => 'active',
            ],

            // Tools Categories
            [
                'key' => 'web-tools',
                'name' => 'Web Tools',
                'description' => 'Tools for web browsing and search',
                'type' => 'Tools',
                'icon' => 'fas fa-globe',
                'color' => '#06B6D4',
                'sort_order' => 1,
                'status' => 'active',
            ],
            [
                'key' => 'code-tools',
                'name' => 'Code Tools',
                'description' => 'Tools for code execution and development',
                'type' => 'Tools',
                'icon' => 'fas fa-terminal',
                'color' => '#EF4444',
                'sort_order' => 2,
                'status' => 'active',
            ],
            [
                'key' => 'data-tools',
                'name' => 'Data Tools',
                'description' => 'Tools for data analysis and processing',
                'type' => 'Tools',
                'icon' => 'fas fa-chart-bar',
                'color' => '#84CC16',
                'sort_order' => 3,
                'status' => 'active',
            ],
            [
                'key' => 'integration-tools',
                'name' => 'Integration Tools',
                'description' => 'Tools for third-party integrations',
                'type' => 'Tools',
                'icon' => 'fas fa-plug',
                'color' => '#F97316',
                'sort_order' => 4,
                'status' => 'active',
            ],
        ];

        foreach ($categories as $category) {
            ModelCategory::updateOrCreate(
                ['key' => $category['key']],
                $category
            );
        }
    }
}
