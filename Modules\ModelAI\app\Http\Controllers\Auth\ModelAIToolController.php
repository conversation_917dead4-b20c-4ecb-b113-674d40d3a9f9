<?php

namespace Modules\ModelAI\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Core\Traits\ResponseTrait;
use Modules\ModelAI\Http\Filters\ModelAIToolFilter;
use Modules\ModelAI\Http\Requests\ModelAIToolRequest;
use Modules\ModelAI\Models\ModelAI;
use Modules\ModelAI\Models\ModelAITool;
use Modules\ModelAI\Models\ModelTool;

class ModelAIToolController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|model-ai-tool.view')->only(['index', 'show']);
        $this->middleware('role_or_permission:super-admin|model-ai-tool.create')->only(['store', 'attachTools']);
        $this->middleware('role_or_permission:super-admin|model-ai-tool.edit')->only(['update', 'toggleTool']);
        $this->middleware('role_or_permission:super-admin|model-ai-tool.delete')->only(['detachTools']);
        $this->middleware('role_or_permission:super-admin|model-ai-tool.destroy')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $aiTools = ModelAITool::query()
            ->with(['modelAI', 'tool'])
            ->filter(new ModelAIToolFilter($request))
            ->orderedByPriority()
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($aiTools, __('Model-tool configurations retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ModelAIToolRequest $request): JsonResponse
    {
        try {
            $aiTool = ModelAITool::create($request->validated());
            $aiTool->load(['modelAI', 'tool']);
            
            return $this->successResponse($aiTool, __('Model-tool configuration created successfully.'), 201);
        } catch (\Exception $e) {
            return $this->errorResponse(__('Failed to create model-tool configuration.'), 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(ModelAITool $modelAITool): JsonResponse
    {
        $modelAITool->load(['modelAI', 'tool']);

        return $this->successResponse($modelAITool, __('Model-tool configuration retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ModelAIToolRequest $request, ModelAITool $modelAITool): JsonResponse
    {
        try {
            $modelAITool->update($request->validated());
            $modelAITool->load(['modelAI', 'tool']);
            
            return $this->successResponse($modelAITool, __('Model-tool configuration updated successfully.'));
        } catch (\Exception $e) {
            return $this->errorResponse(__('Failed to update model-tool configuration.'), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ModelAITool $modelAITool): JsonResponse
    {
        try {
            $modelAITool->delete();
            return $this->successResponse(null, __('Model-tool configuration deleted successfully.'));
        } catch (\Exception $e) {
            return $this->errorResponse(__('Failed to delete model-tool configuration.'), 500);
        }
    }

    /**
     * Attach tools to a model.
     */
    public function attachTools(Request $request, ModelAI $modelAI): JsonResponse
    {
        $request->validate([
            'tools' => 'required|array',
            'tools.*.tool_id' => 'required|integer|exists:model_tools,id',
            'tools.*.is_enabled' => 'boolean',
            'tools.*.configuration' => 'nullable|array',
            'tools.*.priority' => 'integer|min:0',
            'tools.*.max_usage_per_request' => 'nullable|integer|min:1',
            'tools.*.rate_limit_per_minute' => 'nullable|integer|min:1',
        ]);

        DB::beginTransaction();
        
        try {
            foreach ($request->tools as $toolData) {
                ModelAITool::updateOrCreate(
                    [
                        'model_ai_id' => $modelAI->id,
                        'model_tool_id' => $toolData['tool_id'],
                    ],
                    [
                        'is_enabled' => $toolData['is_enabled'] ?? true,
                        'configuration' => $toolData['configuration'] ?? null,
                        'priority' => $toolData['priority'] ?? 0,
                        'max_usage_per_request' => $toolData['max_usage_per_request'] ?? null,
                        'rate_limit_per_minute' => $toolData['rate_limit_per_minute'] ?? null,
                    ]
                );
            }
            
            DB::commit();
            
            return $this->successResponse(null, __('Tools attached to model successfully.'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse(__('Failed to attach tools to model.'), 500);
        }
    }

    /**
     * Detach tools from a model.
     */
    public function detachTools(Request $request, ModelAI $modelAI): JsonResponse
    {
        $request->validate([
            'tool_ids' => 'required|array',
            'tool_ids.*' => 'required|integer|exists:model_tools,id',
        ]);

        try {
            ModelAITool::where('model_ai_id', $modelAI->id)
                       ->whereIn('model_tool_id', $request->tool_ids)
                       ->delete();
            
            return $this->successResponse(null, __('Tools detached from model successfully.'));
        } catch (\Exception $e) {
            return $this->errorResponse(__('Failed to detach tools from model.'), 500);
        }
    }

    /**
     * Get tools for a specific model.
     */
    public function modelTools(ModelAI $modelAI): JsonResponse
    {
        $tools = $modelAI->tools()
                         ->with(['categories'])
                         ->wherePivot('is_enabled', true)
                         ->orderByPivot('priority', 'desc')
                         ->get();

        return $this->successResponse($tools, __('Model tools retrieved successfully.'));
    }

    /**
     * Get models that can use a specific tool.
     */
    public function toolModels(ModelTool $modelTool): JsonResponse
    {
        $models = $modelTool->modelAIs()
                           ->with(['categories', 'service'])
                           ->wherePivot('is_enabled', true)
                           ->orderByPivot('priority', 'desc')
                           ->get();

        return $this->successResponse($models, __('Tool models retrieved successfully.'));
    }

    /**
     * Toggle tool status for a model.
     */
    public function toggleTool(Request $request, ModelAI $modelAI, ModelTool $modelTool): JsonResponse
    {
        $request->validate([
            'is_enabled' => 'required|boolean',
        ]);

        try {
            $aiTool = ModelAITool::where('model_ai_id', $modelAI->id)
                                 ->where('model_tool_id', $modelTool->id)
                                 ->first();

            if (!$aiTool) {
                return $this->errorResponse(__('Model-tool configuration not found.'), 404);
            }

            $aiTool->update(['is_enabled' => $request->is_enabled]);
            
            return $this->successResponse($aiTool, __('Tool status updated successfully.'));
        } catch (\Exception $e) {
            return $this->errorResponse(__('Failed to update tool status.'), 500);
        }
    }
}
