<?php

namespace Modules\Location\Services;

use Modules\Location\Models\Country;
use Modules\Location\Models\GeoDivision;
use Illuminate\Support\Collection;

class LocationService
{
    /**
     * Retrieve a list of active countries for dropdowns.
     *
     * @return Collection
     */
    public function getActiveCountriesForDropdown(): Collection
    {
        return Country::query()
            ->active()
            ->orderBy('name')
            ->select(['id', 'name', 'native_name', 'iso_code_2', 'iso_code_3', 'emoji'])
            ->get();
    }

    /**
     * Get states/provinces for a specific country.
     *
     * @param int $countryId
     * @return Collection
     */
    public function getStatesForCountry(int $countryId): Collection
    {
        return GeoDivision::query()
            ->active()
            ->country($countryId)
            ->type('state')
            ->level(1)
            ->whereNull('parent_id')
            ->orderBy('name')
            ->select(['id', 'name', 'native_name', 'code', 'country_id'])
            ->get();
    }

    /**
     * Get cities for a specific state/province.
     *
     * @param int $stateId
     * @return Collection
     */
    public function getCitiesForState(int $stateId): Collection
    {
        return GeoDivision::query()
            ->active()
            ->parent($stateId)
            ->type('city')
            ->orderBy('name')
            ->select(['id', 'name', 'native_name', 'code', 'country_id', 'parent_id'])
            ->get();
    }

    /**
     * Get districts for a specific city.
     *
     * @param int $cityId
     * @return Collection
     */
    public function getDistrictsForCity(int $cityId): Collection
    {
        return GeoDivision::query()
            ->active()
            ->parent($cityId)
            ->type('district')
            ->orderBy('name')
            ->select(['id', 'name', 'native_name', 'code', 'country_id', 'parent_id'])
            ->get();
    }

    /**
     * Get all geographic divisions for a specific country.
     *
     * @param int $countryId
     * @param string|null $type
     * @return Collection
     */
    public function getGeoDivisionsForCountry(int $countryId, ?string $type = null): Collection
    {
        $query = GeoDivision::query()
            ->active()
            ->country($countryId)
            ->orderBy('level')
            ->orderBy('name');

        if ($type) {
            $query->type($type);
        }

        return $query->select(['id', 'name', 'native_name', 'code', 'type', 'level', 'parent_id', 'country_id'])
            ->get();
    }

    /**
     * Find a country by its ISO code.
     *
     * @param string $isoCode
     * @return Country|null
     */
    public function findCountryByIsoCode(string $isoCode): ?Country
    {
        return Country::where('iso_code_2', $isoCode)
            ->orWhere('iso_code_3', $isoCode)
            ->first();
    }

    /**
     * Find a geographic division by its ID.
     *
     * @param int $id
     * @return GeoDivision|null
     */
    public function findGeoDivisionById(int $id): ?GeoDivision
    {
        return GeoDivision::find($id);
    }

    /**
     * Get hierarchical location data for cascading dropdowns.
     *
     * @param int|null $countryId
     * @param int|null $stateId
     * @param int|null $cityId
     * @return array
     */
    public function getHierarchicalData(?int $countryId = null, ?int $stateId = null, ?int $cityId = null): array
    {
        $data = [
            'countries' => $this->getActiveCountriesForDropdown(),
            'states' => collect(),
            'cities' => collect(),
            'districts' => collect(),
        ];

        if ($countryId) {
            $data['states'] = $this->getStatesForCountry($countryId);
        }

        if ($stateId) {
            $data['cities'] = $this->getCitiesForState($stateId);
        }

        if ($cityId) {
            $data['districts'] = $this->getDistrictsForCity($cityId);
        }

        return $data;
    }

    /**
     * Search locations by name.
     *
     * @param string $query
     * @param string|null $type
     * @param int|null $countryId
     * @param int $limit
     * @return Collection
     */
    public function searchLocations(string $query, ?string $type = null, ?int $countryId = null, int $limit = 20): Collection
    {
        // Search countries
        $countries = Country::query()
            ->active()
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('native_name', 'like', "%{$query}%");
            })
            ->when($countryId, function ($q) use ($countryId) {
                $q->where('id', $countryId);
            })
            ->limit($limit)
            ->get()
            ->map(function ($country) {
                return [
                    'id' => $country->id,
                    'name' => $country->name,
                    'native_name' => $country->native_name,
                    'type' => 'country',
                    'full_name' => $country->name,
                ];
            });

        // Search geo divisions
        $divisions = GeoDivision::query()
            ->active()
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('native_name', 'like', "%{$query}%");
            })
            ->when($type, function ($q) use ($type) {
                $q->type($type);
            })
            ->when($countryId, function ($q) use ($countryId) {
                $q->country($countryId);
            })
            ->with(['country', 'parent'])
            ->limit($limit)
            ->get()
            ->map(function ($division) {
                return [
                    'id' => $division->id,
                    'name' => $division->name,
                    'native_name' => $division->native_name,
                    'type' => $division->type,
                    'full_name' => $division->full_name,
                    'country' => $division->country->name,
                ];
            });

        return $countries->concat($divisions)->take($limit);
    }


}
