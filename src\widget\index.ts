/**
 * ProcMS Chatbot Widget
 * Main entry point for widget and iframe integration
 */

import { createApp, App as VueApp } from 'vue';
import ChatWidget from './components/ChatWidget.vue';
import { WidgetConfig, BotConfig } from './types';
import { getBotConfiguration } from './api/bot-api';
import './styles/widget.scss';

export class ProcmsChatbotWidget {
  private app: VueApp | null = null;
  private container: HTMLElement | null = null;
  private config: WidgetConfig;
  private botConfig: BotConfig | null = null;
  private mounted = false;

  constructor(config: WidgetConfig) {
    this.config = {
      mode: 'widget', // 'widget' | 'iframe'
      theme: 'light',
      position: 'bottom-right',
      showHeader: true,
      showAvatar: true,
      autoOpen: false,
      ...config
    };

    // Validate required config
    if (!this.config.botUuid) {
      throw new Error('Bot UUID is required');
    }

    if (!this.config.apiKey) {
      throw new Error('API Key is required');
    }
  }

  /**
   * Initialize and mount the widget
   */
  async mount(selector: string | HTMLElement): Promise<void> {
    try {
      // Get container element
      this.container = typeof selector === 'string' 
        ? document.querySelector(selector)
        : selector;

      if (!this.container) {
        throw new Error(`Container not found: ${selector}`);
      }

      // Load bot configuration
      await this.loadBotConfiguration();

      // Decide between widget or iframe mode
      if (this.config.mode === 'iframe' || this.shouldUseIframe()) {
        this.mountIframe();
      } else {
        this.mountWidget();
      }

      this.mounted = true;
      this.emit('mounted');
    } catch (error) {
      console.error('Failed to mount ProcMS Chatbot:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Load bot configuration from API
   */
  private async loadBotConfiguration(): Promise<void> {
    try {
      const response = await getBotConfiguration(this.config.botUuid!, this.config.apiKey!);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to load bot configuration');
      }

      this.botConfig = response.data;
      
      // Validate bot status
      if (this.botConfig?.status !== 'active') {
        throw new Error(`Bot is not active. Current status: ${this.botConfig?.status}`);
      }

      this.emit('bot-loaded', this.botConfig);
    } catch (error) {
      console.error('Failed to load bot configuration:', error);
      throw error;
    }
  }

  /**
   * Mount as JavaScript widget
   */
  private mountWidget(): void {
    if (!this.container || !this.botConfig) return;

    // Create Vue app
    this.app = createApp(ChatWidget, {
      config: this.config,
      botConfig: this.botConfig,
      onMessage: (data: any) => this.emit('message', data),
      onError: (error: any) => this.emit('error', error),
      onClose: () => this.emit('close'),
      onOpen: () => this.emit('open')
    });

    // Mount to container
    this.app.mount(this.container);
  }

  /**
   * Mount as iframe
   */
  private mountIframe(): void {
    if (!this.container) return;

    const iframe = document.createElement('iframe');
    const params = new URLSearchParams({
      bot_uuid: this.config.botUuid!,
      api_key: this.config.apiKey!,
      theme: this.config.theme || 'light',
      position: this.config.position || 'bottom-right',
      show_header: this.config.showHeader ? '1' : '0',
      show_avatar: this.config.showAvatar ? '1' : '0',
      auto_open: this.config.autoOpen ? '1' : '0',
      user_id: this.config.userId || '',
      domain: window.location.hostname
    });

    iframe.src = `${this.config.iframeUrl || 'https://widget.procms.com/chat'}?${params.toString()}`;
    iframe.style.cssText = `
      width: 100%;
      height: 100%;
      border: none;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    `;

    // Handle iframe messages
    window.addEventListener('message', (event) => {
      if (event.origin !== (this.config.iframeUrl || 'https://widget.procms.com')) return;
      
      const { type, data } = event.data;
      this.emit(type, data);
    });

    this.container.appendChild(iframe);
  }

  /**
   * Determine if should use iframe mode
   */
  private shouldUseIframe(): boolean {
    // Force iframe for certain conditions
    return (
      this.config.forceIframe ||
      this.detectConflicts() ||
      !this.isModernBrowser()
    );
  }

  /**
   * Detect potential conflicts
   */
  private detectConflicts(): boolean {
    // Check for Vue conflicts
    if (window.Vue && window.Vue.version && !window.Vue.version.startsWith('3')) {
      return true;
    }

    // Check for CSS framework conflicts
    const hasBootstrap = !!document.querySelector('link[href*="bootstrap"]');
    const hasTailwind = !!document.querySelector('script[src*="tailwind"]');
    
    return hasBootstrap || hasTailwind;
  }

  /**
   * Check browser compatibility
   */
  private isModernBrowser(): boolean {
    return !!(
      window.fetch &&
      window.Promise &&
      window.Map &&
      window.Set
    );
  }

  /**
   * Unmount the widget
   */
  unmount(): void {
    if (!this.mounted) return;

    if (this.app) {
      this.app.unmount();
      this.app = null;
    }

    if (this.container) {
      this.container.innerHTML = '';
    }

    this.mounted = false;
    this.emit('unmounted');
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<WidgetConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.mounted) {
      // Re-mount with new config
      this.unmount();
      this.mount(this.container!);
    }
  }

  /**
   * Get current bot configuration
   */
  getBotConfig(): BotConfig | null {
    return this.botConfig;
  }

  /**
   * Check if widget is mounted
   */
  isMounted(): boolean {
    return this.mounted;
  }

  /**
   * Event emitter
   */
  private listeners: { [key: string]: Function[] } = {};

  on(event: string, callback: Function): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  off(event: string, callback?: Function): void {
    if (!this.listeners[event]) return;
    
    if (callback) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    } else {
      this.listeners[event] = [];
    }
  }

  private emit(event: string, data?: any): void {
    if (!this.listeners[event]) return;
    
    this.listeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in event listener for ${event}:`, error);
      }
    });
  }
}

// Static methods for easy integration
export const ProcmsChatbot = {
  /**
   * Create and mount widget in one call
   */
  async create(config: WidgetConfig, selector: string | HTMLElement): Promise<ProcmsChatbotWidget> {
    const widget = new ProcmsChatbotWidget(config);
    await widget.mount(selector);
    return widget;
  },

  /**
   * Create iframe embed code
   */
  generateIframeCode(config: WidgetConfig): string {
    const params = new URLSearchParams({
      bot_uuid: config.botUuid!,
      api_key: config.apiKey!,
      theme: config.theme || 'light',
      position: config.position || 'bottom-right'
    });

    return `<iframe 
      src="${config.iframeUrl || 'https://widget.procms.com/chat'}?${params.toString()}"
      width="400" 
      height="600" 
      frameborder="0"
      style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
    </iframe>`;
  }
};

// Export types
export * from './types';

// Global registration for script tag usage
if (typeof window !== 'undefined') {
  (window as any).ProcmsChatbotWidget = ProcmsChatbotWidget;
  (window as any).ProcmsChatbot = ProcmsChatbot;
}
