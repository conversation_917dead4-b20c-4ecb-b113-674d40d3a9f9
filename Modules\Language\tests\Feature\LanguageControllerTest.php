<?php

namespace Modules\Language\Tests\Feature;

use Tests\TestCase;
use Modules\Language\Models\Language;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;

class LanguageControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations for Language module
        $this->artisan('migrate', ['--path' => 'Modules/Language/database/migrations']);
    }

    #[Test]
    public function it_can_get_active_languages_list()
    {
        // Create test languages
        Language::factory()->create([
            'code' => 'en',
            'name' => 'English',
            'native_name' => 'English',
            'direction' => 'ltr',
            'is_default' => 1,
            'status' => 'active'
        ]);

        Language::factory()->create([
            'code' => 'es',
            'name' => 'Spanish',
            'native_name' => 'Español',
            'direction' => 'ltr',
            'is_default' => 0,
            'status' => 'active'
        ]);

        Language::factory()->create([
            'code' => 'fr',
            'name' => 'French',
            'native_name' => 'Français',
            'direction' => 'ltr',
            'is_default' => 0,
            'status' => 'inactive'
        ]);

        $response = $this->getJson('/api/v1/languages');

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'code',
                    'name',
                    'native_name',
                    'direction',
                    'is_default'
                ]
            ]
        ]);

        $data = $response->json('data');
        $this->assertCount(2, $data); // Only active languages

        // Verify languages are ordered by name
        $this->assertEquals('English', $data[0]['name']);
        $this->assertEquals('Spanish', $data[1]['name']);
    }

    #[Test]
    public function it_returns_empty_array_when_no_active_languages()
    {
        // Create only inactive languages
        Language::factory()->create(['status' => 'inactive']);

        $response = $this->getJson('/api/v1/languages');

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $data = $response->json('data');
        $this->assertCount(0, $data);
    }

    #[Test]
    public function it_can_get_default_language()
    {
        // Create non-default language
        Language::factory()->create([
            'code' => 'es',
            'is_default' => 0,
            'status' => 'active'
        ]);

        // Create default language
        $defaultLanguage = Language::factory()->create([
            'code' => 'en',
            'name' => 'English',
            'is_default' => 1,
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/v1/languages/default');

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $data = $response->json('data');
        $this->assertEquals($defaultLanguage->code, $data['code']);
        $this->assertEquals($defaultLanguage->name, $data['name']);
        $this->assertEquals(1, $data['is_default']);
    }

    #[Test]
    public function it_returns_null_when_no_default_language()
    {
        Language::factory()->create(['is_default' => 0, 'status' => 'active']);

        $response = $this->getJson('/api/v1/languages/default');

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $data = $response->json('data');
        $this->assertNull($data);
    }

    #[Test]
    public function it_can_show_language_by_code()
    {
        $language = Language::factory()->create([
            'code' => 'en',
            'name' => 'English',
            'native_name' => 'English',
            'flag' => 'us',
            'direction' => 'ltr',
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/v1/languages/en');

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $data = $response->json('data');
        $this->assertEquals($language->code, $data['code']);
        $this->assertEquals($language->name, $data['name']);
        $this->assertEquals($language->native_name, $data['native_name']);
        $this->assertEquals($language->flag, $data['flag']);
        $this->assertEquals($language->direction, $data['direction']);
    }

    #[Test]
    public function it_returns_null_for_nonexistent_language_code()
    {
        $response = $this->getJson('/api/v1/languages/nonexistent');

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $data = $response->json('data');
        $this->assertNull($data);
    }

    #[Test]
    public function it_can_show_inactive_language_by_code()
    {
        $language = Language::factory()->create([
            'code' => 'es',
            'status' => 'inactive'
        ]);

        $response = $this->getJson('/api/v1/languages/es');

        $response->assertStatus(200);
        $this->assertJsonResponseStructure($response);

        $data = $response->json('data');
        $this->assertEquals($language->code, $data['code']);
        $this->assertEquals('inactive', $data['status']);
    }

    #[Test]
    public function it_does_not_show_soft_deleted_language()
    {
        $language = Language::factory()->create(['code' => 'en']);
        $language->delete(); // Soft delete

        $response = $this->getJson('/api/v1/languages/en');

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertNull($data);
    }

    #[Test]
    public function it_handles_special_characters_in_language_code()
    {
        $language = Language::factory()->create(['code' => 'zh-CN']);

        $response = $this->getJson('/api/v1/languages/zh-CN');

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals('zh-CN', $data['code']);
    }

    #[Test]
    public function it_returns_consistent_response_format()
    {
        Language::factory()->create(['status' => 'active']);

        $response = $this->getJson('/api/v1/languages');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data'
        ]);

        $this->assertTrue($response->json('success'));
        $this->assertIsString($response->json('message'));
        $this->assertIsArray($response->json('data'));
    }

    #[Test]
    public function it_uses_cache_for_active_languages()
    {
        Language::factory()->create(['status' => 'active']);

        // First request
        $response1 = $this->getJson('/api/v1/languages');
        $response1->assertStatus(200);

        // Second request should use cache
        $response2 = $this->getJson('/api/v1/languages');
        $response2->assertStatus(200);

        // Responses should be identical
        $this->assertEquals($response1->json(), $response2->json());
    }

    #[Test]
    public function it_handles_unicode_content_correctly()
    {
        Language::factory()->create([
            'code' => 'ar',
            'name' => 'Arabic',
            'native_name' => 'العربية',
            'direction' => 'rtl',
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/v1/languages');

        $response->assertStatus(200);
        $data = $response->json('data');

        $arabicLanguage = collect($data)->firstWhere('code', 'ar');
        $this->assertEquals('العربية', $arabicLanguage['native_name']);
        $this->assertEquals('rtl', $arabicLanguage['direction']);
    }

    #[Test]
    public function it_excludes_hidden_attributes_from_response()
    {
        Language::factory()->create(['status' => 'active']);

        $response = $this->getJson('/api/v1/languages');

        $response->assertStatus(200);
        $data = $response->json('data');

        if (!empty($data)) {
            $firstLanguage = $data[0];
            $this->assertArrayNotHasKey('deleted_at', $firstLanguage);
        }
    }

    #[Test]
    public function it_includes_required_fields_in_dropdown_response()
    {
        Language::factory()->create([
            'code' => 'en',
            'name' => 'English',
            'native_name' => 'English',
            'direction' => 'ltr',
            'is_default' => 1,
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/v1/languages');

        $response->assertStatus(200);
        $data = $response->json('data');

        $this->assertCount(1, $data);
        $language = $data[0];

        $requiredFields = ['code', 'name', 'native_name', 'direction', 'is_default'];
        foreach ($requiredFields as $field) {
            $this->assertArrayHasKey($field, $language);
        }
    }

    #[Test]
    public function it_handles_route_model_binding_correctly()
    {
        $language = Language::factory()->create(['code' => 'en-US']);

        $response = $this->getJson('/api/v1/languages/en-US');

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals('en-US', $data['code']);
    }
}
