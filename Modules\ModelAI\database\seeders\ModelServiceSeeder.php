<?php

namespace Modules\ModelAI\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\ModelAI\Models\ModelAI;
use Modules\ModelAI\Models\ModelService;

class ModelServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            [
                'model_key' => 'gpt-4-turbo',
                'input_types' => ['text', 'image'],
                'output_types' => ['text'],
                'supported_sources' => [
                    ['type' => 'upload'],
                    ['type' => 'url'],
                ],
                'cost_per_request' => 0,
                'cost_per1k_tokens' => 0.03,
                'billing_type' => 'per_token',
                'cost_per1k_input' => 0.01,
                'cost_per1k_output' => 0.03,
                'max_tokens' => 4096,
                'context_window' => 128000,
                'rate_limit_rpm' => 500,
                'timeout_seconds' => 120,
                'default_parameters' => [
                    'temperature' => 0.7,
                    'top_p' => 1.0,
                    'max_tokens' => 1000,
                    'presence_penalty' => 0.0,
                    'frequency_penalty' => 0.0,
                ],
                'allowed_parameters' => [
                    'temperature', 'top_p', 'max_tokens', 'presence_penalty', 'frequency_penalty'
                ],
                'priority' => 100,
                'notes' => 'Primary GPT-4 Turbo service configuration',
                'status' => 'active',
            ],
            [
                'model_key' => 'claude-3-sonnet',
                'input_types' => ['text', 'image'],
                'output_types' => ['text'],
                'supported_sources' => [
                    ['type' => 'upload'],
                ],
                'cost_per_request' => 0,
                'cost_per1k_tokens' => 0.015,
                'billing_type' => 'per_token',
                'cost_per1k_input' => 0.003,
                'cost_per1k_output' => 0.015,
                'max_tokens' => 4096,
                'context_window' => 200000,
                'rate_limit_rpm' => 300,
                'timeout_seconds' => 120,
                'default_parameters' => [
                    'temperature' => 0.7,
                    'max_tokens' => 1000,
                    'top_p' => 1.0,
                    'top_k' => 40,
                ],
                'allowed_parameters' => [
                    'temperature', 'max_tokens', 'top_p', 'top_k'
                ],
                'priority' => 90,
                'notes' => 'Claude 3 Sonnet service configuration',
                'status' => 'active',
            ],
            [
                'model_key' => 'dall-e-3',
                'input_types' => ['text'],
                'output_types' => ['image'],
                'supported_sources' => [],
                'cost_per_request' => 100,
                'cost_per1k_tokens' => 0,
                'billing_type' => 'per_request',
                'cost_per1k_input' => 0,
                'cost_per1k_output' => 0,
                'max_tokens' => 0,
                'context_window' => 0,
                'rate_limit_rpm' => 50,
                'timeout_seconds' => 60,
                'default_parameters' => [
                    'size' => '1024x1024',
                    'quality' => 'standard',
                    'style' => 'vivid',
                ],
                'allowed_parameters' => [
                    'size', 'quality', 'style'
                ],
                'priority' => 80,
                'notes' => 'DALL-E 3 image generation service',
                'status' => 'active',
            ],
            [
                'model_key' => 'codellama-34b',
                'input_types' => ['text'],
                'output_types' => ['text'],
                'supported_sources' => [],
                'cost_per_request' => 0,
                'cost_per1k_tokens' => 0.001,
                'billing_type' => 'per_token',
                'cost_per1k_input' => 0.0005,
                'cost_per1k_output' => 0.001,
                'max_tokens' => 2048,
                'context_window' => 16384,
                'rate_limit_rpm' => 100,
                'timeout_seconds' => 90,
                'default_parameters' => [
                    'temperature' => 0.1,
                    'max_tokens' => 1000,
                    'top_p' => 1.0,
                ],
                'allowed_parameters' => [
                    'temperature', 'max_tokens', 'top_p'
                ],
                'priority' => 70,
                'notes' => 'CodeLlama 34B service for code generation',
                'status' => 'active',
            ],
        ];

        foreach ($services as $serviceData) {
            $modelKey = $serviceData['model_key'];
            unset($serviceData['model_key']);

            $model = ModelAI::where('key', $modelKey)->first();
            if ($model) {
                ModelService::updateOrCreate(
                    ['model_ai_id' => $model->id],
                    array_merge($serviceData, ['model_ai_id' => $model->id])
                );
            }
        }
    }
}
