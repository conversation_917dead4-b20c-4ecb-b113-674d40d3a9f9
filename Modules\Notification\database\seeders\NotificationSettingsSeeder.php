<?php

namespace Modules\Notification\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Setting\Models\Setting;
use Modules\Setting\Models\SettingGroup;

class NotificationSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Notification settings group
        $notificationGroup = SettingGroup::updateOrCreate(
            ['key' => 'notifications'],
            [
                'label' => 'Notification Settings',
                'description' => 'Notification system configuration and channel settings',
                'icon' => 'fas fa-bell',
                'sort_order' => 10,
            ]
        );

        // Global notification settings
        Setting::updateOrCreate(
            ['key' => 'enabled'],
            [
                'value' => true,
                'type' => 'boolean',
                'input_type' => 'switch',
                'group_id' => $notificationGroup->id,
                'label' => 'Enable Notifications',
                'description' => 'Enable or disable the entire notification system',
                'is_public' => false,
                'sort_order' => 1,
                'validation_rules' => 'required|boolean',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'queue.enabled'],
            [
                'value' => true,
                'type' => 'boolean',
                'input_type' => 'switch',
                'group_id' => $notificationGroup->id,
                'label' => 'Enable Queue Processing',
                'description' => 'Process notifications asynchronously using queues',
                'is_public' => false,
                'sort_order' => 2,
                'validation_rules' => 'required|boolean',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'queue.connection'],
            [
                'value' => 'database',
                'type' => 'string',
                'input_type' => 'select',
                'group_id' => $notificationGroup->id,
                'label' => 'Queue Connection',
                'description' => 'Queue connection to use for notification processing',
                'options' => json_encode([
                    'redis' => 'Redis',
                    'database' => 'Database',
                    'sync' => 'Sync (No Queue)',
                ]),
                'is_public' => false,
                'sort_order' => 3,
                'validation_rules' => 'required|string|in:redis,database,sync',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'queue.name'],
            [
                'value' => 'notifications',
                'type' => 'string',
                'input_type' => 'text',
                'group_id' => $notificationGroup->id,
                'label' => 'Queue Name',
                'description' => 'Name of the queue for notification processing',
                'is_public' => false,
                'sort_order' => 4,
                'validation_rules' => 'required|string|max:50',
            ]
        );

        // Create Email settings group
        $emailGroup = SettingGroup::updateOrCreate(
            ['key' => 'email'],
            [
                'label' => 'Email Notifications',
                'description' => 'Email notification channel configuration',
                'icon' => 'fas fa-envelope',
                'sort_order' => 11,
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'email.enabled'],
            [
                'value' => true,
                'type' => 'boolean',
                'input_type' => 'switch',
                'group_id' => $notificationGroup->id,
                'label' => 'Enable Email Notifications',
                'description' => 'Enable or disable email notification channel',
                'is_public' => false,
                'sort_order' => 10,
                'validation_rules' => 'required|boolean',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'email.queue'],
            [
                'value' => true,
                'type' => 'boolean',
                'input_type' => 'switch',
                'group_id' => $emailGroup->id,
                'label' => 'Queue Email Notifications',
                'description' => 'Process email notifications through queue',
                'is_public' => false,
                'sort_order' => 2,
                'validation_rules' => 'required|boolean',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'email.from.address'],
            [
                'value' => '<EMAIL>',
                'type' => 'string',
                'input_type' => 'email',
                'group_id' => $emailGroup->id,
                'label' => 'From Email Address',
                'description' => 'Default sender email address for notifications',
                'is_public' => false,
                'sort_order' => 3,
                'validation_rules' => 'required|email|max:255',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'email.from.name'],
            [
                'value' => 'Laravel ProCMS',
                'type' => 'string',
                'input_type' => 'text',
                'group_id' => $emailGroup->id,
                'label' => 'From Name',
                'description' => 'Default sender name for notifications',
                'is_public' => false,
                'sort_order' => 4,
                'validation_rules' => 'required|string|max:255',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'email.reply.to'],
            [
                'value' => '<EMAIL>',
                'type' => 'string',
                'input_type' => 'email',
                'group_id' => $emailGroup->id,
                'label' => 'Reply To Email',
                'description' => 'Reply-to email address for notifications',
                'is_public' => false,
                'sort_order' => 5,
                'validation_rules' => 'nullable|email|max:255',
            ]
        );

        // SMTP Configuration
        Setting::updateOrCreate(
            ['key' => 'smtp.host'],
            [
                'value' => 'smtp.yandex.com',
                'type' => 'string',
                'input_type' => 'text',
                'group_id' => $emailGroup->id,
                'label' => 'SMTP Host',
                'description' => 'SMTP server hostname (e.g., smtp.yandex.com, smtp.gmail.com)',
                'is_public' => false,
                'sort_order' => 6,
                'validation_rules' => 'required|string|max:255',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'smtp.port'],
            [
                'value' => 587,
                'type' => 'integer',
                'input_type' => 'number',
                'group_id' => $emailGroup->id,
                'label' => 'SMTP Port',
                'description' => 'SMTP server port (587 for TLS, 465 for SSL, 25 for plain)',
                'is_public' => false,
                'sort_order' => 7,
                'validation_rules' => 'required|integer|min:1|max:65535',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'smtp.encryption'],
            [
                'value' => 'tls',
                'type' => 'string',
                'input_type' => 'select',
                'group_id' => $emailGroup->id,
                'label' => 'SMTP Encryption',
                'description' => 'SMTP encryption method',
                'options' => json_encode([
                    'tls' => 'TLS',
                    'ssl' => 'SSL',
                    'none' => 'None',
                ]),
                'is_public' => false,
                'sort_order' => 8,
                'validation_rules' => 'required|string|in:tls,ssl,none',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'smtp.username'],
            [
                'value' => '<EMAIL>',
                'type' => 'string',
                'input_type' => 'text',
                'group_id' => $emailGroup->id,
                'label' => 'SMTP Username',
                'description' => 'SMTP authentication username (usually your email address)',
                'is_public' => false,
                'sort_order' => 9,
                'validation_rules' => 'nullable|string|max:255',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'smtp.password'],
            [
                'value' => 'kwmpuavwgmrpkrvx',
                'type' => 'string',
                'input_type' => 'password',
                'group_id' => $emailGroup->id,
                'label' => 'SMTP Password',
                'description' => 'SMTP authentication password (use app password for Gmail/Yandex)',
                'is_public' => false,
                'sort_order' => 10,
                'validation_rules' => 'nullable|string|max:255',
            ]
        );

        // Create SMS settings group
        $smsGroup = SettingGroup::updateOrCreate(
            ['key' => 'sms'],
            [
                'label' => 'SMS Notifications',
                'description' => 'SMS notification channel configuration',
                'icon' => 'fas fa-sms',
                'sort_order' => 12,
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'sms.enabled'],
            [
                'value' => false,
                'type' => 'boolean',
                'input_type' => 'switch',
                'group_id' => $notificationGroup->id,
                'label' => 'Enable SMS Notifications',
                'description' => 'Enable or disable SMS notification channel',
                'is_public' => false,
                'sort_order' => 11,
                'validation_rules' => 'required|boolean',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'sms_queue'],
            [
                'value' => true,
                'type' => 'boolean',
                'input_type' => 'switch',
                'group_id' => $smsGroup->id,
                'label' => 'Queue SMS Notifications',
                'description' => 'Process SMS notifications through queue',
                'is_public' => false,
                'sort_order' => 2,
                'validation_rules' => 'required|boolean',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'sms_provider'],
            [
                'value' => 'twilio',
                'type' => 'string',
                'input_type' => 'select',
                'group_id' => $smsGroup->id,
                'label' => 'SMS Provider',
                'description' => 'SMS service provider',
                'options' => json_encode([
                    'twilio' => 'Twilio',
                    'nexmo' => 'Nexmo/Vonage',
                    'aws_sns' => 'AWS SNS',
                    'log' => 'Log (Testing)',
                ]),
                'is_public' => false,
                'sort_order' => 3,
                'validation_rules' => 'required|string|in:twilio,nexmo,aws_sns,log',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'sms_max_length'],
            [
                'value' => 160,
                'type' => 'integer',
                'input_type' => 'number',
                'group_id' => $smsGroup->id,
                'label' => 'Max SMS Length',
                'description' => 'Maximum length for SMS messages (characters)',
                'is_public' => false,
                'sort_order' => 4,
                'validation_rules' => 'required|integer|min:50|max:1600',
            ]
        );

        // Twilio Settings
        Setting::updateOrCreate(
            ['key' => 'twilio_sid'],
            [
                'value' => env('TWILIO_SID', ''),
                'type' => 'string',
                'input_type' => 'text',
                'group_id' => $smsGroup->id,
                'label' => 'Twilio Account SID',
                'description' => 'Twilio Account SID for SMS sending',
                'is_public' => false,
                'sort_order' => 5,
                'validation_rules' => 'nullable|string|max:255',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'twilio_token'],
            [
                'value' => env('TWILIO_TOKEN', ''),
                'type' => 'string',
                'input_type' => 'password',
                'group_id' => $smsGroup->id,
                'label' => 'Twilio Auth Token',
                'description' => 'Twilio Auth Token for SMS sending',
                'is_public' => false,
                'sort_order' => 6,
                'validation_rules' => 'nullable|string|max:255',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'twilio_from'],
            [
                'value' => env('TWILIO_FROM', ''),
                'type' => 'string',
                'input_type' => 'text',
                'group_id' => $smsGroup->id,
                'label' => 'Twilio From Number',
                'description' => 'Twilio phone number for sending SMS',
                'is_public' => false,
                'sort_order' => 7,
                'validation_rules' => 'nullable|string|max:20',
            ]
        );

        // Create Push Notification settings group
        $pushGroup = SettingGroup::updateOrCreate(
            ['key' => 'push'],
            [
                'label' => 'Push Notifications',
                'description' => 'Push notification channel configuration',
                'icon' => 'fas fa-mobile-alt',
                'sort_order' => 13,
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'push.enabled'],
            [
                'value' => 'false',
                'type' => 'boolean',
                'input_type' => 'switch',
                'group_id' => $notificationGroup->id,
                'label' => 'Enable Push Notifications',
                'description' => 'Enable or disable push notification channel',
                'is_public' => false,
                'sort_order' => 12,
                'validation_rules' => 'required|boolean',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'push_queue'],
            [
                'value' => 'true',
                'type' => 'boolean',
                'input_type' => 'switch',
                'group_id' => $pushGroup->id,
                'label' => 'Queue Push Notifications',
                'description' => 'Process push notifications through queue',
                'is_public' => false,
                'sort_order' => 2,
                'validation_rules' => 'required|boolean',
            ]
        );

        // FCM Settings
        Setting::updateOrCreate(
            ['key' => 'fcm_server_key'],
            [
                'value' => env('FCM_SERVER_KEY', ''),
                'type' => 'string',
                'input_type' => 'password',
                'group_id' => $pushGroup->id,
                'label' => 'FCM Server Key',
                'description' => 'Firebase Cloud Messaging server key',
                'is_public' => false,
                'sort_order' => 3,
                'validation_rules' => 'nullable|string|max:500',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'fcm_sender_id'],
            [
                'value' => env('FCM_SENDER_ID', ''),
                'type' => 'string',
                'input_type' => 'text',
                'group_id' => $pushGroup->id,
                'label' => 'FCM Sender ID',
                'description' => 'Firebase Cloud Messaging sender ID',
                'is_public' => false,
                'sort_order' => 4,
                'validation_rules' => 'nullable|string|max:255',
            ]
        );

        // Create Telegram settings group
        $telegramGroup = SettingGroup::updateOrCreate(
            ['key' => 'telegram'],
            [
                'label' => 'Telegram Notifications',
                'description' => 'Telegram notification channel configuration',
                'icon' => 'fab fa-telegram',
                'sort_order' => 14,
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'telegram.enabled'],
            [
                'value' => false,
                'type' => 'boolean',
                'input_type' => 'switch',
                'group_id' => $notificationGroup->id,
                'label' => 'Enable Telegram Notifications',
                'description' => 'Enable or disable Telegram notification channel',
                'is_public' => false,
                'sort_order' => 13,
                'validation_rules' => 'required|boolean',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'database.enabled'],
            [
                'value' => true,
                'type' => 'boolean',
                'input_type' => 'switch',
                'group_id' => $notificationGroup->id,
                'label' => 'Enable Database Notifications',
                'description' => 'Enable or disable database notification channel',
                'is_public' => false,
                'sort_order' => 14,
                'validation_rules' => 'required|boolean',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'telegram.bot_token'],
            [
                'value' => env('TELEGRAM_BOT_TOKEN', ''),
                'type' => 'string',
                'input_type' => 'password',
                'group_id' => $telegramGroup->id,
                'label' => 'Telegram Bot Token',
                'description' => 'Telegram Bot API token',
                'is_public' => false,
                'sort_order' => 2,
                'validation_rules' => 'nullable|string|max:500',
            ]
        );

        $this->command->info('Notification settings seeded successfully.');
    }
}
