<?php

namespace Modules\ModelAI\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class ModelCategoryFilter extends AbstractFilter
{
    protected function filters(): array
    {
        return [
            'key' => 'like',
            'name' => 'like',
            'description' => 'like',
            'type' => 'exact',
            'icon' => 'exact',
            'color' => 'exact',
            'status' => 'exact',
            'sort_order' => 'exact',
            'is_trashed' => 'trashed',
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
            'updated_from' => ['type' => 'from', 'column' => 'updated_at'],
            'updated_to' => ['type' => 'to', 'column' => 'updated_at'],
        ];
    }
}
