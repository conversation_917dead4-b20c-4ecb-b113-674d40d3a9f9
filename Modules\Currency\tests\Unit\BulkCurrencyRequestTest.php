<?php

namespace Modules\Currency\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Modules\Currency\Http\Requests\BulkCurrencyRequest;
use Modules\Currency\Models\Currency;
use PHPUnit\Framework\Attributes\Test;

class BulkCurrencyRequestTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Currency module
        $this->artisan('migrate', ['--path' => 'Modules/Currency/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_validates_required_ids_field()
    {
        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        $validator = Validator::make([], $rules);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_ids_must_be_array()
    {
        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        $invalidData = ['ids' => 'not-an-array'];
        $validator = Validator::make($invalidData, $rules);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_ids_array_must_not_be_empty()
    {
        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        $invalidData = ['ids' => []];
        $validator = Validator::make($invalidData, $rules);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_each_id_must_be_integer()
    {
        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        $invalidData = ['ids' => ['not-integer', 2, 3]];
        $validator = Validator::make($invalidData, $rules);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids.0', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_each_id_must_exist_in_currencies_table()
    {
        $currency1 = Currency::factory()->create();
        $currency2 = Currency::factory()->create();

        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        // Test with existing IDs
        $validData = ['ids' => [$currency1->id, $currency2->id]];
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        // Test with non-existing ID
        $invalidData = ['ids' => [$currency1->id, 99999]];
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids.1', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_mixed_valid_and_invalid_ids()
    {
        $currency = Currency::factory()->create();

        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        $invalidData = ['ids' => [$currency->id, 'invalid', 99999]];
        $validator = Validator::make($invalidData, $rules);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids.1', $validator->errors()->toArray()); // 'invalid' is not integer
        $this->assertArrayHasKey('ids.2', $validator->errors()->toArray()); // 99999 doesn't exist
    }

    #[Test]
    public function it_validates_single_valid_id()
    {
        $currency = Currency::factory()->create();

        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        $validData = ['ids' => [$currency->id]];
        $validator = Validator::make($validData, $rules);

        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_validates_multiple_valid_ids()
    {
        $currency1 = Currency::factory()->create();
        $currency2 = Currency::factory()->create();
        $currency3 = Currency::factory()->create();

        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        $validData = ['ids' => [$currency1->id, $currency2->id, $currency3->id]];
        $validator = Validator::make($validData, $rules);

        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_validates_duplicate_ids()
    {
        $currency = Currency::factory()->create();

        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        // Duplicate IDs should still pass validation (business logic can handle duplicates)
        $validData = ['ids' => [$currency->id, $currency->id]];
        $validator = Validator::make($validData, $rules);

        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_has_custom_attributes()
    {
        $request = new BulkCurrencyRequest();
        $attributes = $request->attributes();

        $this->assertIsArray($attributes);
        $this->assertArrayHasKey('ids', $attributes);
        $this->assertArrayHasKey('ids.*', $attributes);
        $this->assertEquals(__('Currency IDs'), $attributes['ids']);
        $this->assertEquals(__('Currency ID'), $attributes['ids.*']);
    }

    #[Test]
    public function it_has_custom_messages_if_defined()
    {
        $request = new BulkCurrencyRequest();
        
        // Check if the request has messages method
        if (method_exists($request, 'messages')) {
            $messages = $request->messages();
            $this->assertIsArray($messages);
        } else {
            // If no custom messages method, that's also valid
            $this->assertTrue(true);
        }
    }

    #[Test]
    public function it_validates_with_string_numeric_ids()
    {
        $currency = Currency::factory()->create();

        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        // String numeric IDs should be converted to integers and pass
        $validData = ['ids' => [(string)$currency->id]];
        $validator = Validator::make($validData, $rules);

        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_validates_authorization()
    {
        $request = new BulkCurrencyRequest();
        
        // Check if the request has authorize method
        if (method_exists($request, 'authorize')) {
            $authorized = $request->authorize();
            // Should return true for basic authorization or handle in controllers/middleware
            $this->assertTrue(is_bool($authorized));
        } else {
            // If no authorize method, that's also valid (defaults to true)
            $this->assertTrue(true);
        }
    }

    #[Test]
    public function it_validates_large_number_of_ids()
    {
        // Create multiple currencies
        $currencies = Currency::factory()->count(10)->create();
        $ids = $currencies->pluck('id')->toArray();

        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        $validData = ['ids' => $ids];
        $validator = Validator::make($validData, $rules);

        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_validates_zero_as_invalid_id()
    {
        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        $invalidData = ['ids' => [0]];
        $validator = Validator::make($invalidData, $rules);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids.0', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_negative_ids()
    {
        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        $invalidData = ['ids' => [-1, -5]];
        $validator = Validator::make($invalidData, $rules);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids.0', $validator->errors()->toArray());
        $this->assertArrayHasKey('ids.1', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_float_ids()
    {
        $currency = Currency::factory()->create();

        $request = new BulkCurrencyRequest();
        $rules = $request->rules();

        // Float IDs should fail integer validation
        $invalidData = ['ids' => [1.5, $currency->id]];
        $validator = Validator::make($invalidData, $rules);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids.0', $validator->errors()->toArray());
    }
}
