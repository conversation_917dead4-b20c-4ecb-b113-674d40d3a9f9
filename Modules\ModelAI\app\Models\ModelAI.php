<?php

namespace Modules\ModelAI\Models;

use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\ModelAI\Database\Factories\ModelAIFactory;

class ModelAI extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'model_ai';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'key',
        'name',
        'description',
        'model_provider_id',
        'version',
        'api_endpoint',
        'streaming',
        'vision',
        'function_calling',
        'is_default',
        'sort_order',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'streaming' => 'boolean',
        'vision' => 'boolean',
        'function_calling' => 'boolean',
        'is_default' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = ['deleted_at'];

    /**
     * Scope a query to only include active models.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by provider.
     */
    public function scopeProvider(Builder $query, int $providerId): Builder
    {
        return $query->where('model_provider_id', $providerId);
    }

    /**
     * Scope a query to only include default models.
     */
    public function scopeDefault(Builder $query): Builder
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope a query to include models with streaming support.
     */
    public function scopeStreaming(Builder $query): Builder
    {
        return $query->where('streaming', true);
    }

    /**
     * Scope a query to include models with vision support.
     */
    public function scopeVision(Builder $query): Builder
    {
        return $query->where('vision', true);
    }

    /**
     * Scope a query to include models with function calling support.
     */
    public function scopeFunctionCalling(Builder $query): Builder
    {
        return $query->where('function_calling', true);
    }

    /**
     * Get the provider that owns this model.
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(ModelProvider::class, 'model_provider_id');
    }

    /**
     * Get the categories that belong to this model.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(ModelCategory::class, 'model_categorizations', 'model_ai_id', 'model_category_id');
    }

    /**
     * Get the service configurations for this model.
     */
    public function services(): HasMany
    {
        return $this->hasMany(ModelService::class, 'model_ai_id');
    }

    public function service(): HasOne
    {
        return $this->hasOne(ModelService::class, 'model_ai_id')
                    ->active()
                    ->latest('created_at');
    }

    /**
     * Get the tools that this model can use.
     */
    public function tools(): BelongsToMany
    {
        return $this->belongsToMany(ModelTool::class, 'model_ai_tools', 'model_ai_id', 'model_tool_id')
                    ->withPivot(['is_enabled', 'configuration', 'priority', 'max_usage_per_request', 'rate_limit_per_minute'])
                    ->withTimestamps();
    }

    /**
     * Get the tool configurations for this model.
     */
    public function toolConfigs(): HasMany
    {
        return $this->hasMany(ModelAITool::class, 'model_ai_id');
    }

    /**
     * Check if model is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if model can use a specific tool.
     */
    public function canUseTool(int $toolId): bool
    {
        // Check if tool is public and enabled
        $tool = ModelTool::find($toolId);
        if ($tool && $tool->is_public && $tool->is_enabled && $tool->isActive()) {
            return true;
        }

        // Check if model has explicit permission
        return $this->tools()
                    ->where('model_tool_id', $toolId)
                    ->wherePivot('is_enabled', true)
                    ->exists();
    }

    /**
     * Get enabled tools for this model.
     */
    public function getEnabledTools()
    {
        return $this->tools()
                    ->wherePivot('is_enabled', true)
                    ->where('model_tools.status', 'active')
                    ->where('model_tools.is_enabled', true)
                    ->orderByPivot('priority', 'desc')
                    ->get();
    }

    /**
     * Get public tools available to this model.
     */
    public function getPublicTools()
    {
        return ModelTool::where('is_public', true)
                        ->where('is_enabled', true)
                        ->where('status', 'active')
                        ->ordered()
                        ->get();
    }

    /**
     * Get all available tools (both assigned and public).
     */
    public function getAllAvailableTools()
    {
        $assignedTools = $this->getEnabledTools();
        $publicTools = $this->getPublicTools();

        // Merge and remove duplicates
        $allTools = $assignedTools->merge($publicTools)->unique('id');

        return $allTools->sortBy('name');
    }

    /**
     * Check if model is draft.
     */
    public function isDraft(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Check if model is inactive.
     */
    public function isInactive(): bool
    {
        return $this->status === 'inactive';
    }

    /**
     * Check if model is default.
     */
    public function isDefault(): bool
    {
        return $this->is_default;
    }

    /**
     * Check if model supports streaming.
     */
    public function supportsStreaming(): bool
    {
        return $this->streaming;
    }

    /**
     * Check if model supports vision.
     */
    public function supportsVision(): bool
    {
        return $this->vision;
    }

    /**
     * Check if model supports function calling.
     */
    public function supportsFunctionCalling(): bool
    {
        return $this->function_calling;
    }

    /**
     * Get full model name with version and provider.
     */
    public function getFullName(): string
    {
        $name = $this->name;

        if ($this->version) {
            $name .= " (v{$this->version})";
        }

        if ($this->provider) {
            $name .= " - " . $this->provider->name;
        }

        return $name;
    }

    /**
     * Get AI models formatted for dropdown.
     *
     * @return Collection
     */
    public static function getModelsForDropdown(): Collection
    {
        return self::query()
            ->with(['service' => function ($query) {
                $query->select(['id', 'model_ai_id', 'cost_per_request', 'cost_per1k_tokens', 'billing_type']);
            }])
            ->select(['id', 'key', 'name'])
            ->active()
            ->ordered()
            ->get()
            ->map(function (ModelAI $model) {
                if ($model->service) {
                    $model->service->makeHidden(['id', 'model_ai_id']);
                }
                return $model->makeHidden(['id']);
        });
    }


    /**
     * Get AI models with their associated categories.
     *
     * @return Collection
     */

    public static function getModelsWithCategory(): Collection
    {
        return self::query()
                ->with(['categories' => function ($query) {
                    $query->active()->ordered()->select(['id', 'key', 'name']);
                }, 'service' => function ($query) {
                    $query->select(['id', 'model_ai_id', 'cost_per_request', 'cost_per1k_tokens', 'billing_type']);
                }])
                ->select(['id', 'key', 'name'])
                ->active()
                ->ordered()
                ->get()
                ->map(function (ModelAI $model) {
                    if ($model->service) {
                        $model->service->makeHidden(['id', 'model_ai_id']);
                    }
                    $model->categories->each(function (ModelCategory $category) {
                        $category->makeHidden(['id']);
                    });
                    $model->makeHidden(['id']);
                    return $model;
                });
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): ModelAIFactory
    {
        return ModelAIFactory::new();
    }
}
