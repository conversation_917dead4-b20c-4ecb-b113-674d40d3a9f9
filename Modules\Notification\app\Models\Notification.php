<?php

namespace Modules\Notification\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Carbon\Carbon;
use Modules\Notification\Database\Factories\NotificationFactory;

class Notification extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'notification_type_id',
        'notifiable_type',
        'notifiable_id',
        'channel',
        'title',
        'content',
        'data',
        'metadata',
        'priority',
        'scheduled_at',
        'sent_at',
        'read_at',
        'failed_at',
        'failure_reason',
        'retry_count',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'data' => 'array',
        'metadata' => 'array',
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'read_at' => 'datetime',
        'failed_at' => 'datetime',
        'retry_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = ['deleted_at'];

    /**
     * Notification statuses.
     */
    const STATUS_PENDING = 'pending';
    const STATUS_SENT = 'sent';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_SCHEDULED = 'scheduled';

    /**
     * Notification priorities.
     */
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    /**
     * Scope a query to only include pending notifications.
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope a query to only include sent notifications.
     */
    public function scopeSent(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_SENT);
    }

    /**
     * Scope a query to only include failed notifications.
     */
    public function scopeFailed(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * Scope a query to only include scheduled notifications.
     */
    public function scopeScheduled(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_SCHEDULED);
    }

    /**
     * Scope a query to only include unread notifications.
     */
    public function scopeUnread(Builder $query): Builder
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope a query to only include read notifications.
     */
    public function scopeRead(Builder $query): Builder
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by channel.
     */
    public function scopeByChannel(Builder $query, string $channel): Builder
    {
        return $query->where('channel', $channel);
    }

    /**
     * Scope a query to filter by priority.
     */
    public function scopeByPriority(Builder $query, string $priority): Builder
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope a query to filter by notification type.
     */
    public function scopeByType(Builder $query, int $typeId): Builder
    {
        return $query->where('notification_type_id', $typeId);
    }

    /**
     * Scope a query to filter by notifiable.
     */
    public function scopeForNotifiable(Builder $query, $notifiable): Builder
    {
        return $query->where('notifiable_type', get_class($notifiable))
                    ->where('notifiable_id', $notifiable->id);
    }

    /**
     * Scope a query to filter notifications ready to be sent.
     */
    public function scopeReadyToSend(Builder $query): Builder
    {
        return $query->where(function ($query) {
            $query->where('status', self::STATUS_PENDING)
                  ->orWhere(function ($query) {
                      $query->where('status', self::STATUS_SCHEDULED)
                            ->where('scheduled_at', '<=', now());
                  });
        });
    }

    /**
     * Scope a query to filter notifications that can be retried.
     */
    public function scopeRetryable(Builder $query, int $maxRetries = 3): Builder
    {
        return $query->where('status', self::STATUS_FAILED)
                    ->where('retry_count', '<', $maxRetries);
    }

    /**
     * Get the owning notifiable model.
     */
    public function notifiable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the notification type that owns this notification.
     */
    public function notificationType(): BelongsTo
    {
        return $this->belongsTo(NotificationType::class);
    }

    /**
     * Mark the notification as read.
     */
    public function markAsRead(): bool
    {
        if ($this->read_at) {
            return true;
        }

        return $this->update(['read_at' => now()]);
    }

    /**
     * Mark the notification as unread.
     */
    public function markAsUnread(): bool
    {
        return $this->update(['read_at' => null]);
    }

    /**
     * Mark the notification as sent.
     */
    public function markAsSent(): bool
    {
        return $this->update([
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
        ]);
    }

    /**
     * Mark the notification as failed.
     */
    public function markAsFailed(string $reason = null): bool
    {
        return $this->update([
            'status' => self::STATUS_FAILED,
            'failed_at' => now(),
            'failure_reason' => $reason,
            'retry_count' => $this->retry_count + 1,
        ]);
    }

    /**
     * Mark the notification as cancelled.
     */
    public function markAsCancelled(): bool
    {
        return $this->update(['status' => self::STATUS_CANCELLED]);
    }

    /**
     * Check if the notification is read.
     */
    public function isRead(): bool
    {
        return !is_null($this->read_at);
    }

    /**
     * Check if the notification is unread.
     */
    public function isUnread(): bool
    {
        return is_null($this->read_at);
    }

    /**
     * Check if the notification is sent.
     */
    public function isSent(): bool
    {
        return $this->status === self::STATUS_SENT;
    }

    /**
     * Check if the notification is failed.
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Check if the notification is pending.
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the notification is scheduled.
     */
    public function isScheduled(): bool
    {
        return $this->status === self::STATUS_SCHEDULED;
    }

    /**
     * Check if the notification can be retried.
     */
    public function canRetry(int $maxRetries = 3): bool
    {
        return $this->isFailed() && $this->retry_count < $maxRetries;
    }

    /**
     * Get notification data as array.
     */
    public function getData(): array
    {
        return $this->data ?? [];
    }

    /**
     * Get notification metadata as array.
     */
    public function getMetadata(): array
    {
        return $this->metadata ?? [];
    }

    /**
     * Get a specific data value.
     */
    public function getDataValue(string $key, $default = null)
    {
        return data_get($this->getData(), $key, $default);
    }

    /**
     * Get a specific metadata value.
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return data_get($this->getMetadata(), $key, $default);
    }

    /**
     * Set a specific data value.
     */
    public function setDataValue(string $key, $value): void
    {
        $data = $this->getData();
        data_set($data, $key, $value);
        $this->data = $data;
    }

    /**
     * Set a specific metadata value.
     */
    public function setMetadataValue(string $key, $value): void
    {
        $metadata = $this->getMetadata();
        data_set($metadata, $key, $value);
        $this->metadata = $metadata;
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): NotificationFactory
    {
        return NotificationFactory::new();
    }
}
