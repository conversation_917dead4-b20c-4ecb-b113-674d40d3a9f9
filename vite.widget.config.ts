import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig({
  plugins: [vue()],
  
  build: {
    lib: {
      entry: resolve(__dirname, 'src/widget/index.ts'),
      name: 'ProcmsChatbot',
      fileName: (format) => `procms-chatbot.${format}.js`,
      formats: ['umd', 'es']
    },
    
    rollupOptions: {
      // Externalize deps that shouldn't be bundled
      external: ['vue'],
      output: {
        // Provide global variables for externalized deps in UMD build
        globals: {
          vue: 'Vue'
        },
        // Custom banner for UMD build
        banner: '/* ProcMS Chatbot Widget v1.0.0 | MIT License */'
      }
    },
    
    // Output directory
    outDir: 'dist/widget',
    
    // Generate sourcemaps for debugging
    sourcemap: true,
    
    // Minify for production
    minify: 'terser',
    
    // Target modern browsers
    target: 'es2015'
  },
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  
  define: {
    // Define global constants
    __WIDGET_VERSION__: JSON.stringify('1.0.0'),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString())
  }
});
