<?php

use <PERSON><PERSON><PERSON>\Notification\Http\Controllers\Auth\NotificationTemplateController;
use Mo<PERSON>les\Notification\Http\Controllers\Auth\NotificationTestController;

// Authenticated API routes (admin/auth)
Route::middleware(['auth:api'])->prefix('v1/auth')->group(function () {
    // Notification Templates Management
    Route::prefix('notification-templates')->group(function () {
        // Bulk operations
        Route::delete('bulk-destroy', [NotificationTemplateController::class, 'bulkDestroy'])->name('auth.notification-template.bulk-destroy');
        Route::put('bulk-restore', [NotificationTemplateController::class, 'bulkRestore'])->name('auth.notification-template.bulk-restore');
        Route::delete('bulk-delete', [NotificationTemplateController::class, 'bulkForceDelete'])->name('auth.notification-template.bulk-delete');
        Route::put('bulk-activate', [NotificationTemplateController::class, 'bulkActivate'])->name('auth.notification-template.bulk-activate');
        Route::put('bulk-deactivate', [NotificationTemplateController::class, 'bulkDeactivate'])->name('auth.notification-template.bulk-deactivate');

        // Individual operations
        Route::put('{id}/restore', [NotificationTemplateController::class, 'restore'])->name('auth.notification-template.restore');
        Route::delete('{id}/force-delete', [NotificationTemplateController::class, 'forceDelete'])->name('auth.notification-template.force-delete');

        // Special operations
        Route::post('{notificationTemplate}/preview', [NotificationTemplateController::class, 'preview'])->name('auth.notification-template.preview');
        Route::get('dropdown', [NotificationTemplateController::class, 'dropdown'])->name('auth.notification-template.dropdown');
    });

    // Standard CRUD operations
    Route::apiResource('notification-templates', NotificationTemplateController::class)->names('auth.notification-template');

    // Notification Testing Routes
    Route::prefix('notification-test')->group(function () {
        Route::post('email', [NotificationTestController::class, 'testEmail'])->name('auth.notification-test.email');
        Route::get('email-config', [NotificationTestController::class, 'getEmailConfig'])->name('auth.notification-test.email-config');
        Route::post('notification', [NotificationTestController::class, 'testNotification'])->name('auth.notification-test.notification');
        Route::get('system-status', [NotificationTestController::class, 'getSystemStatus'])->name('auth.notification-test.system-status');
        Route::post('bulk-test', [NotificationTestController::class, 'bulkTest'])->name('auth.notification-test.bulk-test');
    });


});
