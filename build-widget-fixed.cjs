#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const distDir = path.join(__dirname, 'dist', 'widget');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

const umdTemplate = `
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ProcmsChatbot = {}));
})(this, (function (exports) {
  'use strict';

  class EventEmitter {
    constructor() {
      this.events = {};
    }
    on(event, callback) {
      if (!this.events[event]) this.events[event] = [];
      this.events[event].push(callback);
    }
    off(event, callback) {
      if (!this.events[event]) return;
      if (callback) {
        this.events[event] = this.events[event].filter(cb => cb !== callback);
      } else {
        this.events[event] = [];
      }
    }
    emit(event, ...args) {
      if (!this.events[event]) return;
      this.events[event].forEach(callback => {
        try { callback(...args); } catch (error) { console.error('Event error:', error); }
      });
    }
  }

  class ThemeManager {
    constructor(container) {
      this.container = container;
      this.currentTheme = 'light';
    }
    setTheme(theme) {
      this.currentTheme = theme;
      this.container.setAttribute('data-procms-theme', theme);
    }
    setCustomTheme(themeConfig) {
      Object.entries(themeConfig).forEach(([key, value]) => {
        const cssVar = '--procms-' + key.replace(/([A-Z])/g, '-$1').toLowerCase();
        this.container.style.setProperty(cssVar, value);
      });
    }
  }

  class WidgetLoader {
    static detectMode() { return 'widget'; }

    static async loadWidget(config, container) {
      const widget = document.createElement('div');
      widget.className = 'procms-chatbot-widget';
      widget.setAttribute('data-procms-theme', config.theme || 'light');
      widget.setAttribute('data-minimized', 'false');
      widget._procmsInstance = null;
      
      widget.innerHTML = \`
        <div class="procms-widget-header" style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 60px;
          padding: 0 16px;
          background: var(--procms-primary, #3b82f6);
          color: white;
          cursor: pointer;
          user-select: none;
          flex-shrink: 0;
        ">
          <div style="display: flex; align-items: center; gap: 12px;">
            <div style="
              width: 32px; height: 32px; border-radius: 50%;
              background: rgba(255,255,255,0.2);
              display: flex; align-items: center; justify-content: center;
            ">🤖</div>
            <div>
              <div style="font-weight: 600;">ProcMS Bot</div>
              <div style="font-size: 12px; opacity: 0.8;">Online</div>
            </div>
          </div>
          <div style="display: flex; gap: 8px;">
            <button class="procms-minimize-btn" style="
              background: none; border: none; color: white; cursor: pointer;
              padding: 6px 10px; border-radius: 4px; font-size: 18px;
              font-weight: bold; transition: all 0.2s; line-height: 1;
            " title="Minimize">−</button>
            <button class="procms-close-btn" style="
              background: none; border: none; color: white; cursor: pointer;
              padding: 6px 10px; border-radius: 4px; font-size: 18px;
              font-weight: bold; transition: all 0.2s; line-height: 1;
            " title="Close">×</button>
          </div>
        </div>
        
        <div class="procms-widget-body" style="
          flex: 1;
          padding: 16px;
          background: var(--procms-widget-bg, #f9fafb);
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--procms-text-secondary, #6b7280);
          transition: all 0.3s ease;
          overflow: hidden;
          min-height: 0;
        ">
          <div style="text-align: center;">
            <div style="font-size: 18px; margin-bottom: 8px;">🚀</div>
            <div>Widget loaded successfully!</div>
            <div style="font-size: 12px; margin-top: 4px; opacity: 0.7;">Bot: \${config.botUuid}</div>
            <div style="font-size: 12px; margin-top: 8px;">
              <span style="
                background: var(--procms-primary, #3b82f6); color: white;
                padding: 4px 8px; border-radius: 12px; font-size: 10px;
              ">Click header to minimize</span>
            </div>
          </div>
        </div>
        
        <div class="procms-widget-footer" style="
          padding: 16px;
          background: var(--procms-widget-surface, white);
          border-top: 1px solid var(--procms-widget-border, #e5e7eb);
          transition: all 0.3s ease;
          flex-shrink: 0;
        ">
          <div style="display: flex; gap: 8px;">
            <input type="text" placeholder="Type a message..." style="
              flex: 1; padding: 12px 16px;
              border: 1px solid var(--procms-widget-border, #e5e7eb);
              border-radius: 24px; outline: none; font-size: 14px;
              background: var(--procms-widget-bg, #f9fafb);
              color: var(--procms-text-primary, #111827);
            " />
            <button style="
              width: 44px; height: 44px; border-radius: 50%;
              background: var(--procms-primary, #3b82f6); color: white;
              border: none; cursor: pointer; display: flex;
              align-items: center; justify-content: center;
              font-size: 16px; transition: all 0.2s;
            ">→</button>
          </div>
        </div>
      \`;

      // Widget container styles - KEY FIX HERE
      widget.style.cssText = \`
        display: flex;
        flex-direction: column;
        height: 100%;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
      \`;

      const minimizeBtn = widget.querySelector('.procms-minimize-btn');
      const closeBtn = widget.querySelector('.procms-close-btn');
      const header = widget.querySelector('.procms-widget-header');
      const body = widget.querySelector('.procms-widget-body');
      const footer = widget.querySelector('.procms-widget-footer');

      // Store original container height
      let originalHeight = null;

      const toggleMinimize = () => {
        const isMinimized = widget.getAttribute('data-minimized') === 'true';
        const parentContainer = widget.parentElement;
        
        if (isMinimized) {
          // MAXIMIZE - Restore original height
          widget.setAttribute('data-minimized', 'false');
          body.style.display = 'flex';
          footer.style.display = 'block';
          minimizeBtn.textContent = '−';
          minimizeBtn.title = 'Minimize';
          
          // Restore widget height
          widget.style.height = '100%';
          
          // Restore container height if it was stored
          if (originalHeight && parentContainer) {
            parentContainer.style.height = originalHeight;
          }
          
          if (widget._procmsInstance) {
            widget._procmsInstance.emit('maximized');
          }
        } else {
          // MINIMIZE - Store original height and set to header only
          widget.setAttribute('data-minimized', 'true');
          
          // Store original container height
          if (parentContainer && !originalHeight) {
            originalHeight = parentContainer.style.height || parentContainer.offsetHeight + 'px';
          }
          
          body.style.display = 'none';
          footer.style.display = 'none';
          minimizeBtn.textContent = '□';
          minimizeBtn.title = 'Maximize';
          
          // Set widget height to header only
          widget.style.height = '60px';
          
          // Set container height to header only
          if (parentContainer) {
            parentContainer.style.height = '60px';
          }
          
          if (widget._procmsInstance) {
            widget._procmsInstance.emit('minimized');
          }
        }
      };

      minimizeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        toggleMinimize();
      });

      header.addEventListener('click', toggleMinimize);

      closeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        if (widget._procmsInstance) {
          widget._procmsInstance.emit('close-requested');
          widget._procmsInstance.unmount();
        }
      });

      [minimizeBtn, closeBtn].forEach(btn => {
        btn.addEventListener('mouseenter', () => {
          btn.style.background = 'rgba(255,255,255,0.2)';
        });
        btn.addEventListener('mouseleave', () => {
          btn.style.background = 'none';
        });
      });

      if (typeof container === 'string') {
        container = document.querySelector(container);
      }
      
      if (container) {
        container.appendChild(widget);
      }

      return widget;
    }
  }

  class ProcmsChatbotWidget extends EventEmitter {
    constructor(config) {
      super();
      if (!config.botUuid) throw new Error('Bot UUID is required');
      if (!config.apiKey) throw new Error('API Key is required');
      this.config = config;
      this.mounted = false;
      this.minimized = false;
      this.container = null;
      this.themeManager = null;
      this.originalContainerHeight = null;
    }

    async mount(selector) {
      try {
        const container = typeof selector === 'string' 
          ? document.querySelector(selector) 
          : selector;

        if (!container) throw new Error('Container not found');

        this.container = container;
        
        // Store original container height
        this.originalContainerHeight = container.style.height || 
          getComputedStyle(container).height;

        this.widget = await WidgetLoader.loadWidget(this.config, container);
        this.widget._procmsInstance = this;
        this.themeManager = new ThemeManager(this.widget);
        this.mounted = true;

        if (this.config.theme) {
          this.themeManager.setTheme(this.config.theme);
        }

        this.on('minimized', () => {
          this.minimized = true;
          if (this.config.onMinimized) this.config.onMinimized();
        });

        this.on('maximized', () => {
          this.minimized = false;
          if (this.config.onMaximized) this.config.onMaximized();
        });

        this.on('close-requested', () => {
          if (this.config.onCloseRequested) this.config.onCloseRequested();
        });

        this.emit('mounted', { mode: 'widget' });
        this.emit('ready');
        if (this.config.onReady) this.config.onReady();
      } catch (error) {
        this.emit('error', error);
        if (this.config.onError) this.config.onError(error);
        throw error;
      }
    }

    unmount() {
      if (this.widget && this.widget.parentNode) {
        // Restore original container height before unmounting
        if (this.container && this.originalContainerHeight) {
          this.container.style.height = this.originalContainerHeight;
        }
        this.widget.parentNode.removeChild(this.widget);
      }
      this.mounted = false;
      this.minimized = false;
      this.emit('unmounted');
    }

    minimize() {
      if (this.widget && !this.minimized) {
        const minimizeBtn = this.widget.querySelector('.procms-minimize-btn');
        if (minimizeBtn) minimizeBtn.click();
      }
    }

    maximize() {
      if (this.widget && this.minimized) {
        const minimizeBtn = this.widget.querySelector('.procms-minimize-btn');
        if (minimizeBtn) minimizeBtn.click();
      }
    }

    toggle() {
      this.minimized ? this.maximize() : this.minimize();
    }

    isMinimized() { return this.minimized; }
    isMounted() { return this.mounted; }
    getMode() { return 'widget'; }
    getConfig() { return { ...this.config }; }
    setTheme(theme) { if (this.themeManager) this.themeManager.setTheme(theme); }
    setCustomTheme(themeConfig) { if (this.themeManager) this.themeManager.setCustomTheme(themeConfig); }
  }

  class ProcmsChatbot {
    static async create(config, selector) {
      const widget = new ProcmsChatbotWidget(config);
      await widget.mount(selector);
      return widget;
    }

    static async createFloatingWidget(config) {
      const container = document.createElement('div');
      container.id = 'procms-floating-widget';
      
      const position = config.position || 'bottom-right';
      let positionStyles = '';
      
      switch (position) {
        case 'bottom-right': positionStyles = 'bottom: 20px; right: 20px;'; break;
        case 'bottom-left': positionStyles = 'bottom: 20px; left: 20px;'; break;
        case 'top-right': positionStyles = 'top: 20px; right: 20px;'; break;
        case 'top-left': positionStyles = 'top: 20px; left: 20px;'; break;
        case 'center': positionStyles = 'top: 50%; left: 50%; transform: translate(-50%, -50%);'; break;
        default: positionStyles = 'bottom: 20px; right: 20px;';
      }

      container.style.cssText = \`
        position: fixed;
        \${positionStyles}
        width: 400px;
        height: 600px;
        z-index: 9999;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        border-radius: 8px;
        overflow: hidden;
        transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      \`;

      document.body.appendChild(container);

      const widget = new ProcmsChatbotWidget(config);
      await widget.mount(container);
      
      if (window.innerWidth <= 768 && config.autoMinimizeOnMobile !== false) {
        setTimeout(() => widget.minimize(), 1000);
      }
      
      return widget;
    }

    static generateIframeCode(config) {
      const params = new URLSearchParams({
        botUuid: config.botUuid,
        apiKey: config.apiKey,
        theme: config.theme || 'light'
      });
      return \`<iframe src="https://widget.procms.com/embed?\${params}" width="\${config.width || 400}" height="\${config.height || 600}" frameborder="0" style="border-radius: 8px;"></iframe>\`;
    }

    static detectMode() { return WidgetLoader.detectMode(); }
  }

  exports.ProcmsChatbotWidget = ProcmsChatbotWidget;
  exports.ProcmsChatbot = ProcmsChatbot;
  exports.ThemeManager = ThemeManager;

  if (typeof window !== 'undefined') {
    window.ProcmsChatbotWidget = ProcmsChatbotWidget;
    window.ProcmsChatbot = ProcmsChatbot;
  }

}));
`;

fs.writeFileSync(path.join(distDir, 'procms-chatbot.umd.js'), umdTemplate);
console.log('✅ Widget built with HEIGHT FIX!');
console.log('🔧 Fixed: Container height now properly adjusts when minimized');
console.log('📏 Height behavior:');
console.log('  - Minimize: Container height = 60px (header only)');
console.log('  - Maximize: Container height = restored to original');
console.log('  - Stores original height before first minimize');
console.log('  - Restores height on unmount');
