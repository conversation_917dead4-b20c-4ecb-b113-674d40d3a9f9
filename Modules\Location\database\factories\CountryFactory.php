<?php

namespace Modules\Location\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Location\Models\Country;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\Location\Models\Country>
 */
class CountryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Country::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $country = $this->faker->country();

        // Generate unique ISO code that doesn't conflict with existing ones
        do {
            $isoCode2 = $this->faker->countryCode();
        } while (in_array($isoCode2, ['US', 'VN', 'GB']) || Country::where('iso_code_2', $isoCode2)->exists());

        return [
            'name' => $country,
            'native_name' => $this->faker->optional()->country(),
            'iso_code_2' => $isoCode2,
            'iso_code_3' => $this->faker->optional()->countryISOAlpha3(),
            'iso_numeric' => $this->faker->optional()->numerify('###'),
            'phone_code' => $this->faker->optional()->numerify('+###'),
            'region' => $this->faker->randomElement(['Africa', 'Americas', 'Asia', 'Europe', 'Oceania']),
            'subregion' => $this->faker->optional()->words(2, true),
            'latitude' => $this->faker->optional()->latitude(),
            'longitude' => $this->faker->optional()->longitude(),
            'emoji' => $this->faker->optional()->emoji(),
            'emoji_unicode' => $this->faker->optional()->regexify('U\+[0-9A-F]{4,5}'),
            'status' => $this->faker->randomElement(['active', 'inactive']),
        ];
    }

    /**
     * Indicate that the country is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the country is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Create a country with specific region.
     */
    public function region(string $region): static
    {
        return $this->state(fn (array $attributes) => [
            'region' => $region,
        ]);
    }
}
