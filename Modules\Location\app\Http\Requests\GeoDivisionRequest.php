<?php

namespace Modules\Location\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

/**
 * @property mixed $id
 */
class GeoDivisionRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'code' => [
                'nullable',
                'string',
                'max:255',
                'alpha_dash',
            ],
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'native_name' => [
                'nullable',
                'string',
                'max:255',
            ],
            'type' => [
                'required',
                'string',
                Rule::in(['state', 'province', 'city', 'district', 'ward', 'commune']),
            ],
            'country_id' => [
                'required',
                'integer',
                'exists:countries,id',
            ],
            'parent_id' => [
                'nullable',
                'integer',
                'exists:geo_divisions,id',
                'different:id',
            ],
            'latitude' => [
                'nullable',
                'numeric',
                'between:-90,90',
            ],
            'longitude' => [
                'nullable',
                'numeric',
                'between:-180,180',
            ],
            'postal_code' => [
                'nullable',
                'string',
                'max:255',
            ],
            'sort_order' => [
                'nullable',
                'integer',
                'min:1',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(['active', 'inactive']),
            ],
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate parent hierarchy
            if ($this->parent_id && $this->country_id) {
                $parent = \Modules\Location\Models\GeoDivision::find($this->parent_id);
                if ($parent && $parent->country_id !== $this->country_id) {
                    $validator->errors()->add('parent_id', __('Parent division must belong to the same country.'));
                }
            }

            // Validate type hierarchy logic
            if ($this->parent_id && $this->type) {
                $parent = \Modules\Location\Models\GeoDivision::find($this->parent_id);
                if ($parent) {
                    $validHierarchy = [
                        'state' => [],
                        'province' => [],
                        'city' => ['state', 'province'],
                        'district' => ['city'],
                        'ward' => ['district'],
                        'commune' => ['district'],
                    ];

                    if (isset($validHierarchy[$this->type]) && 
                        !empty($validHierarchy[$this->type]) && 
                        !in_array($parent->type, $validHierarchy[$this->type])) {
                        $validator->errors()->add('type', __('Invalid hierarchy: :type cannot be a child of :parent_type.', [
                            'type' => $this->type,
                            'parent_type' => $parent->type
                        ]));
                    }
                }
            }

            // Validate unique code within country and parent
            if ($this->code && $this->country_id) {
                $query = \Modules\Location\Models\GeoDivision::where('code', $this->code)
                    ->where('country_id', $this->country_id)
                    ->where('parent_id', $this->parent_id);

                if ($this->id) {
                    $query->where('id', '!=', $this->id);
                }

                if ($query->exists()) {
                    $validator->errors()->add('code', __('The code must be unique within the same country and parent division.'));
                }
            }
        });
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'code' => __('Code'),
            'name' => __('Name'),
            'native_name' => __('Native Name'),
            'type' => __('Type'),
            'country_id' => __('Country'),
            'parent_id' => __('Parent Division'),
            'latitude' => __('Latitude'),
            'longitude' => __('Longitude'),
            'postal_code' => __('Postal Code'),
            'sort_order' => __('Sort Order'),
            'status' => __('Status'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'latitude.between' => __('The latitude must be between -90 and 90 degrees.'),
            'longitude.between' => __('The longitude must be between -180 and 180 degrees.'),
            'parent_id.different' => __('A division cannot be its own parent.'),
        ];
    }
}
