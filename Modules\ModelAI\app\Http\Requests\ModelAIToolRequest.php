<?php

namespace Modules\ModelAI\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseFormRequest;

/**
 * @property mixed $id
 * @property mixed $model_ai_id
 * @property mixed $model_tool_id
 */
class ModelAIToolRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'model_ai_id' => [
                'required',
                'integer',
                'exists:model_ai,id',
            ],
            'model_tool_id' => [
                'required',
                'integer',
                'exists:model_tools,id',
                Rule::unique('model_ai_tools')->ignore($this->id)->where(function ($query) {
                    return $query->where('model_ai_id', $this->model_ai_id);
                }),
            ],
            'is_enabled' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'configuration' => [
                'nullable',
                'array',
            ],
            'priority' => [
                'sometimes',
                'required',
                'integer',
                'min:0',
                'max:100',
            ],
            'max_usage_per_request' => [
                'nullable',
                'integer',
                'min:1',
                'max:1000',
            ],
            'rate_limit_per_minute' => [
                'nullable',
                'integer',
                'min:1',
                'max:10000',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'model_ai_id' => __('AI Model'),
            'model_tool_id' => __('Tool'),
            'is_enabled' => __('Is Enabled'),
            'configuration' => __('Configuration'),
            'priority' => __('Priority'),
            'max_usage_per_request' => __('Max Usage Per Request'),
            'rate_limit_per_minute' => __('Rate Limit Per Minute'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'model_ai_id.exists' => __('The selected AI model does not exist.'),
            'model_tool_id.exists' => __('The selected tool does not exist.'),
            'model_tool_id.unique' => __('This tool is already assigned to the selected AI model.'),
            'priority.min' => __('Priority must be at least 0.'),
            'priority.max' => __('Priority cannot exceed 100.'),
            'max_usage_per_request.min' => __('Max usage per request must be at least 1.'),
            'max_usage_per_request.max' => __('Max usage per request cannot exceed 1000.'),
            'rate_limit_per_minute.min' => __('Rate limit per minute must be at least 1.'),
            'rate_limit_per_minute.max' => __('Rate limit per minute cannot exceed 10000.'),
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if the tool is compatible with the model
            if ($this->model_ai_id && $this->model_tool_id) {
                $model = \Modules\ModelAI\Models\ModelAI::find($this->model_ai_id);
                $tool = \Modules\ModelAI\Models\ModelTool::find($this->model_tool_id);

                if ($model && $tool) {
                    // If tool is not public and not already assigned, check compatibility
                    if (!$tool->is_public && !$tool->isActive()) {
                        $validator->errors()->add('model_tool_id', __('The selected tool is not available.'));
                    }

                    // Check if model is active
                    if (!$model->isActive()) {
                        $validator->errors()->add('model_ai_id', __('The selected AI model is not active.'));
                    }
                }
            }
        });
    }
}
