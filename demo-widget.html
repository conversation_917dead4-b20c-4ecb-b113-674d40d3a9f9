<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProcMS Chatbot Widget - Demo</title>
    <link rel="stylesheet" href="dist/widget/procms-chatbot.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .hero {
            text-align: center;
            padding: 60px 20px;
        }
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        .hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        .demo-section {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: start;
        }
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .demo-card h3 {
            margin-top: 0;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .widget-container {
            width: 100%;
            height: 600px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .btn.active {
            background: rgba(255, 255, 255, 0.4);
            border-color: rgba(255, 255, 255, 0.6);
        }
        .status {
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }
        .status.success { 
            background: rgba(34, 197, 94, 0.2); 
            border: 1px solid rgba(34, 197, 94, 0.4);
        }
        .status.error { 
            background: rgba(239, 68, 68, 0.2); 
            border: 1px solid rgba(239, 68, 68, 0.4);
        }
        .status.info { 
            background: rgba(59, 130, 246, 0.2); 
            border: 1px solid rgba(59, 130, 246, 0.4);
        }
        .floating-demo {
            text-align: center;
        }
        .floating-demo p {
            opacity: 0.8;
            margin-bottom: 20px;
        }
        @media (max-width: 768px) {
            .demo-section {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .hero h1 {
                font-size: 2rem;
            }
            .demo-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="hero">
        <h1>🤖 ProcMS Chatbot Widget</h1>
        <p>Experience the power of AI-driven customer support</p>
    </div>

    <div class="demo-section">
        <!-- Embedded Widget Demo -->
        <div class="demo-card">
            <h3>📱 Embedded Widget</h3>
            <p>Widget embedded directly in your page content.</p>
            
            <div class="controls">
                <button class="btn" onclick="loadEmbeddedWidget()">Load Widget</button>
                <button class="btn" onclick="switchTheme('light')">Light</button>
                <button class="btn" onclick="switchTheme('dark')">Dark</button>
                <button class="btn" onclick="applyCustomTheme()">Custom</button>
                <button class="btn" onclick="clearEmbeddedWidget()">Clear</button>
            </div>
            
            <div id="embedded-widget" class="widget-container"></div>
            <div id="embedded-status" class="status" style="display: none;"></div>
        </div>

        <!-- Floating Widget Demo -->
        <div class="demo-card floating-demo">
            <h3>🎈 Floating Widget</h3>
            <p>Widget that floats over your content, perfect for customer support.</p>
            
            <div class="controls">
                <button class="btn" onclick="createFloatingWidget('bottom-right')">Bottom Right</button>
                <button class="btn" onclick="createFloatingWidget('bottom-left')">Bottom Left</button>
                <button class="btn" onclick="createFloatingWidget('center')">Center</button>
                <button class="btn" onclick="removeFloatingWidget()">Remove</button>
            </div>
            
            <div style="
                background: rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 40px;
                text-align: center;
                margin: 20px 0;
                border: 2px dashed rgba(255, 255, 255, 0.3);
            ">
                <div style="font-size: 3rem; margin-bottom: 1rem;">💬</div>
                <div>Click a button above to create a floating widget</div>
                <div style="font-size: 0.9rem; opacity: 0.7; margin-top: 0.5rem;">
                    The widget will appear over this content
                </div>
            </div>
            
            <div id="floating-status" class="status" style="display: none;"></div>
        </div>
    </div>

    <!-- Widget Analytics -->
    <div style="max-width: 1200px; margin: 40px auto;">
        <div class="demo-card">
            <h3>📊 Widget Analytics</h3>
            <div id="analytics-log" style="
                background: rgba(0, 0, 0, 0.2);
                border-radius: 8px;
                padding: 15px;
                font-family: monospace;
                font-size: 12px;
                max-height: 200px;
                overflow-y: auto;
                border: 1px solid rgba(255, 255, 255, 0.2);
            ">
                Widget events will appear here...
            </div>
            <button class="btn" onclick="clearAnalytics()" style="margin-top: 10px;">Clear Log</button>
        </div>
    </div>

    <!-- Load Widget Library -->
    <script src="dist/widget/procms-chatbot.umd.js"></script>
    <script>
        let embeddedWidget = null;
        let floatingWidget = null;
        let currentTheme = 'light';

        // Analytics logging
        function logEvent(event, data = {}) {
            const timestamp = new Date().toLocaleTimeString();
            const logEl = document.getElementById('analytics-log');
            const logEntry = `[${timestamp}] ${event}: ${JSON.stringify(data)}`;
            logEl.innerHTML += logEntry + '\n';
            logEl.scrollTop = logEl.scrollHeight;
            console.log('[Widget Analytics]', event, data);
        }

        function showStatus(elementId, message, type = 'info') {
            const statusEl = document.getElementById(elementId);
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            logEvent('status_update', { element: elementId, message, type });
        }

        // Embedded Widget Functions
        async function loadEmbeddedWidget() {
            try {
                if (embeddedWidget) {
                    embeddedWidget.unmount();
                }

                showStatus('embedded-status', 'Loading widget...', 'info');
                logEvent('widget_load_start', { type: 'embedded' });

                embeddedWidget = await ProcmsChatbot.create({
                    botUuid: 'demo-bot-uuid-123',
                    apiKey: 'pk_test_demo_api_key_12345678901234567890',
                    theme: currentTheme,
                    onReady: () => {
                        logEvent('widget_ready', { type: 'embedded' });
                    },
                    onMessage: (message) => {
                        logEvent('message_received', { 
                            type: message.type, 
                            content: message.content.substring(0, 50) + '...' 
                        });
                    },
                    onError: (error) => {
                        logEvent('widget_error', { error: error.message });
                    }
                }, '#embedded-widget');

                showStatus('embedded-status', 'Widget loaded successfully!', 'success');
                logEvent('widget_load_complete', { type: 'embedded' });

            } catch (error) {
                showStatus('embedded-status', `Error: ${error.message}`, 'error');
                logEvent('widget_load_error', { type: 'embedded', error: error.message });
            }
        }

        function clearEmbeddedWidget() {
            if (embeddedWidget) {
                embeddedWidget.unmount();
                embeddedWidget = null;
                showStatus('embedded-status', 'Widget cleared.', 'info');
                logEvent('widget_cleared', { type: 'embedded' });
            }
        }

        // Theme Functions
        function switchTheme(theme) {
            currentTheme = theme;
            if (embeddedWidget) {
                embeddedWidget.setTheme(theme);
                logEvent('theme_changed', { theme });
            }
            
            // Update active button
            document.querySelectorAll('.controls .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function applyCustomTheme() {
            if (embeddedWidget) {
                embeddedWidget.setCustomTheme({
                    primaryColor: '#ff6b6b',
                    backgroundColor: '#2d3748',
                    textPrimary: '#ffffff',
                    borderRadius: '16px'
                });
                logEvent('custom_theme_applied', { 
                    primaryColor: '#ff6b6b',
                    backgroundColor: '#2d3748' 
                });
            }
            
            event.target.classList.add('active');
        }

        // Floating Widget Functions
        async function createFloatingWidget(position) {
            try {
                if (floatingWidget) {
                    removeFloatingWidget();
                }

                showStatus('floating-status', `Creating floating widget at ${position}...`, 'info');
                logEvent('floating_widget_create_start', { position });

                floatingWidget = await ProcmsChatbot.createFloatingWidget({
                    botUuid: 'demo-bot-uuid-123',
                    apiKey: 'pk_test_demo_api_key_12345678901234567890',
                    theme: currentTheme,
                    position: position,
                    onReady: () => {
                        logEvent('floating_widget_ready', { position });
                    },
                    onMessage: (message) => {
                        logEvent('floating_message_received', { 
                            type: message.type,
                            content: message.content.substring(0, 50) + '...'
                        });
                    }
                });

                showStatus('floating-status', `Floating widget created at ${position}!`, 'success');
                logEvent('floating_widget_created', { position });

            } catch (error) {
                showStatus('floating-status', `Error: ${error.message}`, 'error');
                logEvent('floating_widget_error', { position, error: error.message });
            }
        }

        function removeFloatingWidget() {
            if (floatingWidget) {
                floatingWidget.unmount();
                const container = document.getElementById('procms-floating-widget');
                if (container) {
                    container.remove();
                }
                floatingWidget = null;
                showStatus('floating-status', 'Floating widget removed.', 'info');
                logEvent('floating_widget_removed');
            }
        }

        function clearAnalytics() {
            document.getElementById('analytics-log').innerHTML = 'Analytics log cleared...\n';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            logEvent('page_loaded');
            
            // Check if widget library is loaded
            if (typeof ProcmsChatbotWidget !== 'undefined') {
                logEvent('widget_library_loaded', { 
                    hasWidget: typeof ProcmsChatbotWidget !== 'undefined',
                    hasChatbot: typeof ProcmsChatbot !== 'undefined'
                });
            } else {
                logEvent('widget_library_missing');
            }
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (embeddedWidget) embeddedWidget.unmount();
            if (floatingWidget) floatingWidget.unmount();
        });
    </script>
</body>
</html>
