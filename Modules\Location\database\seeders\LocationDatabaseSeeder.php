<?php

namespace Modules\Location\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Location\Models\Country;
use Modules\Location\Models\GeoDivision;

class LocationDatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed permissions first
        $this->call(LocationPermissionSeeder::class);

        // Create sample countries
        $countries = [
            [
                'name' => 'United States',
                'native_name' => 'United States',
                'iso_code_2' => 'US',
                'iso_code_3' => 'USA',
                'iso_numeric' => '840',
                'phone_code' => '+1',
                'region' => 'Americas',
                'subregion' => 'Northern America',
                'latitude' => 37.0902,
                'longitude' => -95.7129,
                'emoji' => '🇺🇸',
                'emoji_unicode' => 'U+1F1FA U+1F1F8',
                'status' => 'active',
            ],
            [
                'name' => 'Vietnam',
                'native_name' => 'Việt Nam',
                'iso_code_2' => 'VN',
                'iso_code_3' => 'VNM',
                'iso_numeric' => '704',
                'phone_code' => '+84',
                'region' => 'Asia',
                'subregion' => 'South-Eastern Asia',
                'latitude' => 14.0583,
                'longitude' => 108.2772,
                'emoji' => '🇻🇳',
                'emoji_unicode' => 'U+1F1FB U+1F1F3',
                'status' => 'active',
            ],
            [
                'name' => 'United Kingdom',
                'native_name' => 'United Kingdom',
                'iso_code_2' => 'GB',
                'iso_code_3' => 'GBR',
                'iso_numeric' => '826',
                'phone_code' => '+44',
                'region' => 'Europe',
                'subregion' => 'Northern Europe',
                'latitude' => 55.3781,
                'longitude' => -3.4360,
                'emoji' => '🇬🇧',
                'emoji_unicode' => 'U+1F1EC U+1F1E7',
                'status' => 'active',
            ],
        ];

        foreach ($countries as $countryData) {
            $country = Country::updateOrCreate(
                ['iso_code_2' => $countryData['iso_code_2']],
                $countryData
            );
            $this->createDivisionsForCountry($country);
        }

        // Create additional random countries (only if not exist)
        $existingCount = Country::count();
        if ($existingCount < 13) { // 3 fixed + 10 random
            $needToCreate = min(10, 13 - $existingCount); // Limit to max 10
            try {
                Country::factory($needToCreate)->active()->create()->each(function ($country) {
                    $this->createDivisionsForCountry($country);
                });
            } catch (\Exception $e) {
                // If factory fails due to unique constraints, just continue
                $this->command->warn('Some random countries could not be created due to unique constraints.');
            }
        }
    }

    /**
     * Create geographic divisions for a country.
     */
    private function createDivisionsForCountry(Country $country): void
    {
        // Create states/provinces for the country
        $states = GeoDivision::factory()
            ->count(rand(3, 8))
            ->province()
            ->active()
            ->forCountry($country->id)
            ->create();

        foreach ($states as $state) {
            // Create cities for each state
            $cities = GeoDivision::factory()
                ->count(rand(2, 6))
                ->city()
                ->active()
                ->forCountry($country->id)
                ->withParent($state->id)
                ->create();


            foreach ($cities as $city) {
                // Create districts for each city
                GeoDivision::factory()
                    ->count(rand(1, 4))
                    ->district()
                    ->active()
                    ->forCountry($country->id)
                    ->withParent($city->id)
                    ->create();
            }
        }
    }
}
