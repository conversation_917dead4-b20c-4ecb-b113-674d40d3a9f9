<?php

namespace Modules\ModelAI\Observers;

use Illuminate\Support\Facades\Cache;
use Modules\ModelAI\Models\ModelTool;

class ModelToolObserver
{
    /**
     * Cache tags for model tools
     */
    private const CACHE_TAGS = ['model-tools', 'model-ai'];

    /**
     * Handle the ModelTool "created" event.
     */
    public function created(ModelTool $modelTool): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelTool "updated" event.
     */
    public function updated(ModelTool $modelTool): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelTool "deleted" event.
     */
    public function deleted(ModelTool $modelTool): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelTool "restored" event.
     */
    public function restored(ModelTool $modelTool): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelTool "force deleted" event.
     */
    public function forceDeleted(ModelTool $modelTool): void
    {
        $this->clearCache();
    }

    /**
     * Clear model tool related cache.
     */
    private function clearCache(): void
    {
        try {
            if (function_exists('enabledCache') && enabledCache()) {
                Cache::tags(self::CACHE_TAGS)->flush();
            }
        } catch (\Exception $e) {
            // Silently handle cache errors
        }
    }
}
