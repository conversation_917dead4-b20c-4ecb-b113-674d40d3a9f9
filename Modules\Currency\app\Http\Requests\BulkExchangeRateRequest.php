<?php

namespace Modules\Currency\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class BulkExchangeRateRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $bulkLimit = setting('currency.currency_bulk_update_limit', 100);

        return [
            'rates' => "required|array|min:1|max:{$bulkLimit}",
            'rates.*.base_currency' => [
                'required',
                'string',
                'size:3',
                'exists:currencies,code',
            ],
            'rates.*.target_currency' => [
                'required',
                'string',
                'size:3',
                'exists:currencies,code',
                'different:rates.*.base_currency',
            ],
            'rates.*.rate' => [
                'required',
                'numeric',
                'min:' . setting('currency.currency_rate_min', '0.00000001'),
                'max:' . setting('currency.currency_rate_max', '999999999.99999999'),
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'rates' => __('Exchange Rates'),
            'rates.*.base_currency' => __('Base Currency'),
            'rates.*.target_currency' => __('Target Currency'),
            'rates.*.rate' => __('Exchange Rate'),
        ];
    }
}
