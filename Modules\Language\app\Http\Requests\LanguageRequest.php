<?php

namespace Modules\Language\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

/**
 * @property mixed $id
 * @property mixed $language_id
 */
class LanguageRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'code' => [
                'required',
                'string',
                'max:20',
                'alpha_dash',
                Rule::unique('languages')->ignore($this->id)->whereNull('deleted_at'),
            ],
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'native_name' => [
                'nullable',
                'string',
                'max:255',
            ],
            'flag' => [
                'nullable',
                'string',
                'max:255',
            ],
            'direction' => [
                'required',
                'string',
                Rule::in(['ltr', 'rtl']),
            ],
            'is_default' => [
                'required',
                'boolean',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(['active', 'inactive']),
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'code' => __('language.code'),
            'name' => __('language.name'),
            'native_name' => __('Native Name'),
            'flag' => __('Flag'),
            'direction' => __('Text Direction'),
            'is_default' => __('language.isDefault'),
            'status' => __('language.status'),
        ];
    }
}
