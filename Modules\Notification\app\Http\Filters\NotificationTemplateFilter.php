<?php

namespace Modules\Notification\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use Modules\Core\Abstracts\AbstractFilter;

class NotificationTemplateFilter extends AbstractFilter
{
    /**
     * Define the filters available for notification templates.
     */
    protected function filters(): array
    {
        return [
            // Basic field filters
            'notification_type_id' => 'exact',
            'channel' => 'exact',
            'locale' => 'exact',
            'status' => 'exact',
            'is_trashed' => 'is_trashed',

            // Search filters
            'subject' => 'like',
            'title' => 'like',
            'content' => 'like',

            // Notification type relation filters
            'notification_type_key' => ['type' => 'custom'],
            'notification_type_name' => ['type' => 'custom'],

            // Date range filters
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
            'updated_from' => ['type' => 'from', 'column' => 'updated_at'],
            'updated_to' => ['type' => 'to', 'column' => 'updated_at'],

            // Custom filters
            'search' => ['type' => 'custom'],
            'has_variables' => ['type' => 'custom'],
            'variable_contains' => ['type' => 'custom'],
            'channels' => ['type' => 'custom'],
            'locales' => ['type' => 'custom'],
        ];
    }

    /**
     * Apply custom filter logic.
     */
    protected function applyCustomFilter(Builder $query, string $field, mixed $value): void
    {
        match ($field) {
            'search' => $this->applySearchFilter($query, $value),
            'notification_type_key' => $this->applyNotificationTypeKeyFilter($query, $value),
            'notification_type_name' => $this->applyNotificationTypeNameFilter($query, $value),
            'has_variables' => $this->applyHasVariablesFilter($query, $value),
            'variable_contains' => $this->applyVariableContainsFilter($query, $value),
            'channels' => $this->applyChannelsFilter($query, $value),
            'locales' => $this->applyLocalesFilter($query, $value),
            default => null,
        };
    }

    /**
     * Apply global search filter across multiple fields.
     */
    protected function applySearchFilter(Builder $query, string $value): void
    {
        $query->where(function ($q) use ($value) {
            $q->where('subject', 'like', "%{$this->escapeLike($value)}%")
              ->orWhere('title', 'like', "%{$this->escapeLike($value)}%")
              ->orWhere('content', 'like', "%{$this->escapeLike($value)}%")
              ->orWhere('channel', 'like', "%{$this->escapeLike($value)}%")
              ->orWhere('locale', 'like', "%{$this->escapeLike($value)}%")
              ->orWhereHas('notificationType', function ($typeQuery) use ($value) {
                  $typeQuery->where('name', 'like', "%{$this->escapeLike($value)}%")
                           ->orWhere('key', 'like', "%{$this->escapeLike($value)}%")
                           ->orWhere('description', 'like', "%{$this->escapeLike($value)}%");
              });
        });
    }

    /**
     * Filter by notification type key.
     */
    protected function applyNotificationTypeKeyFilter(Builder $query, string $value): void
    {
        $query->whereHas('notificationType', function ($typeQuery) use ($value) {
            $typeQuery->where('key', 'like', "%{$this->escapeLike($value)}%");
        });
    }

    /**
     * Filter by notification type name.
     */
    protected function applyNotificationTypeNameFilter(Builder $query, string $value): void
    {
        $query->whereHas('notificationType', function ($typeQuery) use ($value) {
            $typeQuery->where('name', 'like', "%{$this->escapeLike($value)}%");
        });
    }

    /**
     * Filter templates that have or don't have variables.
     */
    protected function applyHasVariablesFilter(Builder $query, mixed $value): void
    {
        $hasVariables = filter_var($value, FILTER_VALIDATE_BOOLEAN);
        
        if ($hasVariables) {
            $query->whereNotNull('variables')
                  ->where(function ($q) {
                      $q->whereJsonLength('variables', '>', 0)
                        ->orWhere('variables', '!=', '[]');
                  });
        } else {
            $query->where(function ($q) {
                $q->whereNull('variables')
                  ->orWhereJsonLength('variables', 0)
                  ->orWhere('variables', '[]');
            });
        }
    }

    /**
     * Filter templates that contain specific variables.
     */
    protected function applyVariableContainsFilter(Builder $query, string $value): void
    {
        $variables = is_array($value) ? $value : explode(',', $value);
        
        $query->where(function ($q) use ($variables) {
            foreach ($variables as $variable) {
                $variable = trim($variable);
                if (!empty($variable)) {
                    $q->orWhereJsonContains('variables', $variable);
                }
            }
        });
    }

    /**
     * Filter by multiple channels.
     */
    protected function applyChannelsFilter(Builder $query, mixed $value): void
    {
        $channels = is_array($value) ? $value : explode(',', $value);
        $channels = array_filter(array_map('trim', $channels));
        
        if (!empty($channels)) {
            $query->whereIn('channel', $channels);
        }
    }

    /**
     * Filter by multiple locales.
     */
    protected function applyLocalesFilter(Builder $query, mixed $value): void
    {
        $locales = is_array($value) ? $value : explode(',', $value);
        $locales = array_filter(array_map('trim', $locales));
        
        if (!empty($locales)) {
            $query->whereIn('locale', $locales);
        }
    }
}
