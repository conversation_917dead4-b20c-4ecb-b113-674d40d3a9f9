<?php

namespace Modules\ModelAI\Models;

use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Modules\ModelAI\Database\Factories\ModelCategoryFactory;

class ModelCategory extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'key',
        'name',
        'description',
        'type',
        'icon',
        'color',
        'sort_order',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = ['deleted_at'];

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive categories.
     */
    public function scopeInactive(Builder $query): Builder
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope a query to only include ModelAI categories.
     */
    public function scopeModelAI(Builder $query): Builder
    {
        return $query->where('type', 'ModelAI');
    }

    /**
     * Scope a query to only include Tools categories.
     */
    public function scopeTools(Builder $query): Builder
    {
        return $query->where('type', 'Tools');
    }

    /**
     * Scope a query to filter by type.
     */
    public function scopeType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Get the AI models that belong to this category.
     */
    public function modelAIs(): BelongsToMany
    {
        return $this->belongsToMany(ModelAI::class, 'model_categorizations', 'model_category_id', 'model_ai_id');
    }

    /**
     * Alias for modelAIs relationship.
     */
    public function models(): BelongsToMany
    {
        return $this->modelAIs();
    }

    /**
     * Get the tools that belong to this category.
     */
    public function tools(): BelongsToMany
    {
        return $this->belongsToMany(ModelTool::class, 'model_tool_categorizations', 'model_category_id', 'model_tool_id');
    }

    /**
     * Check if category is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if category is for ModelAI.
     */
    public function isModelAI(): bool
    {
        return $this->type === 'ModelAI';
    }

    /**
     * Check if category is for Tools.
     */
    public function isTools(): bool
    {
        return $this->type === 'Tools';
    }

    /**
     * Get total model count for this category.
     */
    public function getModelCount(): int
    {
        return $this->models()->count();
    }

    /**
     * Get active model count for this category.
     */
    public function getActiveModelCount(): int
    {
        return $this->models()->where('status', 'active')->count();
    }

    /**
     * Get display name for the category.
     */
    public function getDisplayName(): string
    {
        return $this->name;
    }

    /**
     * Get icon with default fallback.
     */
    public function getIcon(): string
    {
        return $this->icon ?: 'fas fa-folder';
    }

    /**
     * Get color with default fallback.
     */
    public function getColor(): string
    {
        return $this->color ?: '#6b7280';
    }

    /**
     * Check if category has models.
     */
    public function hasModels(): bool
    {
        return $this->models()->exists();
    }

    /**
     * Check if category is draft.
     */
    public function isDraft(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Check if category is inactive.
     */
    public function isInactive(): bool
    {
        return $this->status === 'inactive';
    }



    /**
     * Get categories formatted for dropdown.
     */
    public static function getCategoriesForDropdown(): Collection
    {
        return self::active()
            ->select(['key', 'name'])
            ->ordered()
            ->get()
            ->map(fn (ModelCategory $category) => [
                'value' => $category->key,
                'label' => __("{$category->name}"),
            ]);
    }


    /**
     * Get categories with their associated models.
     *
     * @return Collection
     */
    public static function getCategoriesWithModels(): Collection
    {
        return self::query()
            ->with(['modelAIs' => function ($query) {
                $query->active()->ordered()
                ->select(['id', 'key', 'name']);
            }, 'modelAIs.service' => function ($query) {
                $query->select(['id', 'model_ai_id', 'cost_per_request', 'cost_per1k_tokens', 'billing_type']);
            }])
            ->select(['id', 'key', 'name', 'color', 'icon'])
            ->active()
            ->ordered()
            ->get()
            ->map(function (ModelCategory $category) {
                $category->modelAIs->each(function (ModelAI $model) {
                    if ($model->service) {
                        $model->service->makeHidden(['id', 'model_ai_id']);
                    }
                    $model->makeHidden(['id']);
                });
                $category->makeHidden(['id']);
                $category->name = __($category->name);
                return $category;
            });
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): ModelCategoryFactory
    {
        return ModelCategoryFactory::new();
    }
}
