<?php

namespace Modules\Currency\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Modules\Currency\Models\Currency;
use Modules\Currency\Models\ExchangeRate;
use Modules\Currency\Services\CurrencyService;
use PHPUnit\Framework\Attributes\Test;

class CurrencyServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CurrencyService $currencyService;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Currency module
        $this->artisan('migrate', ['--path' => 'Modules/Currency/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);

        $this->currencyService = app(CurrencyService::class);
    }

    #[Test]
    public function it_can_get_active_currencies()
    {
        Currency::factory()->create(['status' => 'active']);
        Currency::factory()->create(['status' => 'active']);
        Currency::factory()->create(['status' => 'inactive']);

        $currencies = $this->currencyService->getActiveCurrencies();

        $this->assertCount(2, $currencies);
        $currencies->each(function ($currency) {
            $this->assertEquals('active', $currency->status);
        });
    }

    #[Test]
    public function it_caches_active_currencies_when_cache_enabled()
    {
        // Mock enabledCache function to return true
        $this->app->bind('enabledCache', function () {
            return function () {
                return true;
            };
        });

        // Mock setting function
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_list_cache_ttl') {
                    return 3600;
                }
                return $default;
            };
        });

        Currency::factory()->create(['status' => 'active']);

        // Clear cache first
        Cache::flush();

        // First call should cache the result
        $currencies1 = $this->currencyService->getActiveCurrencies();
        
        // Second call should return cached result
        $currencies2 = $this->currencyService->getActiveCurrencies();

        $this->assertEquals($currencies1->count(), $currencies2->count());
    }

    #[Test]
    public function it_can_get_default_currency()
    {
        // Mock setting function
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_default') {
                    return 'USD';
                }
                return $default;
            };
        });

        $usdCurrency = Currency::factory()->create(['code' => 'USD', 'status' => 'active']);
        Currency::factory()->create(['code' => 'EUR', 'status' => 'active']);

        $defaultCurrency = $this->currencyService->getDefaultCurrency();

        $this->assertNotNull($defaultCurrency);
        $this->assertEquals($usdCurrency->id, $defaultCurrency->id);
        $this->assertEquals('USD', $defaultCurrency->code);
    }

    #[Test]
    public function it_returns_null_when_default_currency_not_found()
    {
        // Mock setting function
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_default') {
                    return 'XYZ';
                }
                return $default;
            };
        });

        Currency::factory()->create(['code' => 'USD', 'status' => 'active']);

        $defaultCurrency = $this->currencyService->getDefaultCurrency();

        $this->assertNull($defaultCurrency);
    }

    #[Test]
    public function it_can_find_currency_by_code()
    {
        $currency = Currency::factory()->create(['code' => 'USD', 'status' => 'active']);

        $foundCurrency = $this->currencyService->findByCode('USD');

        $this->assertNotNull($foundCurrency);
        $this->assertEquals($currency->id, $foundCurrency->id);
    }

    #[Test]
    public function it_returns_null_when_currency_not_found_by_code()
    {
        $foundCurrency = $this->currencyService->findByCode('XYZ');

        $this->assertNull($foundCurrency);
    }

    #[Test]
    public function it_can_convert_amount_between_currencies()
    {
        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'rate' => 0.85,
        ]);

        $convertedAmount = $this->currencyService->convertAmount(100, 'USD', 'EUR');

        $this->assertEquals(85.0, $convertedAmount);
    }

    #[Test]
    public function it_returns_same_amount_for_same_currency_conversion()
    {
        $convertedAmount = $this->currencyService->convertAmount(100, 'USD', 'USD');

        $this->assertEquals(100.0, $convertedAmount);
    }

    #[Test]
    public function it_returns_null_when_exchange_rate_not_found()
    {
        $convertedAmount = $this->currencyService->convertAmount(100, 'USD', 'XYZ');

        $this->assertNull($convertedAmount);
    }

    #[Test]
    public function it_can_get_exchange_rate()
    {
        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'rate' => 0.85,
        ]);

        $rate = $this->currencyService->getExchangeRate('USD', 'EUR');

        $this->assertEquals(0.85, $rate);
    }

    #[Test]
    public function it_returns_one_for_same_currency_exchange_rate()
    {
        $rate = $this->currencyService->getExchangeRate('USD', 'USD');

        $this->assertEquals(1.0, $rate);
    }

    #[Test]
    public function it_can_format_amount_with_currency()
    {
        $currency = Currency::factory()->create([
            'code' => 'USD',
            'symbol' => '$',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ]);

        $formatted = $this->currencyService->formatAmount(1234.56, 'USD');

        $this->assertEquals('$ 1,234.56', $formatted);
    }

    #[Test]
    public function it_formats_amount_with_fallback_when_currency_not_found()
    {
        $formatted = $this->currencyService->formatAmount(1234.56, 'XYZ');

        $this->assertEquals('1,234.56 XYZ', $formatted);
    }

    #[Test]
    public function it_can_get_supported_currency_codes()
    {
        Currency::factory()->create(['code' => 'USD', 'status' => 'active']);
        Currency::factory()->create(['code' => 'EUR', 'status' => 'active']);
        Currency::factory()->create(['code' => 'GBP', 'status' => 'inactive']);

        $codes = $this->currencyService->getSupportedCurrencyCodes();

        $this->assertCount(2, $codes);
        $this->assertContains('USD', $codes);
        $this->assertContains('EUR', $codes);
        $this->assertNotContains('GBP', $codes);
    }

    #[Test]
    public function it_can_get_currencies_for_dropdown()
    {
        Currency::factory()->create(['code' => 'USD', 'name' => 'US Dollar', 'status' => 'active']);
        Currency::factory()->create(['code' => 'EUR', 'name' => 'Euro', 'status' => 'active']);
        Currency::factory()->create(['code' => 'GBP', 'name' => 'British Pound', 'status' => 'inactive']);

        $dropdown = $this->currencyService->getCurrenciesForDropdown();

        $this->assertCount(2, $dropdown);
        $dropdown->each(function ($currency) {
            $this->assertArrayHasKey('code', $currency);
            $this->assertArrayHasKey('name', $currency);
        });
    }

    #[Test]
    public function it_can_update_exchange_rate()
    {
        Currency::factory()->create(['code' => 'USD']);
        Currency::factory()->create(['code' => 'EUR']);

        $exchangeRate = $this->currencyService->updateExchangeRate('USD', 'EUR', 0.85);

        $this->assertInstanceOf(ExchangeRate::class, $exchangeRate);
        $this->assertEquals('USD', $exchangeRate->base_currency);
        $this->assertEquals('EUR', $exchangeRate->target_currency);
        $this->assertEquals(0.85, $exchangeRate->rate);
        $this->assertNotNull($exchangeRate->fetched_at);
    }

    #[Test]
    public function it_can_clear_cache()
    {
        Cache::put('currencies.active.list', 'test_data');
        Cache::put('currencies.default', 'test_data');

        $this->currencyService->clearCache();

        $this->assertFalse(Cache::has('currencies.active.list'));
        $this->assertFalse(Cache::has('currencies.default'));
    }

    #[Test]
    public function it_can_clear_exchange_rate_cache()
    {
        Cache::put('exchange_rates.USD.EUR', 0.85);
        Cache::put('exchange_rates.EUR.USD', 1.18);

        $this->currencyService->clearExchangeRateCache();

        $this->assertFalse(Cache::has('exchange_rates.USD.EUR'));
        $this->assertFalse(Cache::has('exchange_rates.EUR.USD'));
    }

    #[Test]
    public function it_can_find_currency_by_symbol()
    {
        $currency = Currency::factory()->create(['symbol' => '$', 'status' => 'active']);

        $foundCurrency = $this->currencyService->findBySymbol('$');

        $this->assertNotNull($foundCurrency);
        $this->assertEquals($currency->id, $foundCurrency->id);
    }

    #[Test]
    public function it_returns_null_when_currency_not_found_by_symbol()
    {
        $foundCurrency = $this->currencyService->findBySymbol('¥');

        $this->assertNull($foundCurrency);
    }

    #[Test]
    public function it_can_check_if_currency_is_supported()
    {
        Currency::factory()->create(['code' => 'USD', 'status' => 'active']);
        Currency::factory()->create(['code' => 'EUR', 'status' => 'inactive']);

        $this->assertTrue($this->currencyService->isCurrencySupported('USD'));
        $this->assertTrue($this->currencyService->isCurrencySupported('usd')); // case insensitive
        $this->assertFalse($this->currencyService->isCurrencySupported('EUR')); // inactive
        $this->assertFalse($this->currencyService->isCurrencySupported('XYZ')); // not exists
    }

    #[Test]
    public function it_can_get_currency_display_name()
    {
        $currency = Currency::factory()->create([
            'code' => 'USD',
            'name' => 'US Dollar',
            'symbol' => '$',
            'status' => 'active',
        ]);

        $displayName = $this->currencyService->getCurrencyDisplayName('USD');

        $this->assertEquals('US Dollar ($)', $displayName);
    }

    #[Test]
    public function it_returns_code_when_currency_not_found_for_display_name()
    {
        $displayName = $this->currencyService->getCurrencyDisplayName('XYZ');

        $this->assertEquals('XYZ', $displayName);
    }

    #[Test]
    public function it_caches_exchange_rates()
    {
        // Mock setting function
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_cache_ttl') {
                    return 300;
                }
                return $default;
            };
        });

        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'rate' => 0.85,
        ]);

        // Clear cache first
        Cache::flush();

        // First call should cache the result
        $rate1 = $this->currencyService->getExchangeRate('USD', 'EUR');
        
        // Second call should return cached result
        $rate2 = $this->currencyService->getExchangeRate('USD', 'EUR');

        $this->assertEquals($rate1, $rate2);
        $this->assertEquals(0.85, $rate1);
    }
}
