<?php

namespace Modules\Location\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class GeoDivisionFilter extends AbstractFilter
{
    protected function filters(): array
    {
        return [
            'name' => 'like',
            'native_name' => 'like',
            'code' => 'like',
            'type' => 'exact',
            'country_id' => 'exact',
            'parent_id' => 'exact',
            'level' => 'exact',
            'postal_code' => 'like',
            'status' => 'exact',
            'is_trashed' => 'exact',
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
        ];
    }
}
