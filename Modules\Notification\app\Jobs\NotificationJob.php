<?php

namespace Modules\Notification\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Carbon;
use Modules\Notification\Facades\NotificationFacade;
use Modules\Notification\Models\NotificationPreference;
use Modules\Notification\Models\NotificationTemplate;
use Modules\Notification\Models\NotificationType;
use Modules\User\Models\User;
use Throwable;

class NotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $connection;
    public $queue;
    public int $tries;
    public int $retryAfter;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public NotificationType $notificationType,
        public string           $channel,
        public array            $recipient,
        public array            $eventData
    )
    {
        $this->configureQueue();
    }

    protected function configureQueue(): void
    {
        // Get cached queue config - fix settings key structure
        $this->connection = setting('notifications.queue.connection', 'database');
        $this->queue = setting('notifications.queue.name', 'notifications');
        $this->tries = setting_int('notifications.queue.tries', 3);
        $this->retryAfter = setting_int('notifications.queue.retry_after', 90);
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Throwable
     */
    public function handle(): void
    {
        if (!$this->shouldSend()) {
            return;
        }

        $template = $this->resolveTemplate();

        if (!$template) {
            $this->logTemplateNotFound();
            return;
        }

        $content = $this->renderContent($template);

        $this->sendViaChannel($content);
    }


    protected function shouldSend(): bool
    {
        if (!setting_bool('notifications.enabled', true)) {
            return false;
        }

        if (!setting_bool("notifications.{$this->channel}.enabled", false)) {
            return false;
        }

        if ($this->notificationType->status !== 'active') {
            return false;
        }

        $preference = $this->getUserPreference();

        if (!$preference->enabled) {
            return false;
        }

        return $this->withinQuietHours($preference);
    }

    protected function getUserPreference(): NotificationPreference
    {
        if (!empty($this->recipient['username'])) {
            return new NotificationPreference([
                'enabled' => true,
                'quiet_hours_start' => null,
                'quiet_hours_end' => null,
                'timezone' => setting('general.timezone', config('app.timezone'))
            ]);
        }

        return NotificationPreference::firstOrCreate(
            [
                'notifiable_type' => User::class,
                'notifiable_id' => $this->recipient['id'],
                'notification_type_id' => $this->notificationType->id,
                'channel' => $this->channel,
            ],
            [
                'enabled' => true,
                'quiet_hours_start' => null,
                'quiet_hours_end' => null,
                'timezone' => $this->recipient['preferences']->timezone ?? setting('general.timezone', config('app.timezone')),
            ]
        );
    }

    protected function withinQuietHours(NotificationPreference $preference): bool
    {
        if (!$preference->quiet_hours_start || !$preference->quiet_hours_end) {
            return true;
        }

        $timezone = $preference->timezone;
        $now = Carbon::now($timezone);
        $start = Carbon::parse($preference->quiet_hours_start, $timezone);
        $end = Carbon::parse($preference->quiet_hours_end, $timezone);

        if ($start->greaterThan($end)) {
            return $now->lessThan($end) || $now->greaterThanOrEqualTo($start);
        }

        return !$now->between($start, $end);
    }


    protected function resolveTemplate(): ?NotificationTemplate
    {
        $locale = $this->resolveUserLocale();

        return NotificationTemplate::where('notification_type_id', $this->notificationType->id)
            ->where('channel', $this->channel)
            ->where('locale', $locale)
            ->where('status', 'active')
            ->first();
    }

    /**
     * Resolve user locale với ưu tiên:
     * 1. User preferences
     * 2. Setting language default
     * 3. Config app.locale
     */
    protected function resolveUserLocale(): string
    {
        // Ưu tiên 1: User preferences
        if (!empty($this->recipient) && isset($this->recipient['preferences']['locale'])) {
            return $this->recipient['preferences']['locale'];
        }

        // Ưu tiên 2: Setting language default
        $defaultLanguage = setting('setting.language', null);
        if ($defaultLanguage) {
            return $defaultLanguage;
        }

        // Ưu tiên 3: Config app.locale (fallback)
        return config('app.locale', 'en');
    }


    protected function logTemplateNotFound(): void
    {
        $notifiableId = $this->recipient['id'] ?? 'guest';

        logger()->error("Notification template not found", [
            'type' => $this->notificationType->key,
            'channel' => $this->channel,
            'locale' => $this->resolveUserLocale(),
            'notifiable' => $notifiableId
        ]);
    }

    protected function renderContent(NotificationTemplate $template): array
    {
        return [
            'subject' => $this->replacePlaceholders($template->subject),
            'title' => $this->replacePlaceholders($template->title),
            'content' => $this->replacePlaceholders($template->content),
        ];
    }

    protected function replacePlaceholders(?string $text): ?string
    {
        if (!$text) return $text;

        return preg_replace_callback('/\{(\w+)\}/', function ($matches) {
            $key = $matches[1];

            // First check if the key exists in eventData
            if (is_array($this->eventData) && array_key_exists($key, $this->eventData)) {
                return $this->eventData[$key];
            }

            if (is_array($this->eventData)) {
                // For verification_code_sent, check if eventData has 'active_code' and we need 'code'
                if ($key === 'code' && isset($this->eventData['active_code'])) {
                    return $this->eventData['active_code'];
                }
            }

            // Common placeholders
            return match ($key) {
                'username' => $this->recipient['username'] ?? $this->recipient['name'] ?? 'Guest',
                'full_name' => $this->recipient['full_name'] ?? $this->recipient['last_name'].' '.$this->recipient['first_name'],
                'email' => $this->recipient['email'] ?? null,
                'app_name' => setting('general.app_name', config('app.name', 'Laravel ProCMS')),
                'app_url' => setting('general.app_url', config('app.url', 'http://localhost')),
                'expires_in' => '15',
                default => $matches[0],
            };
        }, $text);
    }

    /**
     * @throws Throwable
     */
    protected function sendViaChannel(array $content): void
    {
        try {
            $driver = app("notifications.channel.{$this->channel}");

            // Thêm notification_type_id vào data cho database channel
            $eventData = array_merge($this->eventData, [
                'notification_type_id' => $this->notificationType->id,
                'notification_type_key' => $this->notificationType->key
            ]);

            $driver->send($this->recipient, $content, $eventData);
        } catch (Throwable $e) {
            $this->logChannelError($e);
            throw $e; // Re-throw để queue retry
        }
    }

    protected function logChannelError(Throwable $e): void
    {
        $notifiableId = $this->recipient['id'] ?? 'guest';

        logger()->error("Notification channel error", [
            'channel' => $this->channel,
            'error' => $e->getMessage(),
            'type' => $this->notificationType->key,
            'notifiable' => $this->recipient
        ]);

        // Gửi thông báo cho admin
        if (config('notifications.alert_admin_on_failure')) {
            $recipientInfo = (empty($this->recipient) ? "Guest" : isset($this->recipient['username'])) ? $this->recipient['username'] : "Unknown";
            NotificationFacade::trigger('notifications.channel_failed', [
                'channel' => $this->channel,
                'error' => $e->getMessage(),
                'notification_type' => $this->notificationType->key,
                'recipient' => $recipientInfo,
            ]);
        }
    }

    protected function handleError(\Throwable $e): void
    {
        $this->logChannelError($e);

        // Fail the job
        $this->fail($e);
    }
}
