<?php

namespace Modules\Notification\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Notification\Models\Notification;
use Modules\Notification\Models\NotificationType;
use App\Models\User;

class NotificationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Notification::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $channels = ['database', 'email', 'sms', 'push'];
        $priorities = ['low', 'normal', 'high', 'urgent'];
        $statuses = ['pending', 'sent', 'failed', 'scheduled'];

        return [
            'notification_type_id' => NotificationType::factory(),
            'notifiable_type' => User::class,
            'notifiable_id' => User::factory(),
            'channel' => $this->faker->randomElement($channels),
            'title' => $this->faker->sentence(4),
            'content' => $this->faker->paragraph(),
            'data' => [
                'action_url' => $this->faker->url(),
                'action_text' => $this->faker->words(2, true),
                'additional_info' => $this->faker->sentence(),
            ],
            'metadata' => [
                'source' => $this->faker->randomElement(['system', 'user', 'api']),
                'ip_address' => $this->faker->ipv4(),
                'user_agent' => $this->faker->userAgent(),
            ],
            'priority' => $this->faker->randomElement($priorities),
            'status' => $this->faker->randomElement($statuses),
            'retry_count' => 0,
        ];
    }

    /**
     * Indicate that the notification is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'sent_at' => null,
            'failed_at' => null,
            'read_at' => null,
        ]);
    }

    /**
     * Indicate that the notification is sent.
     */
    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'sent',
            'sent_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'failed_at' => null,
        ]);
    }

    /**
     * Indicate that the notification is failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'failed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'failure_reason' => $this->faker->sentence(),
            'retry_count' => $this->faker->numberBetween(1, 3),
            'sent_at' => null,
        ]);
    }

    /**
     * Indicate that the notification is scheduled.
     */
    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'scheduled',
            'scheduled_at' => $this->faker->dateTimeBetween('now', '+1 week'),
            'sent_at' => null,
            'failed_at' => null,
        ]);
    }

    /**
     * Indicate that the notification is read.
     */
    public function read(): static
    {
        return $this->state(fn (array $attributes) => [
            'read_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the notification is unread.
     */
    public function unread(): static
    {
        return $this->state(fn (array $attributes) => [
            'read_at' => null,
        ]);
    }

    /**
     * Indicate that the notification has high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'high',
        ]);
    }

    /**
     * Indicate that the notification has urgent priority.
     */
    public function urgent(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'urgent',
        ]);
    }

    /**
     * Indicate that the notification has low priority.
     */
    public function lowPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'low',
        ]);
    }

    /**
     * Indicate that the notification is for email channel.
     */
    public function email(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'email',
            'data' => array_merge($attributes['data'] ?? [], [
                'subject' => $this->faker->sentence(6),
                'from_name' => $this->faker->name(),
                'reply_to' => $this->faker->email(),
            ]),
        ]);
    }

    /**
     * Indicate that the notification is for SMS channel.
     */
    public function sms(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'sms',
            'content' => $this->faker->text(160), // SMS length limit
            'data' => array_merge($attributes['data'] ?? [], [
                'phone_number' => $this->faker->phoneNumber(),
                'sender_id' => $this->faker->company(),
            ]),
        ]);
    }

    /**
     * Indicate that the notification is for push channel.
     */
    public function push(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'push',
            'data' => array_merge($attributes['data'] ?? [], [
                'icon' => 'notification-icon.png',
                'sound' => 'default',
                'badge' => $this->faker->numberBetween(1, 10),
                'click_action' => $this->faker->url(),
            ]),
        ]);
    }

    /**
     * Indicate that the notification is for database channel.
     */
    public function database(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'database',
        ]);
    }

    /**
     * Create notification with specific notifiable.
     */
    public function forNotifiable($notifiable): static
    {
        return $this->state(fn (array $attributes) => [
            'notifiable_type' => get_class($notifiable),
            'notifiable_id' => $notifiable->id,
        ]);
    }

    /**
     * Create notification with specific type.
     */
    public function ofType(NotificationType $type): static
    {
        return $this->state(fn (array $attributes) => [
            'notification_type_id' => $type->id,
        ]);
    }

    /**
     * Create notification with retry attempts.
     */
    public function withRetries(int $count = 1): static
    {
        return $this->state(fn (array $attributes) => [
            'retry_count' => $count,
            'status' => 'failed',
            'failed_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
            'failure_reason' => $this->faker->sentence(),
        ]);
    }

    /**
     * Create welcome notification.
     */
    public function welcome(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'Welcome to ' . config('app.name', 'Laravel ProCMS'),
            'content' => 'Thank you for joining us! We\'re excited to have you on board.',
            'data' => [
                'action_url' => route('dashboard', [], false),
                'action_text' => 'Get Started',
                'welcome_bonus' => true,
            ],
            'priority' => 'normal',
        ]);
    }

    /**
     * Create security alert notification.
     */
    public function securityAlert(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'Security Alert',
            'content' => 'We detected a new login to your account from a new device.',
            'data' => [
                'alert_type' => 'login',
                'ip_address' => $this->faker->ipv4(),
                'location' => $this->faker->city() . ', ' . $this->faker->country(),
                'device' => $this->faker->userAgent(),
            ],
            'priority' => 'high',
        ]);
    }
}
