<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('code', 3)->unique(); // ISO 4217: VND, USD, EUR
            $table->string('symbol', 10);        // ₫, $, €
            $table->string('name', 50);          // Vietnamese Dong, US Dollar
            $table->integer('decimal_digits')->default(0); // VND = 0, USD = 2
            $table->string('decimal_separator', 5)->default('.');  // . hoặc ,
            $table->string('thousands_separator', 5)->default(','); // , hoặc .
            $table->string('status')->default('active'); // Để tắt tạm thời
            $table->integer('sort_order')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exchange_rates');
        Schema::dropIfExists('currencies');
    }
};
