<?php

namespace Modules\Notification\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseFormRequest;

/**
 * @property mixed $id
 * @property mixed $notification_type_id
 */
class NotificationTypeRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'key' => [
                'required',
                'string',
                'max:255',
                'alpha_dash',
                Rule::unique('notification_types')->ignore($this->id)->whereNull('deleted_at'),
            ],
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'channels' => [
                'required',
                'array',
                'min:1',
            ],
            'channels.*' => [
                'required',
                'string',
                Rule::in(['database', 'email', 'sms', 'push', 'telegram']),
            ],
            'default_settings' => [
                'nullable',
                'array',
            ],
            'default_settings.priority' => [
                'nullable',
                'string',
                Rule::in(['low', 'normal', 'high', 'urgent']),
            ],
            'default_settings.auto_send' => [
                'nullable',
                'boolean',
            ],
            'default_settings.retry_attempts' => [
                'nullable',
                'integer',
                'min:0',
                'max:10',
            ],
            'default_settings.delay_seconds' => [
                'nullable',
                'integer',
                'min:0',
                'max:86400', // 24 hours
            ],
            'is_system' => [
                'sometimes',
                'boolean',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(['active', 'inactive']),
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'key' => __('Notification Key'),
            'name' => __('Notification Name'),
            'description' => __('Description'),
            'channels' => __('Notification Channels'),
            'channels.*' => __('Notification Channel'),
            'default_settings' => __('Default Settings'),
            'default_settings.priority' => __('Priority'),
            'default_settings.auto_send' => __('Auto Send'),
            'default_settings.retry_attempts' => __('Retry Attempts'),
            'default_settings.delay_seconds' => __('Delay (seconds)'),
            'is_system' => __('System Notification'),
            'status' => __('Status'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'key.alpha_dash' => 'The :attribute may only contain letters, numbers, dashes and underscores.',
            'key.unique' => 'A notification type with this key already exists.',
            'channels.required' => 'At least one notification channel must be selected.',
            'channels.min' => 'At least one notification channel must be selected.',
            'channels.*.in' => 'The selected channel is invalid.',
            'default_settings.priority.in' => 'Priority must be one of: low, normal, high, urgent.',
            'default_settings.retry_attempts.max' => 'Retry attempts cannot exceed 10.',
            'default_settings.delay_seconds.max' => 'Delay cannot exceed 24 hours (86400 seconds).',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Prevent modification of system notification types
            if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
                $notificationType = $this->route('notification_type') ?? $this->route('notification');
                if ($notificationType && $notificationType->is_system) {
                    // Allow only status changes for system types
                    $allowedFields = ['status'];
                    $inputFields = array_keys($this->all());
                    $restrictedFields = array_diff($inputFields, $allowedFields);
                    
                    if (!empty($restrictedFields)) {
                        $validator->errors()->add(
                            'is_system',
                            'System notification types can only have their status modified.'
                        );
                    }
                }
            }

            // Validate channels array contains unique values
            if ($this->has('channels') && is_array($this->channels)) {
                $channels = $this->channels;
                if (count($channels) !== count(array_unique($channels))) {
                    $validator->errors()->add('channels', 'Duplicate channels are not allowed.');
                }
            }

            // Validate default_settings structure
            if ($this->has('default_settings') && is_array($this->default_settings)) {
                $settings = $this->default_settings;
                
                // Check for unknown settings keys
                $allowedKeys = ['priority', 'auto_send', 'retry_attempts', 'delay_seconds'];
                $unknownKeys = array_diff(array_keys($settings), $allowedKeys);
                
                if (!empty($unknownKeys)) {
                    $validator->errors()->add(
                        'default_settings',
                        'Unknown settings keys: ' . implode(', ', $unknownKeys)
                    );
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure channels is an array
        if ($this->has('channels') && !is_array($this->channels)) {
            $this->merge([
                'channels' => is_string($this->channels) ? explode(',', $this->channels) : []
            ]);
        }

        // Clean up channels array
        if ($this->has('channels') && is_array($this->channels)) {
            $this->merge([
                'channels' => array_filter(array_map('trim', $this->channels))
            ]);
        }

        // Convert string booleans to actual booleans
        if ($this->has('is_system')) {
            $this->merge([
                'is_system' => filter_var($this->is_system, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false
            ]);
        }

        if ($this->has('default_settings.auto_send')) {
            $autoSend = data_get($this->default_settings, 'auto_send');
            $this->merge([
                'default_settings' => array_merge($this->default_settings ?? [], [
                    'auto_send' => filter_var($autoSend, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false
                ])
            ]);
        }
    }
}
