<?php

namespace Modules\Location\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class CountryFilter extends AbstractFilter
{
    protected function filters(): array
    {
        return [
            'name' => 'like',
            'native_name' => 'like',
            'iso_code_2' => 'exact',
            'iso_code_3' => 'exact',
            'iso_numeric' => 'exact',
            'phone_code' => 'like',
            'region' => 'exact',
            'subregion' => 'exact',
            'status' => 'exact',
            'is_trashed' => 'exact',
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
        ];
    }
}
