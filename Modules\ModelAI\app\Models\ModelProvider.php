<?php

namespace Modules\ModelAI\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;

class ModelProvider extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'key',
        'name',
        'description',
        'base_url',
        'api_key',
        'credentials',
        'status',
    ];

    protected $casts = [
         'api_key' => 'encrypted',
        'credentials' => 'encrypted',
    ];

    protected $hidden = [
        'credentials',
    ];

    /**
     * Get all models for this provider.
     */
    public function modelAIs(): HasMany
    {
        return $this->hasMany(ModelAI::class, 'model_provider_id');
    }

    /**
     * Get active models for this provider.
     */
    public function activeModelAIs(): HasMany
    {
        return $this->modelAIs()->where('status', 'active');
    }

    /**
     * Scope for active providers.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for inactive providers.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Get the provider's display status.
     */
    protected function displayStatus(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->status) {
                'active' => 'Active',
                'inactive' => 'Inactive',
                default => ucfirst($this->status)
            }
        );
    }



    /**
     * Check if provider is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if provider is inactive.
     */
    public function isInactive(): bool
    {
        return $this->status === 'inactive';
    }

    /**
     * Get provider's API key.
     */
    public function getApiKey(): ?string
    {
        return $this->api_key;
    }

    /**
     * Set provider's API key.
     */
    public function setApiKey(string $apiKey): void
    {
        $this->api_key = $apiKey;
    }

    /**
     * Get provider's organization ID from credentials.
     */
    public function getOrganizationId(): ?string
    {
        return $this->credentials['organization_id'] ?? null;
    }

    /**
     * Set provider's organization ID in credentials.
     */
    public function setOrganizationId(string $organizationId): void
    {
        $credentials = $this->credentials ?? [];
        $credentials['organization_id'] = $organizationId;
        $this->credentials = $credentials;
    }

    /**
     * Get all credentials (for admin use only).
     */
    public function getAllCredentials(): array
    {
        return $this->credentials ?? [];
    }

    /**
     * Set all credentials.
     */
    public function setCredentials(array $credentials): void
    {
        $this->credentials = $credentials;
    }

    /**
     * Get masked API key for display.
     */
    public function getMaskedApiKey(): ?string
    {
        $apiKey = $this->getApiKey();
        if (!$apiKey) {
            return null;
        }

        if (strlen($apiKey) <= 8) {
            return str_repeat('*', strlen($apiKey));
        }

        return substr($apiKey, 0, 4) . str_repeat('*', strlen($apiKey) - 8) . substr($apiKey, -4);
    }

    /**
     * Get the full API URL for this provider.
     */
    public function getApiUrl(string $endpoint = ''): string
    {
        $baseUrl = rtrim($this->base_url, '/');
        $endpoint = ltrim($endpoint, '/');

        return $endpoint ? $baseUrl . '/' . $endpoint : $baseUrl;
    }

    /**
     * Check if provider has valid credentials.
     */
    public function hasValidCredentials(): bool
    {
        // Check if provider has API key (most common case)
        if (!empty($this->api_key)) {
            return true;
        }

        // For providers that don't use API key (like AWS), check credentials
        $credentials = $this->credentials ?? [];

        // AWS Bedrock uses access keys
        if ($this->key === 'aws') {
            return !empty($credentials['access_key_id']) && !empty($credentials['secret_access_key']);
        }

        // Ollama doesn't need credentials
        if ($this->key === 'ollama') {
            return true;
        }

        // For other providers, check if they have any credentials
        return !empty($credentials);
    }

    /**
     * Get provider statistics.
     */
    public function getStats(): array
    {
        return [
            'total_models' => $this->modelAIs()->count(),
            'active_models' => $this->activeModelAIs()->count(),
            'inactive_models' => $this->modelAIs()->where('status', '!=', 'active')->count(),
        ];
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Modules\ModelAI\Database\Factories\ModelProviderFactory::new();
    }
}
