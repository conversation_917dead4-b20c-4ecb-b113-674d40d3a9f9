<?php

namespace Modules\Location\Console\Commands;

use Illuminate\Console\Command;

class ClearLocationCacheCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'location:clear-cache';

    /**
     * The console command description.
     */
    protected $description = 'Clear location cache';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Clear location cache logic here
        $this->info('Location cache cleared successfully.');
    }
}
