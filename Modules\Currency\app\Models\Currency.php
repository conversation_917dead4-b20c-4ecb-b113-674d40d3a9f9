<?php

namespace Modules\Currency\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Modules\Currency\Database\Factories\CurrencyFactory;

class Currency extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'code',
        'symbol',
        'name',
        'decimal_digits',
        'decimal_separator',
        'thousands_separator',
        'status',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'decimal_digits' => 'integer',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [];

    /**
     * Scope a query to only include active currencies.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }



    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get exchange rates where this currency is the base currency.
     */
    public function baseExchangeRates(): HasMany
    {
        return $this->hasMany(ExchangeRate::class, 'base_currency', 'code');
    }

    /**
     * Get exchange rates where this currency is the target currency.
     */
    public function targetExchangeRates(): HasMany
    {
        return $this->hasMany(ExchangeRate::class, 'target_currency', 'code');
    }

    /**
     * Format amount according to currency settings.
     */
    public function formatAmount(float $amount): string
    {
        $formattedAmount = number_format(
            $amount,
            $this->decimal_digits,
            $this->decimal_separator,
            $this->thousands_separator
        );

        return $this->symbol . ' ' . $formattedAmount;
    }

    /**
     * Get display name with symbol.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name . ' (' . $this->symbol . ')';
    }



    /**
     * Check if currency is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if this currency is the default currency.
     */
    public function isDefault(): bool
    {
        $defaultCode = setting('currency.currency_default', 'USD');
        return $this->code === $defaultCode;
    }

    public static function findByCode(string $code): ?Currency
    {
        return self::query()->where('code', $code)->active()->first();
    }

    public static function getActiveCurrencies(): Collection
    {
        return self::query()
                    ->active()
                    ->ordered()
                    ->select(['code', 'symbol', 'name', 'decimal_digits', 'decimal_separator', 'thousands_separator'])
                    ->get();
    }

    /**
     * Scope to get default currency.
     */
    public function scopeDefault($query)
    {
        $defaultCode = setting('currency.currency_default', 'USD');
        return $query->where('code', $defaultCode);
    }

    /**
     * Get currencies formatted for dropdown.
     */
    public static function getCurrenciesForDropdown(): array
    {
        return self::active()
            ->ordered()
            ->get()
            ->pluck('name', 'code')
            ->map(function ($name, $code) {
                $currency = static::where('code', $code)->first();
                return $name . ' (' . $currency->symbol . ')';
            })
            ->toArray();
    }

    /**
     * Get supported currency codes.
     */
    public static function getSupportedCurrencyCodes(): array
    {
        return self::active()->pluck('code')->toArray();
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): CurrencyFactory
    {
        return CurrencyFactory::new();
    }
}
