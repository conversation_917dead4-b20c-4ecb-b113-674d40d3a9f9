<?php

namespace Modules\Notification\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Notification\Database\Factories\NotificationTypeFactory;

class NotificationType extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'key',
        'name',
        'description',
        'channels',
        'default_settings',
        'is_system',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'channels' => 'array',
        'default_settings' => 'array',
        'is_system' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = ['deleted_at'];

    /**
     * Scope a query to only include active notification types.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include system notification types.
     */
    public function scopeSystem(Builder $query): Builder
    {
        return $query->where('is_system', true);
    }

    /**
     * Scope a query to only include non-system notification types.
     */
    public function scopeNonSystem(Builder $query): Builder
    {
        return $query->where('is_system', false);
    }

    /**
     * Scope a query to filter by channel.
     */
    public function scopeByChannel(Builder $query, string $channel): Builder
    {
        return $query->whereJsonContains('channels', $channel);
    }

    /**
     * Get the templates for this notification type.
     */
    public function templates(): HasMany
    {
        return $this->hasMany(NotificationTemplate::class);
    }

    /**
     * Get the active templates for this notification type.
     */
    public function activeTemplates(): HasMany
    {
        return $this->templates()->where('status', 'active');
    }

    /**
     * Get the preferences for this notification type.
     */
    public function preferences(): HasMany
    {
        return $this->hasMany(NotificationPreference::class);
    }

    /**
     * Get template for specific channel and locale.
     */
    public function getTemplateForChannel(string $channel, string $locale = 'en'): ?NotificationTemplate
    {
        return $this->templates()
            ->where('channel', $channel)
            ->where('locale', $locale)
            ->where('status', 'active')
            ->first();
    }

    /**
     * Check if notification type supports a specific channel.
     */
    public function supportsChannel(string $channel): bool
    {
        return in_array($channel, $this->channels ?? []);
    }

    /**
     * Get available channels as array.
     */
    public function getAvailableChannels(): array
    {
        return $this->channels ?? [];
    }

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        // Prevent deletion of system notification types
        static::deleting(function ($notificationType) {
            if ($notificationType->is_system) {
                throw new \Exception('System notification types cannot be deleted.');
            }
        });
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): NotificationTypeFactory
    {
        return NotificationTypeFactory::new();
    }
}
