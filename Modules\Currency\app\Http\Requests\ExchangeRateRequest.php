<?php

namespace Modules\Currency\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class ExchangeRateRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'base_currency' => [
                'required',
                'string',
                'size:3',
                'exists:currencies,code',
            ],
            'target_currency' => [
                'required',
                'string',
                'size:3',
                'exists:currencies,code',
                'different:base_currency',
            ],
            'rate' => [
                'required',
                'numeric',
                'min:' . setting('currency.currency_rate_min', '0.00000001'),
                'max:' . setting('currency.currency_rate_max', '999999999.99999999'),
            ],
            'create_reverse' => 'sometimes|boolean',
            'update_reverse' => 'sometimes|boolean',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'base_currency' => __('Base Currency'),
            'target_currency' => __('Target Currency'),
            'rate' => __('Exchange Rate'),
            'create_reverse' => __('Create Reverse Rate'),
            'update_reverse' => __('Update Reverse Rate'),
        ];
    }
}
