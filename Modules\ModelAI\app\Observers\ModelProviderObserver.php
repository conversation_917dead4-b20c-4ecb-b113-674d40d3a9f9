<?php

namespace Modules\ModelAI\Observers;

use Illuminate\Support\Facades\Cache;
use Mo<PERSON>les\ModelAI\Models\ModelProvider;

class ModelProviderObserver
{
    /**
     * Handle the ModelProvider "created" event.
     */
    public function created(ModelProvider $modelProvider): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelProvider "updated" event.
     */
    public function updated(ModelProvider $modelProvider): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelProvider "deleted" event.
     */
    public function deleted(ModelProvider $modelProvider): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelProvider "restored" event.
     */
    public function restored(ModelProvider $modelProvider): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelProvider "force deleted" event.
     */
    public function forceDeleted(ModelProvider $modelProvider): void
    {
        $this->clearCache();
    }

    /**
     * Clear all related cache.
     */
    private function clearCache(): void
    {
        // Clear model provider related cache - using consistent naming
        Cache::tags(['model-providers'])->flush();
        
        // Clear specific cache keys - using consistent naming
        $cacheKeys = [
            'modelai.providers.dropdown',
            'modelai.providers.with_models_count',
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }

        // Clear model AI cache since providers affect models
        Cache::tags(['model-ai'])->flush();
    }
}
