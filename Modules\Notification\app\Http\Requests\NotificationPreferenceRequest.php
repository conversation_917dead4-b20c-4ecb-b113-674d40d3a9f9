<?php

namespace Modules\Notification\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Notification\Models\NotificationType;

/**
 * @property mixed $id
 * @property mixed $notification_preference_id
 */
class NotificationPreferenceRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'notifiable_type' => [
                'sometimes',
                'required',
                'string',
                'max:255',
            ],
            'notifiable_id' => [
                'sometimes',
                'required',
                'integer',
                'min:1',
            ],
            'notification_type_id' => [
                'required',
                'integer',
                'exists:notification_types,id',
            ],
            'channel' => [
                'required',
                'string',
                Rule::in(['database', 'email', 'sms', 'push', 'telegram']),
            ],
            'enabled' => [
                'required',
                'boolean',
            ],
            'settings' => [
                'nullable',
                'array',
            ],
            'settings.frequency' => [
                'nullable',
                'string',
                Rule::in(['immediate', 'daily', 'weekly']),
            ],
            'settings.digest' => [
                'nullable',
                'boolean',
            ],
            'settings.sound_enabled' => [
                'nullable',
                'boolean',
            ],
            'settings.vibration_enabled' => [
                'nullable',
                'boolean',
            ],
            'settings.badge_enabled' => [
                'nullable',
                'boolean',
            ],
            'settings.show_preview' => [
                'nullable',
                'boolean',
            ],
            'settings.html_format' => [
                'nullable',
                'boolean',
            ],
            'settings.mark_as_read_after' => [
                'nullable',
                'integer',
                'min:1',
                'max:365',
            ],
            'settings.auto_delete_after' => [
                'nullable',
                'integer',
                'min:1',
                'max:3650', // 10 years
            ],
            'settings.short_format' => [
                'nullable',
                'boolean',
            ],
            'settings.disable_notification' => [
                'nullable',
                'boolean',
            ],
            'settings.parse_mode' => [
                'nullable',
                'string',
                Rule::in(['HTML', 'Markdown']),
            ],
            'quiet_hours_start' => [
                'nullable',
                'date_format:H:i',
            ],
            'quiet_hours_end' => [
                'nullable',
                'date_format:H:i',
            ],
            'timezone' => [
                'nullable',
                'string',
                'max:50',
                'timezone',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'notifiable_type' => __('Notifiable Type'),
            'notifiable_id' => __('Notifiable ID'),
            'notification_type_id' => __('Notification Type'),
            'channel' => __('Channel'),
            'enabled' => __('Enabled'),
            'settings' => __('Settings'),
            'settings.frequency' => __('Frequency'),
            'settings.digest' => __('Digest Mode'),
            'settings.sound_enabled' => __('Sound Enabled'),
            'settings.vibration_enabled' => __('Vibration Enabled'),
            'settings.badge_enabled' => __('Badge Enabled'),
            'settings.show_preview' => __('Show Preview'),
            'settings.html_format' => __('HTML Format'),
            'settings.mark_as_read_after' => __('Mark as Read After (days)'),
            'settings.auto_delete_after' => __('Auto Delete After (days)'),
            'settings.short_format' => __('Short Format'),
            'settings.disable_notification' => __('Disable Notification'),
            'settings.parse_mode' => __('Parse Mode'),
            'quiet_hours_start' => __('Quiet Hours Start'),
            'quiet_hours_end' => __('Quiet Hours End'),
            'timezone' => __('Timezone'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'notification_type_id.exists' => 'The selected notification type does not exist.',
            'channel.in' => 'The selected channel is invalid.',
            'settings.frequency.in' => 'Frequency must be one of: immediate, daily, weekly.',
            'settings.parse_mode.in' => 'Parse mode must be either HTML or Markdown.',
            'quiet_hours_start.date_format' => 'Quiet hours start must be in HH:MM format.',
            'quiet_hours_end.date_format' => 'Quiet hours end must be in HH:MM format.',
            'timezone.timezone' => 'The timezone must be a valid timezone identifier.',
            'settings.mark_as_read_after.max' => 'Mark as read after cannot exceed 365 days.',
            'settings.auto_delete_after.max' => 'Auto delete after cannot exceed 3650 days (10 years).',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if preference already exists for this notifiable/type/channel combination
            if ($this->isMethod('POST') || ($this->isMethod('PUT') && $this->hasChangedUniqueFields())) {
                $exists = \Modules\Notification\Models\NotificationPreference::where([
                    'notifiable_type' => $this->notifiable_type,
                    'notifiable_id' => $this->notifiable_id,
                    'notification_type_id' => $this->notification_type_id,
                    'channel' => $this->channel,
                ])
                ->when($this->id, function ($query) {
                    return $query->where('id', '!=', $this->id);
                })
                ->exists();

                if ($exists) {
                    $validator->errors()->add(
                        'channel',
                        'A preference for this notifiable, notification type, and channel already exists.'
                    );
                }
            }

            // Validate that channel is supported by notification type
            if ($this->notification_type_id && $this->channel) {
                $notificationType = NotificationType::find($this->notification_type_id);
                if ($notificationType && !$notificationType->supportsChannel($this->channel)) {
                    $validator->errors()->add(
                        'channel',
                        'The selected channel is not supported by this notification type.'
                    );
                }
            }

            // Validate quiet hours logic
            if ($this->quiet_hours_start && $this->quiet_hours_end) {
                if ($this->quiet_hours_start === $this->quiet_hours_end) {
                    $validator->errors()->add(
                        'quiet_hours_end',
                        'Quiet hours start and end times cannot be the same.'
                    );
                }
            }

            // Validate that if quiet hours are set, timezone should be provided
            if (($this->quiet_hours_start || $this->quiet_hours_end) && !$this->timezone) {
                $validator->errors()->add(
                    'timezone',
                    'Timezone is required when quiet hours are specified.'
                );
            }

            // Validate channel-specific settings
            $this->validateChannelSpecificSettings($validator);

            // Validate notifiable exists if provided
            if ($this->notifiable_type && $this->notifiable_id) {
                if (!class_exists($this->notifiable_type)) {
                    $validator->errors()->add(
                        'notifiable_type',
                        'The notifiable type class does not exist.'
                    );
                } else {
                    $model = $this->notifiable_type::find($this->notifiable_id);
                    if (!$model) {
                        $validator->errors()->add(
                            'notifiable_id',
                            'The notifiable record does not exist.'
                        );
                    }
                }
            }
        });
    }

    /**
     * Validate channel-specific settings.
     */
    protected function validateChannelSpecificSettings($validator): void
    {
        if (!$this->channel || !$this->settings) {
            return;
        }

        $settings = $this->settings;
        $channel = $this->channel;

        switch ($channel) {
            case 'email':
                $allowedKeys = ['frequency', 'digest', 'html_format'];
                break;
            case 'database':
                $allowedKeys = ['mark_as_read_after', 'auto_delete_after'];
                break;
            case 'sms':
                $allowedKeys = ['frequency', 'short_format'];
                break;
            case 'push':
                $allowedKeys = ['sound_enabled', 'vibration_enabled', 'badge_enabled', 'show_preview'];
                break;
            case 'telegram':
                $allowedKeys = ['disable_notification', 'parse_mode'];
                break;
            default:
                $allowedKeys = [];
        }

        $unknownKeys = array_diff(array_keys($settings), $allowedKeys);
        if (!empty($unknownKeys)) {
            $validator->errors()->add(
                'settings',
                "Invalid settings for {$channel} channel: " . implode(', ', $unknownKeys)
            );
        }
    }

    /**
     * Check if unique fields have changed.
     */
    protected function hasChangedUniqueFields(): bool
    {
        if (!$this->route('notification_preference')) {
            return true;
        }

        $preference = $this->route('notification_preference');
        return $preference->notifiable_type != $this->notifiable_type ||
               $preference->notifiable_id != $this->notifiable_id ||
               $preference->notification_type_id != $this->notification_type_id ||
               $preference->channel != $this->channel;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert string booleans to actual booleans
        $booleanFields = ['enabled'];
        foreach ($booleanFields as $field) {
            if ($this->has($field)) {
                $this->merge([
                    $field => filter_var($this->$field, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false
                ]);
            }
        }

        // Convert string booleans in settings
        if ($this->has('settings') && is_array($this->settings)) {
            $settings = $this->settings;
            $settingsBooleanFields = [
                'digest', 'sound_enabled', 'vibration_enabled', 'badge_enabled',
                'show_preview', 'html_format', 'short_format', 'disable_notification'
            ];
            
            foreach ($settingsBooleanFields as $field) {
                if (isset($settings[$field])) {
                    $settings[$field] = filter_var(
                        $settings[$field], 
                        FILTER_VALIDATE_BOOLEAN, 
                        FILTER_NULL_ON_FAILURE
                    ) ?? false;
                }
            }
            
            $this->merge(['settings' => $settings]);
        }

        // Set notifiable from authenticated user if not provided
        if (!$this->has('notifiable_type') && !$this->has('notifiable_id') && auth()->check()) {
            $user = auth()->user();
            $this->merge([
                'notifiable_type' => get_class($user),
                'notifiable_id' => $user->id,
            ]);
        }
    }
}
