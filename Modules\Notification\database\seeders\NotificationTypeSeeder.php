<?php

namespace Modules\Notification\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class NotificationTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing non-auth notification data to avoid duplicates
        // Note: Auth-related notifications are now managed by Auth module
        DB::table('notification_templates')->whereNotIn('notification_type_id', function($query) {
            $query->select('id')->from('notification_types')
                  ->whereIn('key', ['welcome', 'password_reset', 'email_verification', 'security_alert', 'account_activity']);
        })->delete();

        DB::table('notification_types')->whereNotIn('key', [
            'welcome', 'password_reset', 'email_verification', 'security_alert', 'account_activity'
        ])->delete();

        $notificationTypes = [
            [
                'key' => 'system_maintenance',
                'name' => 'System Maintenance',
                'description' => 'System maintenance notifications',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'high',
                    'auto_send' => false,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'general_announcement',
                'name' => 'General Announcement',
                'description' => 'General announcements and updates',
                'channels' => json_encode(['database', 'email', 'push']),
                'default_settings' => json_encode([
                    'priority' => 'normal',
                    'auto_send' => false,
                ]),
                'is_system' => false,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'promotional',
                'name' => 'Promotional',
                'description' => 'Promotional and marketing notifications',
                'channels' => json_encode(['database', 'email', 'push']),
                'default_settings' => json_encode([
                    'priority' => 'low',
                    'auto_send' => false,
                ]),
                'is_system' => false,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'reminder',
                'name' => 'Reminder',
                'description' => 'General reminder notifications for users',
                'channels' => json_encode(['database', 'email', 'sms', 'push']),
                'default_settings' => json_encode([
                    'priority' => 'normal',
                    'auto_send' => false,
                    'retry_attempts' => 1,
                    'delay_seconds' => 300, // 5 minutes
                ]),
                'is_system' => false,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'order_confirmation',
                'name' => 'Order Confirmation',
                'description' => 'Order confirmation and receipt notifications',
                'channels' => json_encode(['database', 'email', 'sms']),
                'default_settings' => json_encode([
                    'priority' => 'high',
                    'auto_send' => true,
                    'retry_attempts' => 3,
                    'delay_seconds' => 0,
                ]),
                'is_system' => false,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'payment_notification',
                'name' => 'Payment Notification',
                'description' => 'Payment success, failure, and refund notifications',
                'channels' => json_encode(['database', 'email', 'sms']),
                'default_settings' => json_encode([
                    'priority' => 'high',
                    'auto_send' => true,
                    'retry_attempts' => 2,
                    'delay_seconds' => 0,
                ]),
                'is_system' => false,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],

        ];

        DB::table('notification_types')->insert($notificationTypes);

        $this->command->info('Notification types seeded successfully.');
    }
}
