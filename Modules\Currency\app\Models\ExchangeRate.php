<?php

namespace Modules\Currency\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Currency\Database\Factories\ExchangeRateFactory;

class ExchangeRate extends Model
{
    use HasFactory;
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'base_currency',
        'target_currency',
        'rate',
        'fetched_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'rate' => 'decimal:8',
        'fetched_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the base currency.
     */
    public function baseCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'base_currency', 'code');
    }

    /**
     * Get the target currency.
     */
    public function targetCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'target_currency', 'code');
    }

    /**
     * Scope to get latest rates.
     */
    public function scopeLatest(Builder $query): Builder
    {
        return $query->orderBy('fetched_at', 'desc');
    }

    /**
     * Scope to filter by base currency.
     */
    public function scopeBaseCurrency(Builder $query, string $currency): Builder
    {
        return $query->where('base_currency', $currency);
    }

    /**
     * Scope to filter by target currency.
     */
    public function scopeTargetCurrency(Builder $query, string $currency): Builder
    {
        return $query->where('target_currency', $currency);
    }

    /**
     * Scope to get fresh rates (updated within threshold).
     */
    public function scopeFresh(Builder $query): Builder
    {
        $freshThreshold = (int) setting('currency.currency_fresh_threshold', 3600); // seconds
        return $query->where('fetched_at', '>', now()->subSeconds($freshThreshold));
    }

    /**
     * Scope to get stale rates (older than threshold).
     */
    public function scopeStale(Builder $query): Builder
    {
        $staleThreshold = (int) setting('currency.currency_stale_threshold', 86400); // seconds
        return $query->where('fetched_at', '<', now()->subSeconds($staleThreshold));
    }

    /**
     * Get exchange rate for currency pair.
     */
    public static function getRate(string $baseCurrency, string $targetCurrency): ?float
    {
        if ($baseCurrency === $targetCurrency) {
            return 1.0;
        }

        $rate = static::where('base_currency', $baseCurrency)
            ->where('target_currency', $targetCurrency)
            ->latest('fetched_at')
            ->first();

        return $rate ? (float) $rate->rate : null;
    }

    /**
     * Convert amount from base to target currency.
     */
    public function convertAmount(float $amount): float
    {
        return $amount * $this->rate;
    }

    /**
     * Check if rate is fresh (updated within threshold from settings).
     */
    public function isFresh(): bool
    {
        if (!$this->fetched_at) {
            return false;
        }

        $freshThreshold = (int) setting('currency.currency_fresh_threshold', 3600); // seconds
        return $this->fetched_at->gt(now()->subSeconds($freshThreshold));
    }

    /**
     * Check if rate is stale (older than threshold from settings).
     */
    public function isStale(): bool
    {
        if (!$this->fetched_at) {
            return true;
        }

        $staleThreshold = (int) setting('currency.currency_stale_threshold', 86400); // seconds
        return $this->fetched_at->lt(now()->subSeconds($staleThreshold));
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): ExchangeRateFactory
    {
        return ExchangeRateFactory::new();
    }
}
