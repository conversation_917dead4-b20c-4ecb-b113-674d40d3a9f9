# Language Module

The Language Module provides comprehensive multi-language management functionality for Laravel ProCMS, enabling creation, management, and display of languages with support for locale switching, direction handling, and default language configuration.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [Database Schema](#database-schema)
- [Installation](#installation)
- [API Documentation](#api-documentation)
- [Usage Examples](#usage-examples)
- [Filtering](#filtering)
- [Multi-Language Support](#multi-language-support)
- [Caching](#caching)
- [Testing](#testing)
- [Dependencies](#dependencies)
- [Contributing](#contributing)

## Overview

The Language Module is a foundational component of Laravel ProCMS that handles language management and multi-language support. It provides the infrastructure for locale switching, language configuration, and serves as the backbone for internationalization across the entire CMS.

### Key Components

- **Language Model**: Main language entity with status management and default language controls
- **LanguageController**: Public language display and retrieval functionality
- **AuthLanguageController**: Administrative CRUD operations and bulk management
- **LanguageService**: Business logic and cached data retrieval
- **LanguageFilter**: Advanced filtering and search capabilities
- **LanguageFacade**: Convenient access to language functionality

## Features

### Core Features
- ✅ **Language Management**: Complete CRUD operations for language entities
- ✅ **Status Management**: Active and inactive language states
- ✅ **Default Language**: Single default language configuration with automatic enforcement
- ✅ **Soft Delete**: Safe deletion with restore capabilities
- ✅ **Direction Support**: Left-to-right (LTR) and right-to-left (RTL) text direction
- ✅ **Flag Support**: Country flag association for visual identification
- ✅ **Native Names**: Support for language names in their native script

### Advanced Features
- ✅ **Caching System**: Intelligent caching with 5-minute TTL for performance
- ✅ **Advanced Filtering**: Search across language properties and metadata
- ✅ **Bulk Operations**: Mass delete, restore, and force delete operations
- ✅ **API-First Design**: Complete REST API with comprehensive endpoints
- ✅ **Comprehensive Testing**: 90%+ test coverage with unit and feature tests
- ✅ **Validation System**: Robust validation with custom rules and error handling

### Integration Features
- ✅ **Facade Support**: Easy access through LanguageFacade
- ✅ **Service Container**: Singleton service registration for performance
- ✅ **Module Integration**: Seamless integration with other ProCMS modules
- ✅ **Translation Support**: Foundation for content translation across modules

## Architecture

The Language Module follows Laravel ProCMS architectural patterns:

```
Modules/Language/
├── app/
│   ├── Facades/
│   │   └── LanguageFacade.php           # Service facade
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── LanguageController.php   # Public language API
│   │   │   └── Auth/
│   │   │       └── LanguageController.php # Admin CRUD operations
│   │   ├── Filters/
│   │   │   └── LanguageFilter.php       # Advanced filtering
│   │   └── Requests/
│   │       ├── LanguageRequest.php      # Language validation
│   │       └── BulkLanguageRequest.php  # Bulk operation validation
│   ├── Models/
│   │   └── Language.php                 # Main language model
│   ├── Providers/
│   │   ├── LanguageServiceProvider.php  # Service registration
│   │   ├── EventServiceProvider.php     # Event registration
│   │   └── RouteServiceProvider.php     # Route registration
│   └── Services/
│       └── LanguageService.php          # Business logic
├── database/
│   ├── factories/
│   │   └── LanguageFactory.php          # Language factory
│   ├── migrations/
│   │   └── 2025_06_03_131924_create_languages_table.php
│   └── seeders/
│       └── LanguageDatabaseSeeder.php   # Default language data
├── routes/
│   ├── api.php                          # API routes
│   └── web.php                          # Web routes
└── tests/
    ├── Feature/                         # Integration tests
    └── Unit/                            # Unit tests
```

## Database Schema

### Languages Table
```sql
CREATE TABLE languages (
    id BIGINT UNSIGNED PRIMARY KEY,
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(255) NULL,
    native_name VARCHAR(255) NULL,
    flag VARCHAR(255) NULL,
    direction VARCHAR(255) DEFAULT 'ltr',
    is_default TINYINT DEFAULT 0,
    status VARCHAR(255) DEFAULT 'active',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_status (status)
);
```

### Field Descriptions
- **code**: Unique language identifier (ISO 639-1 format recommended)
- **name**: Language name in English
- **native_name**: Language name in its native script
- **flag**: Country flag identifier for visual representation
- **direction**: Text direction ('ltr' or 'rtl')
- **is_default**: Boolean flag for default language (only one allowed)
- **status**: Language status ('active' or 'inactive')

### Constraints and Indexes
- **Unique constraint**: `code` field must be unique across non-deleted records
- **Status index**: Optimized queries for active/inactive languages
- **Soft delete**: Uses `deleted_at` timestamp for safe deletion

## Installation

The Language Module is included in Laravel ProCMS by default. To manually install or reinstall:

### 1. Run Migrations
```bash
php artisan migrate --path=Modules/Language/database/migrations
```

### 2. Seed Default Data
```bash
php artisan db:seed --class=Modules\\Language\\Database\\Seeders\\LanguageDatabaseSeeder
```

### 3. Register Service Provider
The module is auto-registered via Laravel's package discovery. Manual registration in `config/app.php`:

```php
'providers' => [
    // Other providers...
    Modules\Language\Providers\LanguageServiceProvider::class,
],
```

### 4. Register Facade (Optional)
```php
'aliases' => [
    // Other aliases...
    'LanguageFacade' => Modules\Language\Facades\LanguageFacade::class,
],
```

## API Documentation

### Public API Endpoints

#### Get Active Languages
```http
GET /api/v1/languages
```

**Description**: Retrieve a list of all active languages for public use.

**Response Structure**:
```json
{
    "success": true,
    "message": "Languages retrieved successfully.",
    "data": [
        {
            "code": "en",
            "name": "English",
            "native_name": "English",
            "direction": "ltr",
            "is_default": 1
        },
        {
            "code": "vi",
            "name": "Vietnamese",
            "native_name": "Tiếng Việt",
            "direction": "ltr",
            "is_default": 0
        }
    ]
}
```

**HTTP Status Codes**:
- `200 OK`: Languages retrieved successfully

**Example Request**:
```bash
curl -X GET "https://api.example.com/api/v1/languages" \
     -H "Accept: application/json" \
     -H "Content-Type: application/json"
```

#### Get Default Language
```http
GET /api/v1/languages/default
```

**Description**: Retrieve the default language configuration.

**Response Structure**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "code": "en",
        "name": "English",
        "native_name": "English",
        "flag": "us",
        "direction": "ltr",
        "is_default": 1,
        "status": "active",
        "created_at": "2024-01-01T10:00:00.000000Z",
        "updated_at": "2024-01-01T11:00:00.000000Z"
    }
}
```

**HTTP Status Codes**:
- `200 OK`: Default language found
- `404 Not Found`: No default language configured

#### Get Language by Code
```http
GET /api/v1/languages/{code}
```

**Description**: Retrieve a specific language by its code.

**Parameters**:
- `code` (string, required): The language code (e.g., 'en', 'vi')

**Response Structure**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "code": "en",
        "name": "English",
        "native_name": "English",
        "flag": "us",
        "direction": "ltr",
        "is_default": 1,
        "status": "active",
        "created_at": "2024-01-01T10:00:00.000000Z",
        "updated_at": "2024-01-01T11:00:00.000000Z"
    }
}
```

**HTTP Status Codes**:
- `200 OK`: Language found
- `404 Not Found`: Language not found

**Example Request**:
```bash
curl -X GET "https://api.example.com/api/v1/languages/en" \
     -H "Accept: application/json" \
     -H "Content-Type: application/json"
```

### Administrative API Endpoints

#### List Languages
```http
GET /api/v1/auth/languages
```

**Description**: Retrieve a paginated list of languages with filtering options.

**Query Parameters**:
- `limit` (integer, optional): Number of items per page (default: 10)
- `page` (integer, optional): Page number (default: 1)
- `code` (string, optional): Filter by language code (partial match)
- `name` (string, optional): Filter by language name (partial match)
- `native_name` (string, optional): Filter by native name (partial match)
- `status` (string, optional): Filter by status ('active' or 'inactive')
- `is_default` (boolean, optional): Filter by default language flag
- `is_trashed` (boolean, optional): Include only soft-deleted languages
- `created_from` (date, optional): Filter by creation date from
- `created_to` (date, optional): Filter by creation date to

**Response Structure**:
```json
{
    "current_page": 1,
    "data": [
        {
            "id": 1,
            "code": "en",
            "name": "English",
            "native_name": "English",
            "flag": "us",
            "direction": "ltr",
            "is_default": 1,
            "status": "active",
            "created_at": "2024-01-01T10:00:00.000000Z",
            "updated_at": "2024-01-01T11:00:00.000000Z"
        }
    ],
    "first_page_url": "http://api.example.com/api/v1/auth/languages?page=1",
    "from": 1,
    "last_page": 5,
    "last_page_url": "http://api.example.com/api/v1/auth/languages?page=5",
    "links": [...],
    "next_page_url": "http://api.example.com/api/v1/auth/languages?page=2",
    "path": "http://api.example.com/api/v1/auth/languages",
    "per_page": 10,
    "prev_page_url": null,
    "to": 10,
    "total": 50
}
```

**Example Request**:
```bash
curl -X GET "https://api.example.com/api/v1/auth/languages?status=active&limit=5" \
     -H "Accept: application/json" \
     -H "Content-Type: application/json"
```

#### Show Language
```http
GET /api/v1/auth/languages/{id}
```

**Description**: Retrieve a specific language by ID with complete details.

**Parameters**:
- `id` (integer, required): The language ID

**Response Structure**:
```json
{
    "success": true,
    "message": "Language retrieved successfully.",
    "data": {
        "id": 1,
        "code": "en",
        "name": "English",
        "native_name": "English",
        "flag": "us",
        "direction": "ltr",
        "is_default": 1,
        "status": "active",
        "created_at": "2024-01-01T10:00:00.000000Z",
        "updated_at": "2024-01-01T11:00:00.000000Z"
    }
}
```

#### Create Language
```http
POST /api/v1/auth/languages
```

**Description**: Create a new language.

**Request Body**:
```json
{
    "code": "es",
    "name": "Spanish",
    "native_name": "Español",
    "flag": "es",
    "direction": "ltr",
    "is_default": 0,
    "status": "active"
}
```

**Validation Rules**:
- `code`: required, string, max 20 characters, alpha_dash, unique
- `name`: required, string, max 255 characters
- `native_name`: nullable, string, max 255 characters
- `flag`: nullable, string, max 255 characters
- `direction`: required, string, must be 'ltr' or 'rtl'
- `is_default`: required, boolean or integer (0/1)
- `status`: required, string, must be 'active' or 'inactive'

**HTTP Status Codes**:
- `201 Created`: Language created successfully
- `422 Unprocessable Entity`: Validation errors

**Example Request**:
```bash
curl -X POST "https://api.example.com/api/v1/auth/languages" \
     -H "Accept: application/json" \
     -H "Content-Type: application/json" \
     -d '{
         "code": "es",
         "name": "Spanish",
         "native_name": "Español",
         "flag": "es",
         "direction": "ltr",
         "is_default": 0,
         "status": "active"
     }'
```

**Example Validation Error Response**:
```json
{
    "message": "The given data was invalid.",
    "errors": {
        "code": ["The code field is required."],
        "name": ["The name field is required."],
        "direction": ["The direction field is required."],
        "status": ["The status field is required."]
    }
}
```

#### Update Language
```http
PUT /api/v1/auth/languages/{id}
```

**Description**: Update an existing language.

**Parameters**:
- `id` (integer, required): The language ID

**Request Body**: Same structure as Create Language

**HTTP Status Codes**:
- `200 OK`: Language updated successfully
- `404 Not Found`: Language not found
- `422 Unprocessable Entity`: Validation errors

#### Delete Language (Soft Delete)
```http
DELETE /api/v1/auth/languages/{id}
```

**Description**: Soft delete a language (can be restored). Note: Cannot delete the default language.

**HTTP Status Codes**:
- `200 OK`: Language deleted successfully
- `404 Not Found`: Language not found
- `422 Unprocessable Entity`: Cannot delete default language

**Example Response**:
```json
{
    "success": true,
    "message": "Language deleted successfully."
}
```

#### Restore Language
```http
PUT /api/v1/auth/languages/{id}/restore
```

**Description**: Restore a soft-deleted language.

**HTTP Status Codes**:
- `200 OK`: Language restored successfully
- `404 Not Found`: Language not found

#### Force Delete Language
```http
DELETE /api/v1/auth/languages/{id}/force-delete
```

**Description**: Permanently delete a language (cannot be restored).

**HTTP Status Codes**:
- `200 OK`: Language permanently deleted
- `404 Not Found`: Language not found

#### Get Dropdown Data
```http
GET /api/v1/auth/languages/dropdown
```

**Description**: Get simplified language data for dropdown/select components.

**Response Structure**:
```json
{
    "success": true,
    "message": "Languages retrieved successfully.",
    "data": [
        {
            "code": "en",
            "name": "English",
            "native_name": "English",
            "direction": "ltr",
            "is_default": 1,
            "flag": "us"
        }
    ]
}
```

#### Bulk Operations

##### Bulk Destroy
```http
DELETE /api/v1/auth/languages/bulk-destroy
```

**Request Body**:
```json
{
    "ids": [1, 2, 3, 4, 5]
}
```

**Description**: Soft delete multiple languages. Cannot delete the default language.

##### Bulk Restore
```http
PUT /api/v1/auth/languages/bulk-restore
```

**Request Body**:
```json
{
    "ids": [1, 2, 3, 4, 5]
}
```

**Description**: Restore multiple soft-deleted languages.

##### Bulk Force Delete
```http
DELETE /api/v1/auth/languages/bulk-delete
```

**Request Body**:
```json
{
    "ids": [1, 2, 3, 4, 5]
}
```

**Description**: Permanently delete multiple languages.

**Bulk Operation Validation**:
- `ids`: required, array, minimum 1 item
- `ids.*`: required, integer, exists in languages table

## Usage Examples

### Using the Language Service

```php
use Modules\Language\Services\LanguageService;

// Resolve service from container
$languageService = app(LanguageService::class);

// Get active languages (cached for 5 minutes)
$activeLanguages = $languageService->getActiveLanguages();

foreach ($activeLanguages as $language) {
    echo $language->name . ' (' . $language->code . ')' . PHP_EOL;
    echo 'Direction: ' . $language->direction . PHP_EOL;
    echo 'Default: ' . ($language->is_default ? 'Yes' : 'No') . PHP_EOL;
}

// Get default language
$defaultLanguage = $languageService->getDefaultLanguage();
if ($defaultLanguage) {
    echo 'Default language: ' . $defaultLanguage->name;
}

// Find language by code
$language = $languageService->findByCode('en');
if ($language) {
    echo 'Found: ' . $language->name;
}
```

### Using the Language Facade

```php
use Modules\Language\Facades\LanguageFacade;

// Get active languages
$languages = LanguageFacade::getActiveLanguages();

// Get default language
$defaultLanguage = LanguageFacade::getDefaultLanguage();

// Find by code
$english = LanguageFacade::findByCode('en');

if ($english) {
    echo 'Language: ' . $english->name;
    echo 'Native: ' . $english->native_name;
    echo 'Direction: ' . $english->direction;
}
```

### Working with Language Models

```php
use Modules\Language\Models\Language;

// Create a new language
$language = Language::create([
    'code' => 'fr',
    'name' => 'French',
    'native_name' => 'Français',
    'flag' => 'fr',
    'direction' => 'ltr',
    'is_default' => 0,
    'status' => 'active',
]);

// Query languages
$activeLanguages = Language::active()->get();
$inactiveLanguages = Language::status('inactive')->get();

// Find by code
$spanish = Language::where('code', 'es')->first();

// Get default language
$defaultLanguage = Language::where('is_default', 1)->active()->first();

// Update language status
$language->update(['status' => 'inactive']);

// Soft delete
$language->delete();

// Restore
$language->restore();

// Force delete
$language->forceDelete();
```

### Advanced Filtering

```php
use Modules\Language\Http\Filters\LanguageFilter;
use Illuminate\Http\Request;

// Create filter from request
$request = new Request([
    'status' => 'active',
    'code' => 'en',
    'name' => 'English',
    'is_default' => 1,
    'created_from' => '2024-01-01',
    'created_to' => '2024-12-31',
]);

$filter = new LanguageFilter($request);

// Apply filter to query
$languages = Language::query()
    ->filter($filter)
    ->paginate(10);

// Filter examples
$activeLanguages = Language::query()
    ->filter(new LanguageFilter(new Request(['status' => 'active'])))
    ->get();

$defaultLanguage = Language::query()
    ->filter(new LanguageFilter(new Request(['is_default' => 1])))
    ->first();
```

## Filtering

The Language Module provides comprehensive filtering capabilities through the `LanguageFilter` class:

### Available Filters

#### Basic Filters
- `code`: Filter by language code (partial match)
- `name`: Filter by language name (partial match)
- `native_name`: Filter by native name (partial match)
- `status`: Filter by status ('active' or 'inactive')
- `is_default`: Filter by default language flag (0 or 1)
- `is_trashed`: Include only soft-deleted languages

#### Date Range Filters
- `created_from` / `created_to`: Filter by creation date range

### Filter Usage Examples

```php
// Filter by status
GET /api/v1/auth/languages?status=active

// Filter by code (partial match)
GET /api/v1/auth/languages?code=en

// Filter by name (partial match)
GET /api/v1/auth/languages?name=English

// Date range filtering
GET /api/v1/auth/languages?created_from=2024-01-01&created_to=2024-12-31

// Filter default language
GET /api/v1/auth/languages?is_default=1

// Combined filters
GET /api/v1/auth/languages?status=active&is_default=0

// Include trashed languages
GET /api/v1/auth/languages?is_trashed=true
```

## Multi-Language Support

The Language Module serves as the foundation for multi-language support across the entire ProCMS:

### Language Configuration

Each language supports the following configuration:

```php
// Language properties
$language = [
    'code' => 'ar',              // ISO 639-1 language code
    'name' => 'Arabic',          // English name
    'native_name' => 'العربية',   // Native script name
    'flag' => 'sa',              // Country flag identifier
    'direction' => 'rtl',        // Text direction (ltr/rtl)
    'is_default' => 0,           // Default language flag
    'status' => 'active',        // Language status
];
```

### Default Language Management

- Only one language can be set as default
- Setting a language as default automatically removes the flag from others
- Default language cannot be deleted
- Default language is used as fallback for missing translations

### Direction Support

The module supports both text directions:
- **LTR (Left-to-Right)**: English, French, Spanish, etc.
- **RTL (Right-to-Left)**: Arabic, Hebrew, Persian, etc.

### Integration with Other Modules

The Language Module integrates with:
- **Page Module**: Multi-language page content
- **Blog Module**: Multi-language blog posts and categories
- **Translation Module**: Dynamic translation management
- **Theme Module**: Language-specific theme configurations

## Caching

The Language Module implements intelligent caching strategies for optimal performance:

### Service-Level Caching

The `LanguageService` implements caching for frequently accessed data:

```php
// Active languages cached for 5 minutes
$languages = Cache::remember(
    'proCMS.languages.active.list',
    now()->addMinutes(5),
    function () {
        return Language::active()->orderBy('name')->get();
    }
);
```

### Cache Keys

- `proCMS.languages.active.list`: Active languages list

### Cache Management

```php
// Clear language cache
Cache::forget('proCMS.languages.active.list');

// Cache invalidation on language changes
// Automatically handled by the service layer
```

### Performance Optimizations

- Singleton service registration for container efficiency
- Selective field loading for API responses
- Database indexes on frequently queried fields
- Eager loading prevention of N+1 queries

## Testing

The Language Module includes comprehensive test coverage with 90%+ coverage:

### Test Structure

```
tests/
├── Feature/
│   └── AuthLanguageControllerTest.php    # Admin API endpoints
└── Unit/
    ├── LanguageModelTest.php             # Language model functionality
    ├── LanguageServiceTest.php           # Service layer
    ├── LanguageRequestTest.php           # Form validation
    ├── BulkLanguageRequestTest.php       # Bulk validation
    ├── LanguageFilterTest.php            # Filtering functionality
    ├── LanguageFactoryTest.php           # Factory testing
    └── LanguageFacadeTest.php            # Facade functionality
```

### Running Tests

```bash
# Run all Language module tests
php artisan test Modules/Language/tests/

# Run specific test types
php artisan test Modules/Language/tests/Unit/
php artisan test Modules/Language/tests/Feature/

# Run specific test file
php artisan test Modules/Language/tests/Unit/LanguageModelTest.php

# Run with coverage
php artisan test Modules/Language/tests/ --coverage
```

### Test Categories

#### Unit Tests (7 test files, 150+ test methods)

**LanguageModelTest** (20 test methods)
- Model relationships and scopes
- Fillable attributes validation
- Cast functionality
- Soft delete behavior
- Factory integration
- Boot method functionality

**LanguageServiceTest** (25 test methods)
- Service method functionality
- Caching behavior and TTL
- Active language retrieval
- Default language management
- Code-based language lookup
- Error handling and edge cases

**LanguageRequestTest** (30 test methods)
- Validation rule testing
- Required field validation
- Field-specific validation (code, name, direction, status)
- Unique constraint validation
- Custom validation rules
- Error message testing

**BulkLanguageRequestTest** (15 test methods)
- Bulk operation validation
- ID existence checking
- Array validation
- Error handling

**LanguageFilterTest** (25 test methods)
- Filter functionality
- Date range filtering
- Status filtering
- Default language filtering
- Combined filter testing

**LanguageFactoryTest** (15 test methods)
- Factory functionality
- State generation
- Attribute overriding
- Relationship creation

**LanguageFacadeTest** (20 test methods)
- Facade resolution
- Service method delegation
- Container integration
- Method availability

#### Feature Tests (1 test file, 25+ test methods)

**AuthLanguageControllerTest** (25 test methods)
- CRUD operations
- Pagination testing
- Filtering integration
- Bulk operations
- Validation testing
- Response structure validation
- Default language protection

### Test Data Management

```php
// Use factories for consistent test data
$language = Language::factory()->create();
$activeLanguage = Language::factory()->create(['status' => 'active']);
$defaultLanguage = Language::factory()->create(['is_default' => 1]);

// Create test scenarios
$multipleLanguages = Language::factory()->count(5)->create();
```

### Test Traits and Utilities

```php
use RefreshDatabase;           // Database transactions
use WithFaker;                // Fake data generation

// Custom assertion helpers
$this->assertPaginatedResponseStructure($response);
$this->assertJsonResponseStructure($response);
```

## Dependencies

### Core Dependencies

- **Laravel Framework**: ^10.0 || ^11.0
- **PHP**: ^8.1
- **MySQL/PostgreSQL**: Database support

### Module Dependencies

- **Core Module**: For base request classes and traits
- **User Model**: For potential author relationships (future enhancement)

### Package Dependencies

- **nwidart/laravel-modules**: Module architecture
- **illuminate/support**: Laravel core functionality
- **illuminate/database**: Eloquent ORM and migrations

### Development Dependencies

- **PHPUnit**: Testing framework
- **Laravel Testing**: Feature and unit testing
- **Faker**: Test data generation

## Contributing

We welcome contributions to the Language Module! Please follow these guidelines:

### Development Setup

1. **Clone the repository**
2. **Install dependencies**: `composer install`
3. **Run migrations**: `php artisan migrate`
4. **Seed test data**: `php artisan db:seed --class=LanguageDatabaseSeeder`
5. **Run tests**: `php artisan test Modules/Language/tests/`

### Coding Standards

- Follow PSR-12 coding standards
- Use meaningful variable and method names
- Add comprehensive docblocks
- Maintain 90%+ test coverage

### Pull Request Process

1. **Create feature branch**: `git checkout -b feature/new-feature`
2. **Write tests**: Ensure new functionality is tested
3. **Run test suite**: All tests must pass
4. **Update documentation**: Update README if needed
5. **Submit pull request**: With clear description

### Testing Requirements

- All new features must include unit tests
- Feature tests for API endpoints
- Maintain existing test coverage
- Follow existing test patterns

### Code Review Checklist

- [ ] Code follows PSR-12 standards
- [ ] All tests pass
- [ ] Documentation updated
- [ ] No breaking changes (or properly documented)
- [ ] Performance considerations addressed
- [ ] Security implications reviewed
- [ ] Default language protection maintained

---

For more information about Laravel ProCMS architecture and patterns, see the main project documentation.
