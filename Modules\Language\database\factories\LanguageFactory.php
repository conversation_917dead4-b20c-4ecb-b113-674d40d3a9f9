<?php

namespace Modules\Language\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Language\Models\Language;

class LanguageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Language::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            // Đảm bảo 'code' là duy nhất
            'code' => $this->faker->unique()->languageCode, // ví dụ: 'en', 'fr', 'es'
            // Hoặc bạn có thể dùng một pattern khác nếu cần:
            // 'code' => $this->faker->unique()->bothify('??###'), // ví dụ: ab123

            'name' => $this->faker->country() . ' Language', // Tên ví dụ
            'native_name' => $this->faker->country(), // Tên gốc ví dụ
            'flag' => $this->faker->optional()->countryCode(), // ví dụ: 'US', 'GB'
            'direction' => $this->faker->randomElement(['ltr', 'rtl']),
            'is_default' => 0,
            'status' => 'active', // Mặc định là active hoặc theo logic của bạn
            // Thêm các trường cần thiết khác nếu có
        ];
    }

    /**
     * Indicate that the language is active.
     */
    public function active(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active',
            ];
        });
    }

    /**
     * Indicate that the language is the default language.
     */
    public function default(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'is_default' => 1,
                'status' => 'active', // Ngôn ngữ mặc định thường là active
            ];
        });
    }

    /**
     * Indicate that the language direction is RTL.
     */
    public function rtl(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'direction' => 'rtl',
            ];
        });
    }
}
