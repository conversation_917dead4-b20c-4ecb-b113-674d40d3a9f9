<?php

namespace Modules\ModelAI\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class ModelToolFilter extends AbstractFilter
{
    protected function filters(): array
    {
        return [
            'key' => 'like',
            'name' => 'like',
            'description' => 'like',
            'type' => 'exact',
            'provider' => 'exact',
            'version' => 'like',
            'is_enabled' => 'exact',
            'is_public' => 'exact',
            'status' => 'exact',
            'sort_order' => 'exact',
            'is_trashed' => 'exact',
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
            'updated_from' => ['type' => 'from', 'column' => 'updated_at'],
            'updated_to' => ['type' => 'to', 'column' => 'updated_at'],
        ];
    }
}
