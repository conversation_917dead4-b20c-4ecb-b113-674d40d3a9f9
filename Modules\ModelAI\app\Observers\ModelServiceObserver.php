<?php

namespace Modules\ModelAI\Observers;

use Illuminate\Support\Facades\Cache;
use Modules\ModelAI\Models\ModelService;

class ModelServiceObserver
{
    /**
     * Cache tags for model services
     */
    private const CACHE_TAGS = ['model-services', 'model-ai'];

    /**
     * Handle the ModelService "created" event.
     */
    public function created(ModelService $modelService): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelService "updated" event.
     */
    public function updated(ModelService $modelService): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelService "deleted" event.
     */
    public function deleted(ModelService $modelService): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelService "restored" event.
     */
    public function restored(ModelService $modelService): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelService "force deleted" event.
     */
    public function forceDeleted(ModelService $modelService): void
    {
        $this->clearCache();
    }

    /**
     * Clear model service related cache.
     */
    private function clearCache(): void
    {
        try {
            if (function_exists('enabledCache') && enabledCache()) {
                Cache::tags(self::CACHE_TAGS)->flush();
            }
        } catch (\Exception $e) {
            // Silently handle cache errors
        }
    }
}
