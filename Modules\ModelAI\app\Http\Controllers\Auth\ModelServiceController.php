<?php

namespace Modules\ModelAI\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Modules\Core\Traits\ResponseTrait;
use Modules\ModelAI\Http\Filters\ModelServiceFilter;
use Modules\ModelAI\Models\ModelService;
use Modules\ModelAI\Http\Requests\ModelServiceRequest;
use Modules\ModelAI\Http\Requests\BulkModelServiceRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ModelServiceController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|model-service.view')->only(['index', 'show', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|model-service.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|model-service.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|model-service.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|model-service.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $services = ModelService::query()
            ->with('modelAI')
            ->filter(new ModelServiceFilter($request))
            ->orderBy('priority', 'desc')
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($services, __('Model services retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ModelServiceRequest $request): JsonResponse
    {
        $service = ModelService::create($request->all());
        $service->load('modelAI');

        return $this->successResponse($service, __('Model service created successfully.'), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ModelService $modelService): JsonResponse
    {
        $modelService->load('modelAI');
        
        return $this->successResponse($modelService, __('Model service retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ModelServiceRequest $request, ModelService $modelService): JsonResponse
    {
        $modelService->update($request->all());
        $modelService->load('modelAI');

        return $this->successResponse($modelService->fresh('modelAI'), __('Model service updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ModelService $modelService): JsonResponse
    {
        $modelService->delete();

        return $this->successResponse(null, __('Model service deleted successfully.'));
    }



    /**
     * Restore the specified resource from trash.
     */
    public function restore(int $id): JsonResponse
    {
        $service = ModelService::onlyTrashed()->findOrFail($id);
        $service->restore();

        return $this->successResponse($service, __('Model service restored successfully.'));
    }

    /**
     * Permanently delete the specified resource.
     */
    public function forceDelete(int $id): JsonResponse
    {
        $service = ModelService::onlyTrashed()->findOrFail($id);
        $service->forceDelete();

        return $this->successResponse(null, __('Model service permanently deleted.'));
    }

    /**
     * Bulk destroy multiple resources.
     */
    public function bulkDestroy(BulkModelServiceRequest $request): JsonResponse
    {
        ModelService::whereIn('id', $request->ids)->delete();

        return $this->successResponse(null, __('Model services deleted successfully.'));
    }

    /**
     * Bulk restore multiple resources.
     */
    public function bulkRestore(BulkModelServiceRequest $request): JsonResponse
    {
        ModelService::onlyTrashed()->whereIn('id', $request->ids)->restore();

        return $this->successResponse(null, __('Model services restored successfully.'));
    }

    /**
     * Bulk force delete multiple resources.
     */
    public function bulkForceDelete(BulkModelServiceRequest $request): JsonResponse
    {
        ModelService::onlyTrashed()->whereIn('id', $request->ids)->forceDelete();

        return $this->successResponse(null, __('Model services permanently deleted.'));
    }

    /**
     * Get active services dropdown.
     */
    public function dropdown(): JsonResponse
    {
        $services = ModelService::query()
            ->active()
            ->with('modelAI:id,name,provider')
            ->orderBy('priority', 'desc')
            ->get(['id', 'model_ai_id', 'billing_type', 'priority']);

        return $this->successResponse($services, __('Model services retrieved successfully.'));
    }
}
