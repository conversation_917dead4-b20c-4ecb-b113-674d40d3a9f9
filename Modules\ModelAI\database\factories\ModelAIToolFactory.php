<?php

namespace Modules\ModelAI\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\ModelAI\Models\ModelAI;
use Modules\ModelAI\Models\ModelAITool;
use Modules\ModelAI\Models\ModelTool;

class ModelAIToolFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = ModelAITool::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'model_ai_id' => ModelAI::factory(),
            'model_tool_id' => ModelTool::factory(),
            'is_enabled' => $this->faker->boolean(80),
            'configuration' => $this->faker->optional()->randomElement([
                ['custom_param' => 'value', 'override_timeout' => 60],
                ['priority_boost' => true, 'custom_endpoint' => 'https://api.example.com'],
                null
            ]),
            'priority' => $this->faker->numberBetween(0, 100),
            'max_usage_per_request' => $this->faker->optional()->numberBetween(1, 10),
            'rate_limit_per_minute' => $this->faker->optional()->numberBetween(10, 1000),
        ];
    }

    /**
     * Indicate that the configuration is enabled.
     */
    public function enabled(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_enabled' => true,
        ]);
    }

    /**
     * Indicate that the configuration is disabled.
     */
    public function disabled(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_enabled' => false,
        ]);
    }

    /**
     * Set high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => $this->faker->numberBetween(80, 100),
        ]);
    }

    /**
     * Set low priority.
     */
    public function lowPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => $this->faker->numberBetween(0, 20),
        ]);
    }

    /**
     * Set usage limits.
     */
    public function withLimits(): static
    {
        return $this->state(fn (array $attributes) => [
            'max_usage_per_request' => $this->faker->numberBetween(1, 5),
            'rate_limit_per_minute' => $this->faker->numberBetween(10, 100),
        ]);
    }
}
