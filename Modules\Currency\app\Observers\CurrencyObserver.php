<?php

namespace Modules\Currency\Observers;

use Illuminate\Support\Facades\Cache;
use Modules\Currency\Models\Currency;

class CurrencyObserver
{
    /**
     * Cache tag for currencies
     */
    private const CACHE_TAG = 'currencies';

    /**
     * Handle the Currency "created" event.
     */
    public function created(Currency $currency): void
    {
        $this->clearCurrencyCache($currency);
    }

    /**
     * Handle the Currency "updated" event.
     */
    public function updated(Currency $currency): void
    {
        $this->clearCurrencyCache($currency);
    }

    /**
     * Handle the Currency "deleted" event.
     */
    public function deleted(Currency $currency): void
    {
        $this->clearCurrencyCache($currency);
    }

    /**
     * Clear cache for currencies.
     */
    private function clearCurrencyCache(Currency $currency): void
    {
        if (enabledCache()) {
            Cache::tags([self::CACHE_TAG])->flush();
        }
    }
}
