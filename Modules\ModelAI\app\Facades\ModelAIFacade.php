<?php

namespace Modules\ModelAI\Facades;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Facade;
use Modules\ModelAI\Services\ModelAIService;

/**
 * @method static \Illuminate\Support\Collection getActiveModels()
 * @method static \Modules\ModelAI\Models\ModelAI|null getDefaultModel()
 * @method static \Modules\ModelAI\Models\ModelAI|null findByKey(string $key)
 * @method static Collection getCategoriesForDropdown(string $locale)
 * @method static Collection getModelsForDropdown(string $locale)
 * @method static Collection getCategoriesWithModels(string $locale)
 * @method static Collection getModelsWithCategories(string $locale)
 * @method static Collection getToolsForDropdown(string $locale)
 * @method static Collection getCategoriesWithTools(string $locale)
 * @method static Collection getModelsWithTools(string $locale)
 * @method static Collection getAvailableToolsForModel(int $modelId)
 * @method static bool attachToolToModel(int $modelId, int $toolId, array $config = [])
 * @method static bool detachToolFromModel(int $modelId, int $toolId)
 *
 * @see ModelAIService
 */
class ModelAIFacade extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return 'modelai.service';
    }
}
