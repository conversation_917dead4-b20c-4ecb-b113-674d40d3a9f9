<?php

namespace Modules\Currency\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Modules\Currency\Models\Currency;
use Modules\Currency\Models\ExchangeRate;
use Modules\Currency\Services\ExchangeRateService;
use Modules\Setting\Models\Setting;
use Modules\Setting\Models\SettingGroup;
use PHPUnit\Framework\Attributes\Test;

class ExchangeRateServiceTest extends TestCase
{
    use RefreshDatabase;

    protected ExchangeRateService $exchangeRateService;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Currency module
        $this->artisan('migrate', ['--path' => 'Modules/Currency/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
        $this->artisan('migrate', ['--path' => 'Modules/Setting/database/migrations']);

        $this->exchangeRateService = app(ExchangeRateService::class);

        // Create setting group for tests
        $this->createSettingGroup();
    }

    private function createSettingGroup(): void
    {
        SettingGroup::create([
            'key' => 'currency',
            'label' => 'Currency Settings',
            'description' => 'Currency settings for testing',
            'icon' => 'fas fa-coins',
            'sort_order' => 1,
        ]);
    }

    private function createCurrencySettings(): void
    {
        $group = SettingGroup::firstOrCreate([
            'key' => 'currency'
        ], [
            'label' => 'Currency Settings',
            'description' => 'Currency settings for testing',
            'icon' => 'fas fa-coins',
            'sort_order' => 1,
        ]);

        // Settings from seeder
        $settings = [
            [
                'key' => 'currency_freecurrency_api_key',
                'value' => 'fca_live_0gSS8pGGYNXqGMnKQNOsMoMxZHCf4uEliILF7gI9',
                'type' => 'string',
                'input_type' => 'password',
                'label' => 'FreeCurrencyAPI Key',
                'description' => 'API key for FreeCurrencyAPI service (free tier: 5000 requests/month)',
                'validation_rules' => 'nullable|string|max:255',
                'is_public' => false,
                'sort_order' => 13,
            ],
            [
                'key' => 'currency_api_timeout',
                'value' => '30',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'API Request Timeout',
                'description' => 'Timeout in seconds for external API requests',
                'validation_rules' => 'required|integer|min:5|max:120',
                'is_public' => false,
                'sort_order' => 14,
            ],
            [
                'key' => 'currency_stale_threshold',
                'value' => '86400',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'Stale Rate Threshold',
                'description' => 'Time in seconds when exchange rates are considered stale (86400 = 24 hours)',
                'validation_rules' => 'required|integer|min:3600|max:604800',
                'is_public' => false,
                'sort_order' => 41,
            ],
            [
                'key' => 'currency_fixer_api_key',
                'value' => '',
                'type' => 'string',
                'input_type' => 'password',
                'label' => 'Fixer.io API Key',
                'description' => 'API key for Fixer.io service (required for Fixer API)',
                'validation_rules' => 'nullable|string|max:255',
                'is_public' => false,
                'sort_order' => 11,
            ],
        ];

        foreach ($settings as $settingData) {
            $settingData['group_id'] = $group->id;
            Setting::create($settingData);
        }

        // Clear settings cache
        Cache::forget('settings');
    }

    #[Test]
    public function it_can_fetch_exchange_rate_from_freecurrency_api()
    {
        $this->createCurrencySettings();

        // Mock HTTP response from FreeCurrencyAPI
        Http::fake([
            'api.freecurrencyapi.com/*' => Http::response([
                'data' => [
                    'EUR' => 0.85
                ]
            ], 200)
        ]);

        $rate = $this->exchangeRateService->fetchExchangeRate('USD', 'EUR', 'freecurrency');

        $this->assertEquals(0.85, $rate);

        // Verify the HTTP request was made correctly
        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'api.freecurrencyapi.com/v1/latest') &&
                   $request['apikey'] === 'fca_live_0gSS8pGGYNXqGMnKQNOsMoMxZHCf4uEliILF7gI9' &&
                   $request['base_currency'] === 'USD' &&
                   $request['currencies'] === 'EUR';
        });
    }

    #[Test]
    public function it_returns_null_when_freecurrency_api_key_not_configured()
    {
        // Create settings with empty API key
        $group = SettingGroup::firstOrCreate([
            'key' => 'currency'
        ], [
            'label' => 'Currency Settings',
            'description' => 'Currency settings for testing',
            'icon' => 'fas fa-coins',
            'sort_order' => 1,
        ]);

        Setting::create([
            'group_id' => $group->id,
            'key' => 'currency_freecurrency_api_key',
            'value' => '',
            'type' => 'string',
            'input_type' => 'password',
            'label' => 'FreeCurrencyAPI Key',
            'description' => 'API key for FreeCurrencyAPI service (free tier: 5000 requests/month)',
            'validation_rules' => 'nullable|string|max:255',
            'is_public' => false,
            'sort_order' => 13,
        ]);

        Cache::forget('settings');

        $rate = $this->exchangeRateService->fetchExchangeRate('USD', 'EUR', 'freecurrency');

        $this->assertNull($rate);
    }

    #[Test]
    public function it_returns_null_when_freecurrency_api_fails()
    {
        $this->createCurrencySettings();

        // Mock HTTP failure response
        Http::fake([
            'api.freecurrencyapi.com/*' => Http::response(['error' => 'API limit exceeded'], 429)
        ]);

        $rate = $this->exchangeRateService->fetchExchangeRate('USD', 'EUR', 'freecurrency');

        $this->assertNull($rate);
    }

    #[Test]
    public function it_can_fetch_mock_exchange_rate()
    {
        $rate = $this->exchangeRateService->fetchExchangeRate('USD', 'EUR', 'mock');

        $this->assertIsFloat($rate);
        $this->assertGreaterThan(0, $rate);
        $this->assertLessThan(2, $rate); // EUR rate should be less than 2
    }

    #[Test]
    public function it_can_update_exchange_rate_in_database()
    {
        Currency::factory()->create(['code' => 'USD']);
        Currency::factory()->create(['code' => 'EUR']);

        $exchangeRate = $this->exchangeRateService->updateExchangeRate('USD', 'EUR', 0.85);

        $this->assertInstanceOf(ExchangeRate::class, $exchangeRate);
        $this->assertEquals('USD', $exchangeRate->base_currency);
        $this->assertEquals('EUR', $exchangeRate->target_currency);
        $this->assertEquals(0.85, $exchangeRate->rate);
        $this->assertNotNull($exchangeRate->fetched_at);

        // Check reverse rate was also created
        $reverseRate = ExchangeRate::where('base_currency', 'EUR')
            ->where('target_currency', 'USD')
            ->first();

        $this->assertNotNull($reverseRate);
        $this->assertEqualsWithDelta(1 / 0.85, $reverseRate->rate, 0.0001);
    }

    #[Test]
    public function it_can_update_rates_from_api()
    {
        Currency::factory()->create(['code' => 'USD', 'status' => 'active']);
        Currency::factory()->create(['code' => 'EUR', 'status' => 'active']);
        Currency::factory()->create(['code' => 'GBP', 'status' => 'active']);

        $results = $this->exchangeRateService->updateRatesFromAPI('USD', ['EUR', 'GBP'], 'mock');

        $this->assertEquals(2, $results['updated']);
        $this->assertEquals(0, $results['failed']);
        $this->assertCount(2, $results['rates']);

        // Check rates were saved to database
        $this->assertDatabaseHas('exchange_rates', [
            'base_currency' => 'USD',
            'target_currency' => 'EUR'
        ]);
        $this->assertDatabaseHas('exchange_rates', [
            'base_currency' => 'USD',
            'target_currency' => 'GBP'
        ]);
    }

    #[Test]
    public function it_can_get_stale_rates()
    {
        $freshRate = ExchangeRate::factory()->create([
            'fetched_at' => now()->subHours(1)
        ]);
        $staleRate = ExchangeRate::factory()->create([
            'fetched_at' => now()->subDays(2)
        ]);
        $nullRate = ExchangeRate::factory()->create([
            'fetched_at' => null
        ]);

        $staleRates = $this->exchangeRateService->getStaleRates(24);

        $this->assertCount(2, $staleRates);
        $this->assertTrue($staleRates->contains($staleRate));
        $this->assertTrue($staleRates->contains($nullRate));
        $this->assertFalse($staleRates->contains($freshRate));
    }

    #[Test]
    public function it_can_check_if_rates_need_update()
    {
        // Create fresh rate
        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'fetched_at' => now()->subMinutes(30)
        ]);

        // Create stale rate
        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'GBP',
            'fetched_at' => now()->subHours(2)
        ]);

        $needsUpdate = $this->exchangeRateService->needsUpdate('USD', ['EUR', 'GBP'], 1);
        $this->assertTrue($needsUpdate); // GBP rate is stale

        $needsUpdate = $this->exchangeRateService->needsUpdate('USD', ['EUR'], 1);
        $this->assertFalse($needsUpdate); // EUR rate is fresh
    }

    #[Test]
    public function it_can_batch_fetch_exchange_rates()
    {
        $rates = $this->exchangeRateService->batchFetchExchangeRates('USD', ['EUR', 'GBP'], 'mock');

        $this->assertArrayHasKey('EUR', $rates);
        $this->assertArrayHasKey('GBP', $rates);
        $this->assertIsFloat($rates['EUR']);
        $this->assertIsFloat($rates['GBP']);
    }

    #[Test]
    public function it_can_get_supported_api_providers()
    {
        $providers = $this->exchangeRateService->getSupportedApiProviders();

        $expectedProviders = ['freecurrency', 'exchangerate', 'fixer', 'currencylayer', 'mock'];
        $this->assertEquals($expectedProviders, $providers);
    }

    #[Test]
    public function it_can_validate_api_provider()
    {
        $this->assertTrue($this->exchangeRateService->isValidApiProvider('freecurrency'));
        $this->assertTrue($this->exchangeRateService->isValidApiProvider('mock'));
        $this->assertFalse($this->exchangeRateService->isValidApiProvider('invalid'));
    }

    #[Test]
    public function it_can_get_api_provider_info()
    {
        $info = $this->exchangeRateService->getApiProviderInfo('freecurrency');

        $this->assertIsArray($info);
        $this->assertEquals('FreeCurrencyAPI', $info['name']);
        $this->assertTrue($info['requires_key']);
        $this->assertEquals('5000 requests/month', $info['free_tier']);
        $this->assertEquals('https://freecurrencyapi.com', $info['url']);
    }

    #[Test]
    public function it_returns_null_for_unknown_api_provider_info()
    {
        $info = $this->exchangeRateService->getApiProviderInfo('unknown');

        $this->assertNull($info);
    }

    #[Test]
    public function it_can_check_if_api_key_is_configured()
    {
        $this->createCurrencySettings();

        $this->assertTrue($this->exchangeRateService->isApiKeyConfigured('freecurrency'));
        $this->assertFalse($this->exchangeRateService->isApiKeyConfigured('fixer')); // Empty in settings
        $this->assertTrue($this->exchangeRateService->isApiKeyConfigured('exchangerate')); // No key required
        $this->assertTrue($this->exchangeRateService->isApiKeyConfigured('mock')); // No key required
    }

    #[Test]
    public function it_can_get_last_update_time()
    {
        $fetchedAt = now()->subHours(2);
        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'fetched_at' => $fetchedAt
        ]);

        $lastUpdate = $this->exchangeRateService->getLastUpdateTime('USD', 'EUR');

        $this->assertNotNull($lastUpdate);
        $this->assertTrue($lastUpdate->eq($fetchedAt));
    }

    #[Test]
    public function it_returns_null_for_non_existent_rate_last_update()
    {
        $lastUpdate = $this->exchangeRateService->getLastUpdateTime('USD', 'XYZ');

        $this->assertNull($lastUpdate);
    }

    #[Test]
    public function it_can_check_if_single_rate_needs_update()
    {
        $this->createCurrencySettings();

        // Create fresh rate
        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'fetched_at' => now()->subHours(1)
        ]);

        // Create stale rate
        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'GBP',
            'fetched_at' => now()->subDays(2)
        ]);

        $this->assertFalse($this->exchangeRateService->needsSingleUpdate('USD', 'EUR'));
        $this->assertTrue($this->exchangeRateService->needsSingleUpdate('USD', 'GBP'));
        $this->assertTrue($this->exchangeRateService->needsSingleUpdate('USD', 'JPY')); // Non-existent
    }

    #[Test]
    public function it_can_generate_cache_key()
    {
        $cacheKey = $this->exchangeRateService->getCacheKey('USD', 'EUR');

        $this->assertEquals('exchange_rates.USD.EUR', $cacheKey);
    }

    #[Test]
    public function it_can_clear_rate_cache()
    {
        $cacheKey = 'exchange_rates.USD.EUR';
        Cache::put($cacheKey, 0.85, 300);

        $this->assertTrue(Cache::has($cacheKey));

        $cleared = $this->exchangeRateService->clearRateCache('USD', 'EUR');

        $this->assertTrue($cleared);
        $this->assertFalse(Cache::has($cacheKey));
    }

    #[Test]
    public function it_throws_exception_for_unknown_api_provider()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Unknown API provider: unknown');

        $this->exchangeRateService->fetchExchangeRate('USD', 'EUR', 'unknown');
    }
}
