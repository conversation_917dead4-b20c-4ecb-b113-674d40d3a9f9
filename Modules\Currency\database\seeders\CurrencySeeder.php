<?php

namespace Modules\Currency\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Currency\Models\Currency;
use Modules\Currency\Models\ExchangeRate;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currencies = [
            [
                'code' => 'USD',
                'symbol' => '$',
                'name' => 'US Dollar',
                'decimal_digits' => 2,
                'decimal_separator' => '.',
                'thousands_separator' => ',',
                'status' => 'active',
                'sort_order' => 1,
            ],
            [
                'code' => 'VND',
                'symbol' => '₫',
                'name' => 'Vietnamese Dong',
                'decimal_digits' => 0,
                'decimal_separator' => '.',
                'thousands_separator' => ',',
                'status' => 'active',
                'sort_order' => 2,
            ],
            [
                'code' => 'EUR',
                'symbol' => '€',
                'name' => 'Euro',
                'decimal_digits' => 2,
                'decimal_separator' => ',',
                'thousands_separator' => '.',
                'status' => 'active',
                'sort_order' => 3,
            ],
            [
                'code' => 'GBP',
                'symbol' => '£',
                'name' => 'British Pound',
                'decimal_digits' => 2,
                'decimal_separator' => '.',
                'thousands_separator' => ',',
                'status' => 'active',
                'sort_order' => 4,
            ],
            [
                'code' => 'JPY',
                'symbol' => '¥',
                'name' => 'Japanese Yen',
                'decimal_digits' => 0,
                'decimal_separator' => '.',
                'thousands_separator' => ',',
                'status' => 'active',
                'sort_order' => 5,
            ],
        ];

        foreach ($currencies as $currency) {
            Currency::updateOrCreate(
                ['code' => $currency['code']],
                $currency
            );
        }

        // Sample exchange rates (USD as base)
        $exchangeRates = [
            ['base_currency' => 'USD', 'target_currency' => 'VND', 'rate' => 23000.0],
            ['base_currency' => 'USD', 'target_currency' => 'EUR', 'rate' => 0.85],
            ['base_currency' => 'USD', 'target_currency' => 'GBP', 'rate' => 0.73],
            ['base_currency' => 'USD', 'target_currency' => 'JPY', 'rate' => 110.0],
        ];

        foreach ($exchangeRates as $rate) {
            ExchangeRate::updateOrCreate(
                [
                    'base_currency' => $rate['base_currency'],
                    'target_currency' => $rate['target_currency'],
                ],
                [
                    'rate' => $rate['rate'],
                    'fetched_at' => now(),
                ]
            );

            // Create reverse rate
            ExchangeRate::updateOrCreate(
                [
                    'base_currency' => $rate['target_currency'],
                    'target_currency' => $rate['base_currency'],
                ],
                [
                    'rate' => 1 / $rate['rate'],
                    'fetched_at' => now(),
                ]
            );
        }
    }
}
