<?php

namespace Modules\Location\Tests\Feature;

use Tests\TestCase;
use Modules\Location\Models\Country;
use Modules\Location\Models\GeoDivision;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;

class LocationApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations for Location module
        $this->artisan('migrate', ['--path' => 'Modules/Location/database/migrations']);
    }

    #[Test]
    public function it_can_get_all_active_countries()
    {
        Country::factory()->count(3)->active()->create();
        Country::factory()->inactive()->create();

        $response = $this->getJson('/api/v1/locations/countries');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'native_name',
                        'iso_code_2',
                        'iso_code_3',
                        'emoji'
                    ]
                ]
            ]);

        $this->assertCount(3, $response->json('data'));
    }

    #[Test]
    public function it_can_get_states_for_country()
    {
        $country = Country::factory()->create();
        $states = GeoDivision::factory()->count(3)->state()->forCountry($country->id)->create();
        GeoDivision::factory()->city()->forCountry($country->id)->create(); // Should not be included

        $response = $this->getJson("/api/v1/locations/countries/{$country->id}/states");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'native_name',
                        'code',
                        'country_id'
                    ]
                ]
            ]);

        $this->assertCount(3, $response->json('data'));
    }

    #[Test]
    public function it_can_get_cities_for_state()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $cities = GeoDivision::factory()->count(2)->city()->forCountry($country->id)->childOf($state->id)->create();

        $response = $this->getJson("/api/v1/locations/states/{$state->id}/cities");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'native_name',
                        'code',
                        'country_id',
                        'parent_id',

                    ]
                ]
            ]);

        $this->assertCount(2, $response->json('data'));
    }

    #[Test]
    public function it_can_get_districts_for_city()
    {
        $country = Country::factory()->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();
        $districts = GeoDivision::factory()->count(2)->district()->forCountry($country->id)->childOf($city->id)->create();

        $response = $this->getJson("/api/v1/locations/cities/{$city->id}/districts");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'native_name',
                        'code',
                        'country_id',
                        'parent_id'
                    ]
                ]
            ]);

        $this->assertCount(2, $response->json('data'));
    }

    #[Test]
    public function it_can_get_hierarchical_data()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->childOf($state->id)->create();

        $response = $this->getJson('/api/v1/locations/hierarchical?' . http_build_query([
            'country_id' => $country->id,
            'state_id' => $state->id,
            'city_id' => $city->id
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'countries',
                    'states',
                    'cities',
                    'districts'
                ]
            ]);

        $data = $response->json('data');
        $this->assertNotEmpty($data['countries']);
        $this->assertNotEmpty($data['states']);
        $this->assertNotEmpty($data['cities']);
    }

    #[Test]
    public function it_can_search_locations()
    {
        $country = Country::factory()->create(['name' => 'United States']);
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create(['name' => 'California']);

        $response = $this->getJson('/api/v1/locations/search?' . http_build_query([
            'query' => 'United'
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'type',
                        'full_name'
                    ]
                ]
            ]);

        $this->assertNotEmpty($response->json('data'));
    }

    #[Test]
    public function it_validates_search_query_parameters()
    {
        $response = $this->getJson('/api/v1/locations/search');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['query']);
    }

    #[Test]
    public function it_can_search_locations_by_type()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create(['name' => 'Test State']);
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create(['name' => 'Test City']);

        $response = $this->getJson('/api/v1/locations/search?' . http_build_query([
            'query' => 'Test',
            'type' => 'state'
        ]));

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertNotEmpty($data);
        $this->assertEquals('state', $data[0]['type']);
    }

    #[Test]
    public function it_can_search_locations_by_country()
    {
        $country1 = Country::factory()->create();
        $country2 = Country::factory()->create();
        
        $state1 = GeoDivision::factory()->state()->forCountry($country1->id)->create(['name' => 'Test Division']);
        $state2 = GeoDivision::factory()->state()->forCountry($country2->id)->create(['name' => 'Test Division']);

        $response = $this->getJson('/api/v1/locations/search?' . http_build_query([
            'query' => 'Test',
            'country_id' => $country1->id
        ]));

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($country1->name, $data[0]['country']);
    }

    #[Test]
    public function it_respects_search_limit()
    {
        $country = Country::factory()->create();
        GeoDivision::factory()->count(10)->forCountry($country->id)->create(['name' => 'Test Division']);

        $response = $this->getJson('/api/v1/locations/search?' . http_build_query([
            'query' => 'Test',
            'limit' => 5
        ]));

        $response->assertStatus(200);
        $this->assertCount(5, $response->json('data'));
    }

    #[Test]
    public function it_can_get_country_by_iso_code()
    {
        $country = Country::factory()->create(['iso_code_2' => 'US']);

        $response = $this->getJson('/api/v1/locations/country/US');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'iso_code_2',
                    'iso_code_3'
                ]
            ]);

        $this->assertEquals($country->id, $response->json('data.id'));
    }

    #[Test]
    public function it_returns_404_for_invalid_iso_code()
    {
        $response = $this->getJson('/api/v1/locations/country/INVALID');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => __('Country not found.')
            ]);
    }

    #[Test]
    public function it_can_get_division_by_id()
    {
        $country = Country::factory()->create();
        $division = GeoDivision::factory()->forCountry($country->id)->create();

        $response = $this->getJson("/api/v1/locations/division/{$division->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'type',
                    'country_id'
                ]
            ]);

        $this->assertEquals($division->id, $response->json('data.id'));
    }

    #[Test]
    public function it_returns_404_for_invalid_division_id()
    {
        $response = $this->getJson('/api/v1/locations/division/999');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => __('Geographic division not found.')
            ]);
    }

    #[Test]
    public function it_can_get_all_divisions_for_country()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();

        $response = $this->getJson("/api/v1/locations/countries/{$country->id}/divisions");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'type',
                        'level',
                        'country_id'
                    ]
                ]
            ]);

        $this->assertCount(2, $response->json('data'));
    }

    #[Test]
    public function it_can_get_divisions_for_country_by_type()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();

        $response = $this->getJson("/api/v1/locations/countries/{$country->id}/divisions?" . http_build_query([
            'type' => 'state'
        ]));

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals('state', $data[0]['type']);
    }

    #[Test]
    public function it_returns_empty_array_for_non_existent_country_states()
    {
        $response = $this->getJson('/api/v1/locations/countries/999/states');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => []
            ]);
    }

    #[Test]
    public function it_returns_empty_array_for_non_existent_state_cities()
    {
        $response = $this->getJson('/api/v1/locations/states/999/cities');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => []
            ]);
    }

    #[Test]
    public function it_returns_empty_array_for_non_existent_city_districts()
    {
        $response = $this->getJson('/api/v1/locations/cities/999/districts');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => []
            ]);
    }

    #[Test]
    public function it_validates_search_type_parameter()
    {
        $response = $this->getJson('/api/v1/locations/search?' . http_build_query([
            'query' => 'test',
            'type' => 'invalid'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['type']);
    }

    #[Test]
    public function it_validates_search_country_id_parameter()
    {
        $response = $this->getJson('/api/v1/locations/search?' . http_build_query([
            'query' => 'test',
            'country_id' => 999
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['country_id']);
    }

    #[Test]
    public function it_validates_search_limit_parameter()
    {
        $response = $this->getJson('/api/v1/locations/search?' . http_build_query([
            'query' => 'test',
            'limit' => 101
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['limit']);
    }

    #[Test]
    public function hierarchical_data_returns_empty_collections_when_no_parameters()
    {
        Country::factory()->count(2)->create();

        $response = $this->getJson('/api/v1/locations/hierarchical');

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertNotEmpty($data['countries']);
        $this->assertEmpty($data['states']);
        $this->assertEmpty($data['cities']);
        $this->assertEmpty($data['districts']);
    }

    #[Test]
    public function it_only_returns_active_locations_in_public_endpoints()
    {
        $country = Country::factory()->active()->create();
        Country::factory()->inactive()->create();
        
        $activeState = GeoDivision::factory()->state()->active()->forCountry($country->id)->create();
        GeoDivision::factory()->state()->inactive()->forCountry($country->id)->create();

        // Test countries endpoint
        $response = $this->getJson('/api/v1/locations/countries');
        $this->assertCount(1, $response->json('data'));

        // Test states endpoint
        $response = $this->getJson("/api/v1/locations/countries/{$country->id}/states");
        $this->assertCount(1, $response->json('data'));
    }
}
