<?php

namespace Modules\Currency\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Currency\Models\ExchangeRate;

class ExchangeRateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $exchangeRates = [
            // USD as base currency
            ['base_currency' => 'USD', 'target_currency' => 'VND', 'rate' => 23000.0],
            ['base_currency' => 'USD', 'target_currency' => 'EUR', 'rate' => 0.85],
            ['base_currency' => 'USD', 'target_currency' => 'GBP', 'rate' => 0.73],
            ['base_currency' => 'USD', 'target_currency' => 'JPY', 'rate' => 110.0],
            ['base_currency' => 'USD', 'target_currency' => 'CNY', 'rate' => 6.45],
            ['base_currency' => 'USD', 'target_currency' => 'KRW', 'rate' => 1180.0],
            ['base_currency' => 'USD', 'target_currency' => 'THB', 'rate' => 33.5],
            ['base_currency' => 'USD', 'target_currency' => 'SGD', 'rate' => 1.35],
            ['base_currency' => 'USD', 'target_currency' => 'MYR', 'rate' => 4.15],
            ['base_currency' => 'USD', 'target_currency' => 'IDR', 'rate' => 14500.0],

            // Reverse rates (target to USD)
            ['base_currency' => 'VND', 'target_currency' => 'USD', 'rate' => 1/23000.0],
            ['base_currency' => 'EUR', 'target_currency' => 'USD', 'rate' => 1/0.85],
            ['base_currency' => 'GBP', 'target_currency' => 'USD', 'rate' => 1/0.73],
            ['base_currency' => 'JPY', 'target_currency' => 'USD', 'rate' => 1/110.0],
            ['base_currency' => 'CNY', 'target_currency' => 'USD', 'rate' => 1/6.45],
            ['base_currency' => 'KRW', 'target_currency' => 'USD', 'rate' => 1/1180.0],
            ['base_currency' => 'THB', 'target_currency' => 'USD', 'rate' => 1/33.5],
            ['base_currency' => 'SGD', 'target_currency' => 'USD', 'rate' => 1/1.35],
            ['base_currency' => 'MYR', 'target_currency' => 'USD', 'rate' => 1/4.15],
            ['base_currency' => 'IDR', 'target_currency' => 'USD', 'rate' => 1/14500.0],

            // Some cross rates (EUR as base)
            ['base_currency' => 'EUR', 'target_currency' => 'VND', 'rate' => 27058.82], // 23000 / 0.85
            ['base_currency' => 'EUR', 'target_currency' => 'GBP', 'rate' => 0.86], // 0.73 / 0.85
            ['base_currency' => 'EUR', 'target_currency' => 'JPY', 'rate' => 129.41], // 110 / 0.85

            // Reverse cross rates
            ['base_currency' => 'VND', 'target_currency' => 'EUR', 'rate' => 1/27058.82],
            ['base_currency' => 'GBP', 'target_currency' => 'EUR', 'rate' => 1/0.86],
            ['base_currency' => 'JPY', 'target_currency' => 'EUR', 'rate' => 1/129.41],
        ];

        foreach ($exchangeRates as $rateData) {
            ExchangeRate::updateOrCreate(
                [
                    'base_currency' => $rateData['base_currency'],
                    'target_currency' => $rateData['target_currency']
                ],
                [
                    'rate' => $rateData['rate'],
                    'fetched_at' => now()
                ]
            );
        }

        $this->command->info('Exchange rates seeded successfully.');
    }
}
