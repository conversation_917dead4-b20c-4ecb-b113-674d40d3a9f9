<?php

namespace Modules\Language\Tests\Unit;

use Tests\TestCase;
use Modules\Language\Models\Language;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\Test;

class LanguageModelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations for Language module
        $this->artisan('migrate', ['--path' => 'Modules/Language/database/migrations']);
    }

    #[Test]
    public function it_has_correct_fillable_attributes()
    {
        $language = new Language();

        $expectedFillable = [
            'code',
            'name',
            'native_name',
            'flag',
            'direction',
            'is_default',
            'status',
        ];

        $this->assertEquals($expectedFillable, $language->getFillable());
    }

    #[Test]
    public function it_has_correct_casts()
    {
        $language = new Language();

        $expectedCasts = [
            'is_default' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];

        foreach ($expectedCasts as $attribute => $cast) {
            $this->assertEquals($cast, $language->getCasts()[$attribute]);
        }
    }

    #[Test]
    public function it_has_correct_hidden_attributes()
    {
        $language = new Language();

        $this->assertEquals(['deleted_at'], $language->getHidden());
    }

    #[Test]
    public function it_uses_soft_deletes()
    {
        $this->assertContains(SoftDeletes::class, class_uses(Language::class));
    }

    #[Test]
    public function it_can_create_a_language()
    {
        $languageData = [
            'code' => 'en',
            'name' => 'English',
            'native_name' => 'English',
            'flag' => 'us',
            'direction' => 'ltr',
            'is_default' => 1,
            'status' => 'active'
        ];

        $language = Language::create($languageData);

        $this->assertInstanceOf(Language::class, $language);
        $this->assertDatabaseHas('languages', $languageData);
        $this->assertEquals('en', $language->code);
        $this->assertEquals('English', $language->name);
        $this->assertEquals(1, $language->is_default);
    }

    #[Test]
    public function it_can_filter_active_languages()
    {
        Language::factory()->create(['status' => 'active', 'code' => 'en']);
        Language::factory()->create(['status' => 'inactive', 'code' => 'es']);
        Language::factory()->create(['status' => 'active', 'code' => 'fr']);

        $activeLanguages = Language::active()->get();

        $this->assertCount(2, $activeLanguages);
        $this->assertTrue($activeLanguages->every(fn($lang) => $lang->status === 'active'));
    }

    #[Test]
    public function it_can_filter_by_status()
    {
        Language::factory()->create(['status' => 'active']);
        Language::factory()->create(['status' => 'inactive']);
        Language::factory()->create(['status' => 'active']);

        $activeLanguages = Language::status('active')->get();
        $inactiveLanguages = Language::status('inactive')->get();

        $this->assertCount(2, $activeLanguages);
        $this->assertCount(1, $inactiveLanguages);
    }

    #[Test]
    public function it_ensures_only_one_default_language_exists()
    {
        // Create first default language
        $language1 = Language::factory()->create(['is_default' => 1, 'code' => 'en']);

        // Create second language and set as default
        $language2 = Language::factory()->create(['is_default' => 0, 'code' => 'es']);
        $language2->update(['is_default' => 1]);

        // Refresh first language from database
        $language1->refresh();

        // First language should no longer be default
        $this->assertEquals(0, $language1->is_default);
        $this->assertEquals(1, $language2->is_default);

        // Only one default should exist in database
        $defaultCount = Language::where('is_default', 1)->count();
        $this->assertEquals(1, $defaultCount);
    }

    #[Test]
    public function it_can_be_soft_deleted()
    {
        $language = Language::factory()->create();

        $language->delete();

        $this->assertSoftDeleted('languages', ['id' => $language->id]);
        $this->assertNotNull($language->fresh()->deleted_at);
    }

    #[Test]
    public function it_can_be_restored_after_soft_delete()
    {
        $language = Language::factory()->create();

        $language->delete();
        $language->restore();

        $this->assertDatabaseHas('languages', [
            'id' => $language->id,
            'deleted_at' => null
        ]);
    }

    #[Test]
    public function it_can_be_force_deleted()
    {
        $language = Language::factory()->create();
        $languageId = $language->id;

        $language->forceDelete();

        $this->assertDatabaseMissing('languages', ['id' => $languageId]);
    }

    #[Test]
    public function it_has_correct_table_structure()
    {
        $this->assertTrue(Schema::hasTable('languages'));

        $expectedColumns = [
            'id', 'code', 'name', 'native_name', 'flag',
            'direction', 'is_default', 'status',
            'created_at', 'updated_at', 'deleted_at'
        ];

        foreach ($expectedColumns as $column) {
            $this->assertTrue(Schema::hasColumn('languages', $column));
        }
    }

    #[Test]
    public function it_has_factory()
    {
        $language = Language::factory()->make();

        $this->assertInstanceOf(Language::class, $language);
        $this->assertNotEmpty($language->code);
        $this->assertNotEmpty($language->name);
    }

    #[Test]
    public function factory_creates_unique_codes()
    {
        $languages = Language::factory()->count(3)->create();

        $codes = $languages->pluck('code')->toArray();
        $uniqueCodes = array_unique($codes);

        $this->assertCount(3, $uniqueCodes);
    }

    #[Test]
    public function factory_can_create_active_language()
    {
        $language = Language::factory()->active()->create();

        $this->assertEquals('active', $language->status);
    }

    #[Test]
    public function factory_can_create_default_language()
    {
        $language = Language::factory()->default()->create();

        $this->assertEquals(1, $language->is_default);
        $this->assertEquals('active', $language->status);
    }

    #[Test]
    public function factory_can_create_rtl_language()
    {
        $language = Language::factory()->rtl()->create();

        $this->assertEquals('rtl', $language->direction);
    }

    #[Test]
    public function it_validates_direction_values()
    {
        $language = Language::factory()->create(['direction' => 'ltr']);
        $this->assertEquals('ltr', $language->direction);

        $language = Language::factory()->create(['direction' => 'rtl']);
        $this->assertEquals('rtl', $language->direction);
    }

    #[Test]
    public function it_validates_status_values()
    {
        $language = Language::factory()->create(['status' => 'active']);
        $this->assertEquals('active', $language->status);

        $language = Language::factory()->create(['status' => 'inactive']);
        $this->assertEquals('inactive', $language->status);
    }

    #[Test]
    public function it_casts_is_default_to_integer()
    {
        $language = Language::factory()->create(['is_default' => '1']);

        $this->assertIsInt($language->is_default);
        $this->assertEquals(1, $language->is_default);
    }
}
