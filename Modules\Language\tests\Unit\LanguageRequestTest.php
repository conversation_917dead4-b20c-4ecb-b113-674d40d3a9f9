<?php

namespace Modules\Language\Tests\Unit;

use Tests\TestCase;
use Modules\Language\Models\Language;
use Modules\Language\Http\Requests\LanguageRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use PHPUnit\Framework\Attributes\Test;

class LanguageRequestTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations for Language module
        $this->artisan('migrate', ['--path' => 'Modules/Language/database/migrations']);
    }

    #[Test]
    public function it_validates_required_fields()
    {
        $request = new LanguageRequest();
        $rules = $request->rules();

        // Test required fields
        $requiredFields = ['code', 'name', 'direction', 'is_default', 'status'];

        foreach ($requiredFields as $field) {
            $this->assertContains('required', $rules[$field]);
        }
    }

    #[Test]
    public function it_validates_code_field_correctly()
    {
        $validData = $this->getValidLanguageData();

        // Test valid code
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());

        // Test missing code
        unset($validData['code']);
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('code', $validator->errors()->toArray());

        // Test empty code
        $validData['code'] = '';
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());

        // Test code too long (max 20 characters)
        $validData['code'] = str_repeat('a', 21);
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());

        // Test code with invalid characters (only alpha_dash allowed)
        $validData['code'] = 'en@us';
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());

        // Test valid alpha_dash codes
        $validCodes = ['en', 'en-US', 'en_US', 'zh-CN'];
        foreach ($validCodes as $code) {
            $validData['code'] = $code;
            $validator = Validator::make($validData, (new LanguageRequest())->rules());
            $this->assertTrue($validator->passes(), "Code '{$code}' should be valid");
        }
    }

    #[Test]
    public function it_validates_code_uniqueness()
    {
        // Create existing language
        Language::factory()->create(['code' => 'en']);

        $validData = $this->getValidLanguageData();
        $validData['code'] = 'en'; // Duplicate code

        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('code', $validator->errors()->toArray());
    }

    #[Test]
    public function it_ignores_current_record_when_updating()
    {
        $language = Language::factory()->create(['code' => 'en']);

        // Simulate updating the same record
        $request = new LanguageRequest();
        $request->merge(['id' => $language->id]);

        $validData = $this->getValidLanguageData();
        $validData['code'] = 'en'; // Same code as existing record

        $validator = Validator::make($validData, $request->rules());
        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_validates_name_field()
    {
        $validData = $this->getValidLanguageData();

        // Test valid name
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());

        // Test missing name
        unset($validData['name']);
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());

        // Test empty name
        $validData['name'] = '';
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());

        // Test name too long (max 255 characters)
        $validData['name'] = str_repeat('a', 256);
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());

        // Test valid name at max length
        $validData['name'] = str_repeat('a', 255);
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_validates_native_name_field()
    {
        $validData = $this->getValidLanguageData();

        // Test without native_name (should be valid as it's nullable)
        unset($validData['native_name']);
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());

        // Test with null native_name
        $validData['native_name'] = null;
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());

        // Test with valid native_name
        $validData['native_name'] = 'English';
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());

        // Test native_name too long (max 255 characters)
        $validData['native_name'] = str_repeat('a', 256);
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());
    }

    #[Test]
    public function it_validates_flag_field()
    {
        $validData = $this->getValidLanguageData();

        // Test without flag (should be valid as it's nullable)
        unset($validData['flag']);
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());

        // Test with null flag
        $validData['flag'] = null;
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());

        // Test with valid flag
        $validData['flag'] = 'us';
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());

        // Test flag too long (max 255 characters)
        $validData['flag'] = str_repeat('a', 256);
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());
    }

    #[Test]
    public function it_validates_direction_field()
    {
        $validData = $this->getValidLanguageData();

        // Test valid directions
        $validDirections = ['ltr', 'rtl'];
        foreach ($validDirections as $direction) {
            $validData['direction'] = $direction;
            $validator = Validator::make($validData, (new LanguageRequest())->rules());
            $this->assertTrue($validator->passes(), "Direction '{$direction}' should be valid");
        }

        // Test invalid direction
        $validData['direction'] = 'invalid';
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());

        // Test missing direction
        unset($validData['direction']);
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());
    }

    #[Test]
    public function it_validates_status_field()
    {
        $validData = $this->getValidLanguageData();

        // Test valid statuses
        $validStatuses = ['active', 'inactive'];
        foreach ($validStatuses as $status) {
            $validData['status'] = $status;
            $validator = Validator::make($validData, (new LanguageRequest())->rules());
            $this->assertTrue($validator->passes(), "Status '{$status}' should be valid");
        }

        // Test invalid status
        $validData['status'] = 'invalid';
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());

        // Test missing status
        unset($validData['status']);
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());
    }

    #[Test]
    public function it_validates_is_default_field()
    {
        $validData = $this->getValidLanguageData();

        // Test valid is_default values
        $validValues = [0, 1, '0', '1', true, false];
        foreach ($validValues as $value) {
            $validData['is_default'] = $value;
            $validator = Validator::make($validData, (new LanguageRequest())->rules());
            $this->assertTrue($validator->passes(), "is_default value '{$value}' should be valid");
        }

        // Test missing is_default
        unset($validData['is_default']);
        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertFalse($validator->passes());
    }

    #[Test]
    public function it_ignores_soft_deleted_records_for_uniqueness()
    {
        // Create and soft delete a language
        $language = Language::factory()->create(['code' => 'en']);
        $language->delete();

        $validData = $this->getValidLanguageData();
        $validData['code'] = 'en'; // Same code as soft deleted record

        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_validates_complete_valid_data()
    {
        $validData = [
            'code' => 'en-US',
            'name' => 'English (United States)',
            'native_name' => 'English (United States)',
            'flag' => 'us',
            'direction' => 'ltr',
            'is_default' => 1,
            'status' => 'active'
        ];

        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_validates_minimal_valid_data()
    {
        $minimalData = [
            'code' => 'en',
            'name' => 'English',
            'direction' => 'ltr',
            'is_default' => 0,
            'status' => 'active'
        ];

        $validator = Validator::make($minimalData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_handles_unicode_characters_in_native_name()
    {
        $validData = $this->getValidLanguageData();
        $validData['native_name'] = 'العربية'; // Arabic text
        $validData['code'] = 'ar';

        $validator = Validator::make($validData, (new LanguageRequest())->rules());
        $this->assertTrue($validator->passes());
    }
}
