<template>
  <div class="procms-chatbot-widget" :class="widgetClasses" :style="widgetStyles">
    <!-- Header -->
    <div v-if="config.showHeader" class="widget-header">
      <div class="header-content">
        <img 
          v-if="config.showAvatar && botConfig.logoUrl" 
          :src="botConfig.logoUrl" 
          :alt="botConfig.name"
          class="bot-avatar"
        />
        <div class="bot-info">
          <h3 class="bot-name">{{ botConfig.name }}</h3>
          <span class="bot-status">Online</span>
        </div>
      </div>
      <button @click="closeWidget" class="close-button">×</button>
    </div>

    <!-- Messages Container -->
    <div class="messages-container" ref="messagesContainer">
      <!-- Greeting Message -->
      <div v-if="botConfig.greetingMessage" class="message bot-message">
        <img 
          v-if="config.showAvatar && botConfig.logoUrl" 
          :src="botConfig.logoUrl" 
          :alt="botConfig.name"
          class="message-avatar"
        />
        <div class="message-content">
          <div class="message-text">{{ botConfig.greetingMessage }}</div>
          <div class="message-time">{{ formatTime(new Date()) }}</div>
        </div>
      </div>

      <!-- Starter Messages -->
      <div v-if="showStarterMessages && botConfig.starterMessages?.length" class="starter-messages">
        <button 
          v-for="(message, index) in botConfig.starterMessages" 
          :key="index"
          @click="sendStarterMessage(message)"
          class="starter-message-btn"
        >
          {{ message }}
        </button>
      </div>

      <!-- Chat Messages -->
      <div 
        v-for="message in messages" 
        :key="message.id"
        class="message"
        :class="{ 'user-message': message.type === 'user', 'bot-message': message.type === 'bot' }"
      >
        <img 
          v-if="message.type === 'bot' && config.showAvatar && botConfig.logoUrl" 
          :src="botConfig.logoUrl" 
          :alt="botConfig.name"
          class="message-avatar"
        />
        <div class="message-content">
          <div class="message-text">{{ message.content }}</div>
          <div class="message-time">{{ formatTime(message.timestamp) }}</div>
        </div>
      </div>

      <!-- Typing Indicator -->
      <div v-if="isTyping" class="message bot-message typing">
        <img 
          v-if="config.showAvatar && botConfig.logoUrl" 
          :src="botConfig.logoUrl" 
          :alt="botConfig.name"
          class="message-avatar"
        />
        <div class="message-content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- Input Area -->
    <div class="input-area">
      <div class="input-container">
        <input 
          v-model="inputMessage"
          @keypress.enter="sendMessage"
          @input="handleInput"
          :placeholder="inputPlaceholder"
          :disabled="isLoading"
          class="message-input"
          ref="messageInput"
        />
        <button 
          @click="sendMessage"
          :disabled="!inputMessage.trim() || isLoading"
          class="send-button"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
      <button @click="clearError" class="error-close">×</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import type { WidgetConfig, BotConfig, ChatMessage } from '../types';
import { getBotApiClient } from '../api/bot-api';

// Props
interface Props {
  config: WidgetConfig;
  botConfig: BotConfig;
  onMessage?: (data: any) => void;
  onError?: (error: any) => void;
  onClose?: () => void;
  onOpen?: () => void;
  onReady?: () => void;
}

const props = defineProps<Props>();

// Reactive data
const messages = ref<ChatMessage[]>([]);
const inputMessage = ref('');
const isLoading = ref(false);
const isTyping = ref(false);
const errorMessage = ref('');
const showStarterMessages = ref(true);
const conversationId = ref<string | null>(null);

// Refs
const messagesContainer = ref<HTMLElement>();
const messageInput = ref<HTMLInputElement>();

// Computed
const widgetClasses = computed(() => ({
  [`theme-${props.config.theme}`]: true,
  [`position-${props.config.position}`]: true,
  'has-error': !!errorMessage.value
}));

const widgetStyles = computed(() => ({
  '--primary-color': props.botConfig.theme?.primaryColor || '#007bff',
  '--background-color': props.botConfig.theme?.backgroundColor || '#ffffff',
  '--text-color': props.botConfig.theme?.textColor || '#333333',
  '--border-radius': props.botConfig.theme?.borderRadius || '8px',
  '--font-family': props.botConfig.theme?.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
}));

const inputPlaceholder = computed(() => {
  return isLoading.value ? 'Sending...' : 'Type your message...';
});

// Methods
const sendMessage = async () => {
  const message = inputMessage.value.trim();
  if (!message || isLoading.value) return;

  try {
    isLoading.value = true;
    showStarterMessages.value = false;

    // Add user message
    const userMessage: ChatMessage = {
      id: generateId(),
      conversationId: conversationId.value || '',
      type: 'user',
      content: message,
      timestamp: new Date()
    };
    messages.value.push(userMessage);
    inputMessage.value = '';

    // Scroll to bottom
    await nextTick();
    scrollToBottom();

    // Show typing indicator
    isTyping.value = true;

    // Send to API
    const apiClient = getBotApiClient(props.config.apiKey);
    
    // Start conversation if needed
    if (!conversationId.value) {
      const convResponse = await apiClient.startConversation(props.config.botUuid, props.config.userId);
      if (convResponse.success && convResponse.data) {
        conversationId.value = convResponse.data.conversationId;
      }
    }

    // Send message
    const response = await apiClient.sendMessage(
      props.config.botUuid,
      conversationId.value!,
      message
    );

    isTyping.value = false;

    if (response.success && response.data) {
      // Add bot response
      const botMessage: ChatMessage = {
        id: response.data.messageId || generateId(),
        conversationId: conversationId.value!,
        type: 'bot',
        content: response.data.response,
        timestamp: new Date(),
        metadata: {
          tokens: response.data.tokens
        }
      };
      messages.value.push(botMessage);

      // Emit message event
      props.onMessage?.(botMessage);
    } else {
      throw new Error(response.message || 'Failed to send message');
    }

  } catch (error) {
    isTyping.value = false;
    console.error('Failed to send message:', error);
    errorMessage.value = error instanceof Error ? error.message : 'Failed to send message';
    props.onError?.(error);
  } finally {
    isLoading.value = false;
    await nextTick();
    scrollToBottom();
  }
};

const sendStarterMessage = (message: string) => {
  inputMessage.value = message;
  sendMessage();
};

const handleInput = () => {
  // Handle typing events if needed
};

const closeWidget = () => {
  props.onClose?.();
};

const clearError = () => {
  errorMessage.value = '';
};

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

// Lifecycle
onMounted(() => {
  // Focus input
  messageInput.value?.focus();
  
  // Emit ready event
  props.onReady?.();
});

// Watch for new messages to scroll
watch(messages, () => {
  nextTick(() => scrollToBottom());
}, { deep: true });
</script>

<style scoped>
.procms-chatbot-widget {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--background-color);
  border-radius: var(--border-radius);
  font-family: var(--font-family);
  color: var(--text-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--primary-color);
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bot-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.bot-info {
  display: flex;
  flex-direction: column;
}

.bot-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.bot-status {
  font-size: 12px;
  opacity: 0.8;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message {
  display: flex;
  gap: 8px;
}

.user-message {
  flex-direction: row-reverse;
}

.user-message .message-content {
  background: var(--primary-color);
  color: white;
  margin-left: auto;
}

.bot-message .message-content {
  background: #f1f3f5;
  color: var(--text-color);
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
}

.message-text {
  margin-bottom: 4px;
}

.message-time {
  font-size: 11px;
  opacity: 0.6;
}

.starter-messages {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.starter-message-btn {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 20px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.starter-message-btn:hover {
  background: #f8f9fa;
  border-color: var(--primary-color);
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

.input-area {
  padding: 16px;
  border-top: 1px solid #e1e5e9;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #e1e5e9;
  border-radius: 24px;
  outline: none;
  font-size: 14px;
}

.message-input:focus {
  border-color: var(--primary-color);
}

.send-button {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.error-message {
  background: #fee;
  color: #c53030;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #fed7d7;
}

.error-close {
  background: none;
  border: none;
  color: #c53030;
  cursor: pointer;
  font-size: 18px;
}

/* Theme variations */
.theme-dark {
  --background-color: #1a1a1a;
  --text-color: #ffffff;
}

.theme-dark .bot-message .message-content {
  background: #2d3748;
  color: #ffffff;
}

.theme-dark .input-area {
  border-top-color: #4a5568;
}

.theme-dark .message-input {
  background: #2d3748;
  border-color: #4a5568;
  color: #ffffff;
}
</style>
