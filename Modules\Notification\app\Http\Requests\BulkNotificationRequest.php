<?php

namespace Modules\Notification\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseFormRequest;

class BulkNotificationRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'action' => [
                'required',
                'string',
                Rule::in(['delete', 'restore', 'activate', 'deactivate', 'force_delete']),
            ],
        ];

        // Add specific rules based on the resource type
        $resourceType = $this->getResourceType();
        
        switch ($resourceType) {
            case 'notification_types':
                $rules = array_merge($rules, $this->getNotificationTypeRules());
                break;
            case 'notification_templates':
                $rules = array_merge($rules, $this->getNotificationTemplateRules());
                break;
            case 'notification_preferences':
                $rules = array_merge($rules, $this->getNotificationPreferenceRules());
                break;
        }

        return $rules;
    }

    /**
     * Get validation rules for notification types.
     */
    protected function getNotificationTypeRules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
                'max:100',
                'distinct',
            ],
            'ids.*' => [
                'required',
                'integer',
                'exists:notification_types,id',
                'distinct',
            ],
        ];
    }

    /**
     * Get validation rules for notification templates.
     */
    protected function getNotificationTemplateRules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
                'max:100',
                'distinct',
            ],
            'ids.*' => [
                'required',
                'integer',
                'exists:notification_templates,id',
                'distinct',
            ],
        ];
    }

    /**
     * Get validation rules for notification preferences.
     */
    protected function getNotificationPreferenceRules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
                'max:100',
                'distinct',
            ],
            'ids.*' => [
                'required',
                'integer',
                'exists:notification_preferences,id',
                'distinct',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        $resourceType = $this->getResourceType();
        
        $attributes = [
            'action' => __('Action'),
            'ids' => __('IDs'),
            'ids.*' => __('ID'),
        ];

        switch ($resourceType) {
            case 'notification_types':
                $attributes['ids'] = __('Notification Type IDs');
                $attributes['ids.*'] = __('Notification Type ID');
                break;
            case 'notification_templates':
                $attributes['ids'] = __('Template IDs');
                $attributes['ids.*'] = __('Template ID');
                break;
            case 'notification_preferences':
                $attributes['ids'] = __('Preference IDs');
                $attributes['ids.*'] = __('Preference ID');
                break;
        }

        return $attributes;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'action.required' => 'The action field is required.',
            'action.in' => 'The selected action is invalid.',
            'ids.required' => 'At least one ID must be provided.',
            'ids.array' => 'The IDs must be provided as an array.',
            'ids.min' => 'At least one ID must be selected.',
            'ids.max' => 'Cannot process more than 100 items at once.',
            'ids.distinct' => 'Duplicate IDs are not allowed.',
            'ids.*.required' => 'Each ID is required.',
            'ids.*.integer' => 'Each ID must be an integer.',
            'ids.*.exists' => 'One or more selected items do not exist.',
            'ids.*.distinct' => 'Duplicate IDs are not allowed.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $resourceType = $this->getResourceType();
            $action = $this->action;
            $ids = $this->ids ?? [];

            // Validate action compatibility with resource type
            $this->validateActionCompatibility($validator, $resourceType, $action);

            // Validate system notification types protection
            if ($resourceType === 'notification_types' && in_array($action, ['delete', 'force_delete'])) {
                $this->validateSystemNotificationTypes($validator, $ids);
            }

            // Validate items exist and are in correct state for the action
            $this->validateItemsState($validator, $resourceType, $action, $ids);
        });
    }

    /**
     * Validate action compatibility with resource type.
     */
    protected function validateActionCompatibility($validator, string $resourceType, string $action): void
    {
        $allowedActions = [
            'notification_types' => ['delete', 'restore', 'activate', 'deactivate', 'force_delete'],
            'notification_templates' => ['delete', 'restore', 'activate', 'deactivate', 'force_delete'],
            'notification_preferences' => ['delete', 'restore', 'force_delete'],
        ];

        if (!isset($allowedActions[$resourceType]) || !in_array($action, $allowedActions[$resourceType])) {
            $validator->errors()->add(
                'action',
                "The action '{$action}' is not supported for {$resourceType}."
            );
        }
    }

    /**
     * Validate system notification types cannot be deleted.
     */
    protected function validateSystemNotificationTypes($validator, array $ids): void
    {
        $systemTypes = \Modules\Notification\Models\NotificationType::whereIn('id', $ids)
            ->where('is_system', true)
            ->pluck('name')
            ->toArray();

        if (!empty($systemTypes)) {
            $validator->errors()->add(
                'ids',
                'System notification types cannot be deleted: ' . implode(', ', $systemTypes)
            );
        }
    }

    /**
     * Validate items are in correct state for the action.
     */
    protected function validateItemsState($validator, string $resourceType, string $action, array $ids): void
    {
        if (empty($ids)) {
            return;
        }

        $modelClass = $this->getModelClass($resourceType);
        if (!$modelClass) {
            return;
        }

        switch ($action) {
            case 'delete':
                // Items should not be already soft deleted
                $deletedCount = $modelClass::whereIn('id', $ids)->onlyTrashed()->count();
                if ($deletedCount > 0) {
                    $validator->errors()->add(
                        'ids',
                        "Some items are already deleted and cannot be deleted again."
                    );
                }
                break;

            case 'restore':
                // Items should be soft deleted
                $activeCount = $modelClass::whereIn('id', $ids)->withoutTrashed()->count();
                if ($activeCount > 0) {
                    $validator->errors()->add(
                        'ids',
                        "Some items are not deleted and cannot be restored."
                    );
                }
                break;

            case 'activate':
                // Items should be inactive
                if (in_array($resourceType, ['notification_types', 'notification_templates'])) {
                    $activeCount = $modelClass::whereIn('id', $ids)->where('status', 'active')->count();
                    if ($activeCount === count($ids)) {
                        $validator->errors()->add(
                            'ids',
                            "All selected items are already active."
                        );
                    }
                }
                break;

            case 'deactivate':
                // Items should be active
                if (in_array($resourceType, ['notification_types', 'notification_templates'])) {
                    $inactiveCount = $modelClass::whereIn('id', $ids)->where('status', 'inactive')->count();
                    if ($inactiveCount === count($ids)) {
                        $validator->errors()->add(
                            'ids',
                            "All selected items are already inactive."
                        );
                    }
                }
                break;

            case 'force_delete':
                // Items should be soft deleted first
                $activeCount = $modelClass::whereIn('id', $ids)->withoutTrashed()->count();
                if ($activeCount > 0) {
                    $validator->errors()->add(
                        'ids',
                        "Items must be soft deleted before they can be permanently deleted."
                    );
                }
                break;
        }
    }

    /**
     * Get the resource type from the route.
     */
    protected function getResourceType(): string
    {
        $route = $this->route();
        if (!$route) {
            return '';
        }

        $uri = $route->uri();
        
        if (str_contains($uri, 'notification-types')) {
            return 'notification_types';
        } elseif (str_contains($uri, 'notification-templates')) {
            return 'notification_templates';
        } elseif (str_contains($uri, 'notification-preferences')) {
            return 'notification_preferences';
        }

        return '';
    }

    /**
     * Get the model class for the resource type.
     */
    protected function getModelClass(string $resourceType): ?string
    {
        $models = [
            'notification_types' => \Modules\Notification\Models\NotificationType::class,
            'notification_templates' => \Modules\Notification\Models\NotificationTemplate::class,
            'notification_preferences' => \Modules\Notification\Models\NotificationPreference::class,
        ];

        return $models[$resourceType] ?? null;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure ids is an array
        if ($this->has('ids') && !is_array($this->ids)) {
            $ids = is_string($this->ids) ? explode(',', $this->ids) : [];
            $this->merge([
                'ids' => array_filter(array_map('intval', $ids))
            ]);
        }

        // Remove duplicate IDs
        if ($this->has('ids') && is_array($this->ids)) {
            $this->merge([
                'ids' => array_unique($this->ids)
            ]);
        }
    }
}
