<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Modules\ChatBot\Models\Bot;
use Modules\ModelAI\Models\ModelAI;
use Modules\User\Models\User;
use Modules\ChatBot\Enums\BotStatus;
use Modules\ChatBot\Enums\BotVisibility;
use Modules\ChatBot\Enums\BotType;
use Modules\ChatBot\Enums\ToolCallingMode;
use Illuminate\Support\Str;

echo "🤖 CREATING TEST BOT FOR WIDGET\n";
echo "===============================\n\n";

try {
    // Get required data
    $user = User::first();
    $modelAI = ModelAI::first();
    
    if (!$user) {
        echo "❌ No users found. Please create a user first.\n";
        exit(1);
    }
    
    if (!$modelAI) {
        echo "❌ No AI models found. Please seed ModelAI first.\n";
        exit(1);
    }
    
    echo "📋 Using:\n";
    echo "   User: {$user->name} (ID: {$user->id})\n";
    echo "   Model: {$modelAI->name}\n\n";
    
    // Check if test bot already exists
    $existingBot = Bot::where('name', 'Widget Test Bot')->first();
    if ($existingBot) {
        echo "🔍 Found existing test bot:\n";
        echo "   Name: {$existingBot->name}\n";
        echo "   UUID: {$existingBot->uuid}\n";
        echo "   API Key: {$existingBot->api_key}\n";
        echo "   Status: {$existingBot->status->value}\n\n";
        
        echo "✅ Test bot already exists! Use these credentials:\n";
        echo "   Bot UUID: {$existingBot->uuid}\n";
        echo "   API Key: {$existingBot->api_key}\n\n";
        
        // Update widget test page with these credentials
        updateTestPage($existingBot->uuid, $existingBot->api_key);
        exit(0);
    }
    
    // Create new test bot
    echo "🚀 Creating new test bot...\n";
    
    $bot = Bot::create([
        'uuid' => Str::uuid(),
        'name' => 'Widget Test Bot',
        'description' => 'Test bot for widget integration testing',
        'owner_id' => $user->id,
        'owner_type' => get_class($user),
        'model_ai_id' => $modelAI->id,
        'system_prompt' => 'You are a helpful AI assistant for testing the widget integration. Be friendly, helpful, and respond in Vietnamese when appropriate. You can help with general questions, provide information, and test the chat functionality.',
        'greeting_message' => 'Xin chào! Tôi là bot test cho widget. Tôi có thể giúp gì cho bạn?',
        'starter_messages' => [
            'Chào bạn!',
            'Widget hoạt động như thế nào?',
            'Test tin nhắn',
            'Giúp tôi test chat',
            'Bạn có thể làm gì?'
        ],
        'closing_message' => 'Cảm ơn bạn đã test widget! Hẹn gặp lại.',
        'parameters' => [
            'temperature' => 0.7,
            'max_tokens' => 1000,
            'top_p' => 0.9,
        ],
        'tool_calling_mode' => ToolCallingMode::None,
        'status' => BotStatus::Active,
        'visibility' => BotVisibility::PRIVATE,
        'bot_type' => BotType::PERSONAL,
        'is_shareable' => true,
        'metadata' => [
            'category' => 'testing',
            'tags' => ['widget', 'test', 'integration'],
            'version' => '1.0',
            'purpose' => 'widget_testing',
        ],
        'theme' => [
            'primaryColor' => '#3b82f6',
            'backgroundColor' => '#f9fafb',
            'textColor' => '#111827',
            'borderRadius' => '8px',
        ]
    ]);
    
    echo "✅ Test bot created successfully!\n\n";
    echo "📋 Bot Details:\n";
    echo "   Name: {$bot->name}\n";
    echo "   UUID: {$bot->uuid}\n";
    echo "   API Key: {$bot->api_key}\n";
    echo "   Status: {$bot->status->value}\n";
    echo "   Greeting: {$bot->greeting_message}\n\n";
    
    echo "🔗 Widget Integration Credentials:\n";
    echo "   Bot UUID: {$bot->uuid}\n";
    echo "   API Key: {$bot->api_key}\n";
    echo "   API Base URL: " . url('/api/v1/widget') . "\n\n";
    
    // Update test page with credentials
    updateTestPage($bot->uuid, $bot->api_key);
    
    echo "🎯 Next Steps:\n";
    echo "1. Open real-api-test.html in browser\n";
    echo "2. Credentials are auto-filled\n";
    echo "3. Click 'Test Connection' to verify\n";
    echo "4. Click 'Load Widget' to start chatting\n\n";
    
    echo "🌐 Test URLs:\n";
    echo "   Widget Test: file://" . __DIR__ . "/real-api-test.html\n";
    echo "   API Health: " . url('/api/v1/widget/health') . "\n";
    echo "   Bot Validate: " . url("/api/v1/widget/bot/{$bot->uuid}/validate") . "\n\n";
    
} catch (Exception $e) {
    echo "❌ Error creating test bot: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

function updateTestPage($botUuid, $apiKey) {
    $testPagePath = __DIR__ . '/real-api-test.html';
    
    if (!file_exists($testPagePath)) {
        echo "⚠️  Test page not found at: {$testPagePath}\n";
        return;
    }
    
    try {
        $content = file_get_contents($testPagePath);
        
        // Update the fillSampleConfig function with real credentials
        $newFillFunction = "
        function fillSampleConfig() {
            // Fill with REAL test bot credentials
            document.getElementById('botUuid').value = '{$botUuid}';
            document.getElementById('apiKey').value = '{$apiKey}';
            document.getElementById('apiBaseUrl').value = window.location.origin + '/api/v1/widget';
            document.getElementById('userId').value = 'widget_test_user_' + Date.now();
            
            showStatus('✅ Real test bot credentials loaded!', 'success');
        }";
        
        // Replace the existing fillSampleConfig function
        $pattern = '/function fillSampleConfig\(\) \{[^}]+\}/s';
        $content = preg_replace($pattern, trim($newFillFunction), $content);
        
        // Auto-fill credentials on page load
        $autoFillScript = "
        // Auto-fill real credentials on page load
        document.addEventListener('DOMContentLoaded', () => {
            fillSampleConfig();
        });";
        
        // Add auto-fill script before closing body tag
        $content = str_replace('</body>', $autoFillScript . "\n</body>", $content);
        
        file_put_contents($testPagePath, $content);
        echo "✅ Test page updated with real credentials\n";
        
    } catch (Exception $e) {
        echo "⚠️  Failed to update test page: " . $e->getMessage() . "\n";
    }
}

echo "🎉 Test bot setup complete!\n";
