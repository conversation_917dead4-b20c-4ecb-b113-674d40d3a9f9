<?php

namespace Modules\ModelAI\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Core\Traits\ResponseTrait;
use Modules\ModelAI\Http\Filters\ModelProviderFilter;
use Modules\ModelAI\Http\Requests\BulkModelProviderRequest;
use Modules\ModelAI\Http\Requests\BulkModelProviderDestroyRequest;
use Modules\ModelAI\Http\Requests\ModelProviderRequest;
use Modules\ModelAI\Models\ModelProvider;

class ProviderController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|model-provider.view')->only(['index', 'show', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|model-provider.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|model-provider.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|model-provider.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|model-provider.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $providers = ModelProvider::query()
            ->withCount(['modelAIs', 'modelAIs as active_models_count' => function ($query) {
                $query->where('status', 'active');
            }])
            ->filter(new ModelProviderFilter($request))
            ->orderBy('name')
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($providers, __('Providers retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ModelProviderRequest $request): JsonResponse
    {
        try {
            $provider = ModelProvider::create($request->all());
            return $this->successResponse($provider, __('Provider created successfully.'), 201);
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to create provider.'));
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        $provider = ModelProvider::withTrashed()->findOrFail($id);
        $provider->loadCount('modelAIs');
        return $this->successResponse($provider, __('Provider retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ModelProviderRequest $request, int $id): JsonResponse
    {
        try {
            $provider = ModelProvider::withTrashed()->findOrFail($id);
            $provider->update($request->all());
            return $this->successResponse($provider->fresh(), __('Provider updated successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to update provider.'));
        }
    }

    /**
     * Soft delete the specified resource.
     */
    public function delete(int $id): JsonResponse
    {
        try {
            $provider = ModelProvider::withTrashed()->findOrFail($id);

            // Check if provider has active models
            if ($provider->activeModelAIs()->exists()) {
                return $this->errorResponse(
                    null,
                    __('Cannot delete provider that has active models.'),
                    422
                );
            }

            $provider->delete();

            return $this->successResponse(null, __('Provider deleted successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to delete provider.'));
        }
    }

    /**
     * Restore the specified provider from trash.
     */
    public function restore(int $id): JsonResponse
    {
        try {
            $provider = ModelProvider::onlyTrashed()->findOrFail($id);
            $provider->restore();

            return $this->successResponse($provider->fresh(), __('Provider restored successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to restore provider.'));
        }
    }

    /**
     * Permanently delete the specified provider.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $provider = ModelProvider::withTrashed()->findOrFail($id);

            // Check if provider has any models (even soft deleted)
            if ($provider->modelAIs()->withTrashed()->exists()) {
                return $this->errorResponse(
                    null,
                    __('Cannot permanently delete provider that has models.'),
                    422
                );
            }

            $provider->forceDelete();

            return $this->successResponse(null, __('Provider permanently deleted successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to permanently delete provider.'));
        }
    }

    /**
     * Bulk soft delete providers.
     */
    public function bulkDelete(BulkModelProviderRequest $request): JsonResponse
    {
        $ids = $request->input('ids');
        $providers = ModelProvider::whereIn('id', $ids)->get();

        $deletedCount = 0;
        $errors = [];

        foreach ($providers as $provider) {
            if (!$provider->activeModelAIs()->exists()) {
                $provider->delete();
                $deletedCount++;
            } else {
                $errors[] = "Provider '{$provider->name}' cannot be deleted.";
            }
        }

        $message = __(':count providers deleted successfully.', ['count' => $deletedCount]);

        if (!empty($errors)) {
            $message .= ' ' . __('Some providers could not be deleted: :errors', ['errors' => implode(', ', $errors)]);
        }

        return $this->successResponse(['deleted_count' => $deletedCount, 'errors' => $errors], $message);
    }

    /**
     * Bulk restore providers.
     */
    public function bulkRestore(BulkModelProviderRequest $request): JsonResponse
    {
        $ids = $request->input('ids');
        $restoredCount = ModelProvider::onlyTrashed()->whereIn('id', $ids)->restore();

        return $this->successResponse(
            ['restored_count' => $restoredCount],
            __('Providers restored successfully.')
        );
    }

    /**
     * Bulk permanently delete providers.
     */
    public function bulkDestroy(BulkModelProviderDestroyRequest $request): JsonResponse
    {
        $ids = $request->input('ids');
        $providers = ModelProvider::withTrashed()->whereIn('id', $ids)->get();

        $deletedCount = 0;
        $errors = [];

        foreach ($providers as $provider) {
            if (!$provider->modelAIs()->withTrashed()->exists()) {
                $provider->forceDelete();
                $deletedCount++;
            } else {
                $errors[] = "Provider '{$provider->name}' cannot be permanently deleted.";
            }
        }

        $message = __(':count providers permanently deleted.', ['count' => $deletedCount]);

        if (!empty($errors)) {
            $message .= ' ' . __('Some providers could not be deleted: :errors', ['errors' => implode(', ', $errors)]);
        }

        return $this->successResponse(['deleted_count' => $deletedCount, 'errors' => $errors], $message);
    }

    /**
     * Get providers for dropdown.
     */
    public function dropdown(): JsonResponse
    {
        $providers = ModelProvider::query()->active()->orderBy('name')->get();
        return $this->successResponse(
            $providers->makeHidden(['updated_at', 'created_at', 'status']),
            __('Providers retrieved successfully.')
        );
    }
}
