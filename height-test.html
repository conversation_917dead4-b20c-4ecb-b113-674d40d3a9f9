<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Height Fix Test</title>
    <link rel="stylesheet" href="dist/widget/procms-chatbot.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f2f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .widget-container {
            width: 400px;
            height: 600px;
            border: 3px solid #e74c3c;
            border-radius: 8px;
            margin: 20px auto;
            position: relative;
            background: #fff5f5;
        }
        .widget-container::before {
            content: "Container: 400x600px";
            position: absolute;
            top: -25px;
            left: 0;
            font-size: 12px;
            color: #e74c3c;
            font-weight: bold;
        }
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        .btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            text-align: center;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 14px;
        }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.info { background: #dbeafe; color: #1e40af; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .height-info {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 13px;
        }
        .height-info h4 {
            margin: 0 0 10px 0;
            font-family: inherit;
            color: #374151;
        }
        .measurement {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        .measurement .label {
            color: #6b7280;
        }
        .measurement .value {
            color: #1f2937;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Height Fix Test</h1>
        <p>Testing that container height properly adjusts when widget is minimized.</p>

        <div class="test-section">
            <h3>📏 Container Height Behavior Test</h3>
            <p>The red border shows the container. Watch how it changes when minimizing:</p>
            
            <div class="controls">
                <button class="btn" onclick="loadWidget()">Load Widget</button>
                <button class="btn" onclick="minimizeWidget()" id="minimize-btn" disabled>Minimize</button>
                <button class="btn" onclick="maximizeWidget()" id="maximize-btn" disabled>Maximize</button>
                <button class="btn" onclick="toggleWidget()" id="toggle-btn" disabled>Toggle</button>
                <button class="btn" onclick="measureHeight()">Measure Height</button>
                <button class="btn" onclick="clearWidget()">Clear</button>
            </div>
            
            <div id="widget-container" class="widget-container"></div>
            
            <div id="height-info" class="height-info">
                <h4>📐 Height Measurements:</h4>
                <div class="measurement">
                    <span class="label">Container Height:</span>
                    <span class="value" id="container-height">Not measured</span>
                </div>
                <div class="measurement">
                    <span class="label">Widget Height:</span>
                    <span class="value" id="widget-height">Not measured</span>
                </div>
                <div class="measurement">
                    <span class="label">Widget State:</span>
                    <span class="value" id="widget-state">No widget</span>
                </div>
                <div class="measurement">
                    <span class="label">Expected Behavior:</span>
                    <span class="value">Minimized = 60px, Maximized = 600px</span>
                </div>
            </div>
            
            <div id="status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>✅ Expected Behavior:</h3>
            <ul>
                <li><strong>Load Widget:</strong> Container shows 600px height (red border)</li>
                <li><strong>Minimize:</strong> Container shrinks to 60px height (header only)</li>
                <li><strong>Maximize:</strong> Container restores to 600px height</li>
                <li><strong>Clear:</strong> Container returns to original 600px height</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🐛 Previous Bug:</h3>
            <p>Before the fix, the container would keep its 600px height even when minimized, 
            leaving empty space below the header. Now it should properly shrink to 60px.</p>
        </div>
    </div>

    <script src="dist/widget/procms-chatbot.umd.js"></script>
    <script>
        let widget = null;

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            console.log(`[Height Test] ${message}`);
        }

        function updateButtons() {
            const hasWidget = widget && widget.isMounted();
            document.getElementById('minimize-btn').disabled = !hasWidget;
            document.getElementById('maximize-btn').disabled = !hasWidget;
            document.getElementById('toggle-btn').disabled = !hasWidget;
        }

        function measureHeight() {
            const container = document.getElementById('widget-container');
            const widgetEl = container.querySelector('.procms-chatbot-widget');
            
            const containerHeight = container.offsetHeight;
            const widgetHeight = widgetEl ? widgetEl.offsetHeight : 0;
            const widgetState = widget ? (widget.isMinimized() ? 'Minimized' : 'Maximized') : 'No widget';
            
            document.getElementById('container-height').textContent = containerHeight + 'px';
            document.getElementById('widget-height').textContent = widgetHeight + 'px';
            document.getElementById('widget-state').textContent = widgetState;
            
            console.log('Height measurements:', {
                container: containerHeight,
                widget: widgetHeight,
                state: widgetState
            });
            
            // Validate expected behavior
            if (widget && widget.isMinimized()) {
                if (containerHeight === 60) {
                    showStatus('✅ PASS: Container height is 60px when minimized', 'success');
                } else {
                    showStatus(`❌ FAIL: Container height is ${containerHeight}px, expected 60px`, 'error');
                }
            } else if (widget && !widget.isMinimized()) {
                if (containerHeight === 600) {
                    showStatus('✅ PASS: Container height is 600px when maximized', 'success');
                } else {
                    showStatus(`⚠️ INFO: Container height is ${containerHeight}px when maximized`, 'info');
                }
            }
        }

        async function loadWidget() {
            try {
                if (widget) {
                    widget.unmount();
                }

                showStatus('Loading widget...', 'info');

                widget = await ProcmsChatbot.create({
                    botUuid: 'height-test-bot-123',
                    apiKey: 'pk_test_height_test_key_12345678901234567890',
                    theme: 'light',
                    onReady: () => {
                        showStatus('Widget loaded! Test minimize/maximize.', 'success');
                        updateButtons();
                        setTimeout(measureHeight, 100);
                    },
                    onMinimized: () => {
                        showStatus('Widget minimized - measuring height...', 'info');
                        setTimeout(measureHeight, 100);
                    },
                    onMaximized: () => {
                        showStatus('Widget maximized - measuring height...', 'info');
                        setTimeout(measureHeight, 100);
                    },
                    onCloseRequested: () => {
                        showStatus('Close requested - clearing widget...', 'info');
                        setTimeout(clearWidget, 500);
                    }
                }, '#widget-container');

            } catch (error) {
                showStatus(`Error: ${error.message}`, 'error');
                console.error('Widget load error:', error);
            }
        }

        function minimizeWidget() {
            if (widget) {
                widget.minimize();
                showStatus('Minimize command sent', 'info');
            }
        }

        function maximizeWidget() {
            if (widget) {
                widget.maximize();
                showStatus('Maximize command sent', 'info');
            }
        }

        function toggleWidget() {
            if (widget) {
                const wasMinimized = widget.isMinimized();
                widget.toggle();
                showStatus(`Toggle: ${wasMinimized ? 'maximizing' : 'minimizing'}`, 'info');
            }
        }

        function clearWidget() {
            if (widget) {
                widget.unmount();
                widget = null;
                showStatus('Widget cleared', 'info');
                updateButtons();
                setTimeout(measureHeight, 100);
            }
        }

        // Auto-measure height every 2 seconds
        setInterval(() => {
            if (widget && widget.isMounted()) {
                measureHeight();
            }
        }, 2000);

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Height Fix Test loaded');
            updateButtons();
            measureHeight();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'l':
                        e.preventDefault();
                        loadWidget();
                        break;
                    case 'm':
                        e.preventDefault();
                        if (widget) toggleWidget();
                        break;
                    case 'h':
                        e.preventDefault();
                        measureHeight();
                        break;
                }
            }
        });

        console.log('Keyboard shortcuts:');
        console.log('  Ctrl/Cmd + L: Load widget');
        console.log('  Ctrl/Cmd + M: Toggle minimize');
        console.log('  Ctrl/Cmd + H: Measure height');
    </script>
</body>
</html>
