<?php

namespace Modules\Location\Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Modules\Location\Models\Country;
use Modules\Location\Models\GeoDivision;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use PHPUnit\Framework\Attributes\Test;

class GeoDivisionApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Country $country;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations for Location module
        $this->artisan('migrate', ['--path' => 'Modules/Location/database/migrations']);
        
        // Create and authenticate user
        $this->user = User::factory()->create();
        Sanctum::actingAs($this->user);
        
        // Create a test country
        $this->country = Country::factory()->create();
    }

    #[Test]
    public function it_requires_authentication_for_admin_endpoints()
    {
        Sanctum::actingAs(null);

        $response = $this->getJson('/api/v1/geo-divisions');

        $response->assertStatus(401);
    }

    #[Test]
    public function it_can_list_geo_divisions_with_pagination()
    {
        GeoDivision::factory()->count(15)->forCountry($this->country->id)->create();

        $response = $this->getJson('/api/v1/geo-divisions?limit=10');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'native_name',
                            'type',
                            'country_id',
                            'level',
                            'status',
                            'country',
                            'parent'
                        ]
                    ],
                    'current_page',
                    'last_page',
                    'per_page',
                    'total'
                ]
            ]);

        $this->assertCount(10, $response->json('data.data'));
        $this->assertEquals(15, $response->json('data.total'));
    }

    #[Test]
    public function it_can_create_a_geo_division()
    {
        $divisionData = [
            'code' => 'CA',
            'name' => 'California',
            'native_name' => 'California',
            'type' => 'state',
            'country_id' => $this->country->id,
            'latitude' => 36.7783,
            'longitude' => -119.4179,
            'postal_code' => '90210',
            'sort_order' => 1,
            'status' => 'active'
        ];

        $response = $this->postJson('/api/v1/geo-divisions', $divisionData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'type',
                    'country_id',
                    'level',
                    'status'
                ]
            ]);

        $this->assertDatabaseHas('geo_divisions', [
            'name' => 'California',
            'type' => 'state',
            'country_id' => $this->country->id
        ]);
    }

    #[Test]
    public function it_validates_required_fields_when_creating_geo_division()
    {
        $response = $this->postJson('/api/v1/geo-divisions', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'type', 'country_id', 'status']);
    }

    #[Test]
    public function it_validates_country_exists_when_creating_geo_division()
    {
        $response = $this->postJson('/api/v1/geo-divisions', [
            'name' => 'Test Division',
            'type' => 'state',
            'country_id' => 999,

            'status' => 'active'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['country_id']);
    }

    #[Test]
    public function it_validates_parent_exists_when_creating_geo_division()
    {
        $response = $this->postJson('/api/v1/geo-divisions', [
            'name' => 'Test City',
            'type' => 'city',
            'country_id' => $this->country->id,
            'parent_id' => 999,

            'status' => 'active'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['parent_id']);
    }

    #[Test]
    public function it_automatically_sets_level_and_path_when_creating_geo_division()
    {
        // Create parent state
        $state = GeoDivision::factory()->state()->forCountry($this->country->id)->create();

        $response = $this->postJson('/api/v1/geo-divisions', [
            'name' => 'Los Angeles',
            'type' => 'city',
            'country_id' => $this->country->id,
            'parent_id' => $state->id,

            'status' => 'active'
        ]);

        $response->assertStatus(201);

        $city = GeoDivision::find($response->json('data.id'));
        $this->assertEquals(2, $city->level);
        $this->assertEquals($state->id, $city->path);
    }

    #[Test]
    public function it_can_show_a_geo_division()
    {
        $division = GeoDivision::factory()->forCountry($this->country->id)->create();

        $response = $this->getJson("/api/v1/geo-divisions/{$division->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'type',
                    'country_id',
                    'level',
                    'status',
                    'country',
                    'parent',
                    'children'
                ]
            ]);

        $this->assertEquals($division->id, $response->json('data.id'));
    }

    #[Test]
    public function it_returns_404_for_non_existent_geo_division()
    {
        $response = $this->getJson('/api/v1/geo-divisions/999');

        $response->assertStatus(404);
    }

    #[Test]
    public function it_can_update_a_geo_division()
    {
        $division = GeoDivision::factory()->forCountry($this->country->id)->create();

        $updateData = [
            'name' => 'Updated Division Name',
            'native_name' => 'Updated Native Name',
            'population' => 1000000,
            'status' => 'inactive'
        ];

        $response = $this->putJson("/api/v1/geo-divisions/{$division->id}", $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'population',
                    'status'
                ]
            ]);

        $this->assertDatabaseHas('geo_divisions', [
            'id' => $division->id,
            'name' => 'Updated Division Name',
            'population' => 1000000,
            'status' => 'inactive'
        ]);
    }

    #[Test]
    public function it_can_soft_delete_a_geo_division()
    {
        $division = GeoDivision::factory()->forCountry($this->country->id)->create();

        $response = $this->deleteJson("/api/v1/geo-divisions/{$division->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => $division->id
            ]);

        $this->assertSoftDeleted('geo_divisions', ['id' => $division->id]);
    }

    #[Test]
    public function it_can_restore_a_soft_deleted_geo_division()
    {
        $division = GeoDivision::factory()->forCountry($this->country->id)->create();
        $division->delete();

        $response = $this->patchJson("/api/v1/geo-divisions/{$division->id}/restore");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'deleted_at'
                ]
            ]);

        $this->assertDatabaseHas('geo_divisions', [
            'id' => $division->id,
            'deleted_at' => null
        ]);
    }

    #[Test]
    public function it_can_force_delete_a_geo_division()
    {
        $division = GeoDivision::factory()->forCountry($this->country->id)->create();
        $division->delete();

        $response = $this->deleteJson("/api/v1/geo-divisions/{$division->id}/force-delete");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => $division->id
            ]);

        $this->assertDatabaseMissing('geo_divisions', ['id' => $division->id]);
    }

    #[Test]
    public function it_can_list_trashed_geo_divisions()
    {
        $activeDivision = GeoDivision::factory()->forCountry($this->country->id)->create();
        $trashedDivision = GeoDivision::factory()->forCountry($this->country->id)->create();
        $trashedDivision->delete();

        $response = $this->getJson('/api/v1/geo-divisions/trashed/list');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'deleted_at'
                        ]
                    ]
                ]
            ]);

        $this->assertCount(1, $response->json('data.data'));
        $this->assertEquals($trashedDivision->id, $response->json('data.data.0.id'));
    }

    #[Test]
    public function it_can_bulk_delete_geo_divisions()
    {
        $divisions = GeoDivision::factory()->count(3)->forCountry($this->country->id)->create();
        $ids = $divisions->pluck('id')->toArray();

        $response = $this->postJson('/api/v1/geo-divisions/bulk-delete', [
            'ids' => $ids
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'deleted_count'
                ]
            ]);

        $this->assertEquals(3, $response->json('data.deleted_count'));
        
        foreach ($ids as $id) {
            $this->assertSoftDeleted('geo_divisions', ['id' => $id]);
        }
    }

    #[Test]
    public function it_can_bulk_restore_geo_divisions()
    {
        $divisions = GeoDivision::factory()->count(3)->forCountry($this->country->id)->create();
        $divisions->each->delete();
        $ids = $divisions->pluck('id')->toArray();

        $response = $this->postJson('/api/v1/geo-divisions/bulk-restore', [
            'ids' => $ids
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'restored_count'
                ]
            ]);

        $this->assertEquals(3, $response->json('data.restored_count'));
        
        foreach ($ids as $id) {
            $this->assertDatabaseHas('geo_divisions', [
                'id' => $id,
                'deleted_at' => null
            ]);
        }
    }

    #[Test]
    public function it_can_bulk_force_delete_geo_divisions()
    {
        $divisions = GeoDivision::factory()->count(3)->forCountry($this->country->id)->create();
        $divisions->each->delete();
        $ids = $divisions->pluck('id')->toArray();

        $response = $this->postJson('/api/v1/geo-divisions/bulk-force-delete', [
            'ids' => $ids
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'deleted_count'
                ]
            ]);

        $this->assertEquals(3, $response->json('data.deleted_count'));
        
        foreach ($ids as $id) {
            $this->assertDatabaseMissing('geo_divisions', ['id' => $id]);
        }
    }

    #[Test]
    public function it_can_get_geo_divisions_dropdown()
    {
        $state = GeoDivision::factory()->state()->active()->forCountry($this->country->id)->create();
        GeoDivision::factory()->city()->active()->forCountry($this->country->id)->create();

        $response = $this->getJson('/api/v1/geo-divisions/dropdown/list?type=state');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'native_name',
                        'code',
                        'type'
                    ]
                ]
            ]);

        $this->assertCount(1, $response->json('data'));
        $this->assertEquals('state', $response->json('data.0.type'));
    }

    #[Test]
    public function it_validates_dropdown_type_parameter()
    {
        $response = $this->getJson('/api/v1/geo-divisions/dropdown/list');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['type']);
    }

    #[Test]
    public function it_can_filter_geo_divisions_by_type()
    {
        GeoDivision::factory()->state()->forCountry($this->country->id)->create();
        GeoDivision::factory()->city()->forCountry($this->country->id)->create();

        $response = $this->getJson('/api/v1/geo-divisions?type=state');

        $response->assertStatus(200);
        
        $divisions = $response->json('data.data');
        $this->assertCount(1, $divisions);
        $this->assertEquals('state', $divisions[0]['type']);
    }

    #[Test]
    public function it_can_filter_geo_divisions_by_country()
    {
        $otherCountry = Country::factory()->create();
        
        GeoDivision::factory()->forCountry($this->country->id)->create();
        GeoDivision::factory()->forCountry($otherCountry->id)->create();

        $response = $this->getJson("/api/v1/geo-divisions?country_id={$this->country->id}");

        $response->assertStatus(200);
        
        $divisions = $response->json('data.data');
        $this->assertCount(1, $divisions);
        $this->assertEquals($this->country->id, $divisions[0]['country_id']);
    }

    #[Test]
    public function it_can_filter_geo_divisions_by_level()
    {
        $state = GeoDivision::factory()->state()->forCountry($this->country->id)->create();
        GeoDivision::factory()->city()->forCountry($this->country->id)->childOf($state->id)->create();

        $response = $this->getJson('/api/v1/geo-divisions?level=1');

        $response->assertStatus(200);
        
        $divisions = $response->json('data.data');
        $this->assertCount(1, $divisions);
        $this->assertEquals(1, $divisions[0]['level']);
    }

    #[Test]
    public function it_can_filter_geo_divisions_by_parent()
    {
        $state = GeoDivision::factory()->state()->forCountry($this->country->id)->create();
        GeoDivision::factory()->city()->forCountry($this->country->id)->childOf($state->id)->create();
        GeoDivision::factory()->city()->forCountry($this->country->id)->create(); // No parent

        $response = $this->getJson("/api/v1/geo-divisions?parent_id={$state->id}");

        $response->assertStatus(200);
        
        $divisions = $response->json('data.data');
        $this->assertCount(1, $divisions);
        $this->assertEquals($state->id, $divisions[0]['parent_id']);
    }

    #[Test]
    public function it_validates_coordinates_when_creating_geo_division()
    {
        $response = $this->postJson('/api/v1/geo-divisions', [
            'name' => 'Test Division',
            'type' => 'state',
            'country_id' => $this->country->id,
            'latitude' => 91, // Invalid latitude
            'longitude' => 181, // Invalid longitude
            'is_capital' => false,
            'status' => 'active'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['latitude', 'longitude']);
    }



    #[Test]
    public function it_validates_type_values_when_creating_geo_division()
    {
        $response = $this->postJson('/api/v1/geo-divisions', [
            'name' => 'Test Division',
            'type' => 'invalid_type',
            'country_id' => $this->country->id,
            'is_capital' => false,
            'status' => 'active'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['type']);
    }

    #[Test]
    public function it_can_search_geo_divisions_by_name()
    {
        GeoDivision::factory()->forCountry($this->country->id)->create(['name' => 'California']);
        GeoDivision::factory()->forCountry($this->country->id)->create(['name' => 'Texas']);

        $response = $this->getJson('/api/v1/geo-divisions?name=California');

        $response->assertStatus(200);
        
        $divisions = $response->json('data.data');
        $this->assertCount(1, $divisions);
        $this->assertEquals('California', $divisions[0]['name']);
    }


}
