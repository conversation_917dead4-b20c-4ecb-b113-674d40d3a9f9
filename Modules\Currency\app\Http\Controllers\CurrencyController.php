<?php

namespace Modules\Currency\Http\Controllers;

use Modules\Core\Traits\ResponseTrait;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Currency\Facades\CurrencyFacade;

class CurrencyController extends Controller
{
    use ResponseTrait;

    public function index(Request $request): JsonResponse
    {
        return $this->successResponse(
            CurrencyFacade::getActiveCurrencies(),
            __('Currencies retrieved successfully.')
        );
    }

    public function dropdown(): JsonResponse
    {
        return $this->successResponse(
            CurrencyFacade::getCurrenciesForDropdown(),
            __('Currency dropdown data retrieved successfully.')
        );
    }

    public function default(): JsonResponse
    {
        return $this->successResponse(
            CurrencyFacade::getDefaultCurrency(),
            __('Default currency retrieved successfully.')
        );
    }

    public function show(string $code): JsonResponse
    {
        return $this->successResponse(CurrencyFacade::findByCode(strtoupper($code)));
    }

    public function convert(Request $request): JsonResponse
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
            'from' => 'required|string|size:3|exists:currencies,code',
            'to' => 'required|string|size:3|exists:currencies,code',
        ]);

        $amount = $request->amount;
        $fromCurrency = strtoupper($request->from);
        $toCurrency = strtoupper($request->to);

        $convertedAmount = CurrencyFacade::convertAmount($amount, $fromCurrency, $toCurrency);

        if ($convertedAmount === null) {
            return $this->errorResponse('Exchange rate not available for this currency pair.', 400);
        }

        $exchangeRate = CurrencyFacade::getExchangeRate($fromCurrency, $toCurrency);

        return $this->successResponse([
            'original_amount' => $amount,
            'converted_amount' => $convertedAmount,
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
            'exchange_rate' => $exchangeRate,
            'formatted_original' => CurrencyFacade::formatAmount($amount, $fromCurrency),
            'formatted_converted' => CurrencyFacade::formatAmount($convertedAmount, $toCurrency),
        ], __('Amount converted successfully.'));
    }

    public function exchangeRate(Request $request): JsonResponse
    {
        $request->validate([
            'from' => 'required|string|size:3|exists:currencies,code',
            'to' => 'required|string|size:3|exists:currencies,code',
        ]);

        $fromCurrency = strtoupper($request->from);
        $toCurrency = strtoupper($request->to);

        $rate = CurrencyFacade::getExchangeRate($fromCurrency, $toCurrency);

        if ($rate === null) {
            return $this->notFoundResponse(null, __('Exchange rate not available for this currency pair.'));
        }

        return $this->successResponse([
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
            'rate' => $rate,
            'formatted_rate' => "1 {$fromCurrency} = {$rate} {$toCurrency}",
        ], __('Exchange rate retrieved successfully.'));
    }

    public function format(Request $request): JsonResponse
    {
        $request->validate([
            'amount' => 'required|numeric',
            'currency' => 'required|string|size:3|exists:currencies,code',
        ]);

        $amount = $request->amount;
        $currencyCode = strtoupper($request->currency);

        return $this->successResponse([
            'original_amount' => $amount,
            'currency_code' => $currencyCode,
            'formatted_amount' => CurrencyFacade::formatAmount($amount, $currencyCode),
        ], __('Amount formatted successfully.'));
    }

    public function codes(): JsonResponse
    {
        return $this->successResponse(
            CurrencyFacade::getSupportedCurrencyCodes(),
            __('Supported currency codes retrieved successfully.')
        );
    }
}
