<?php

namespace Modules\Notification\Facades;

use Illuminate\Support\Facades\Facade;
use Modules\Notification\Services\NotificationService;

/**
 * @method static void trigger(string $eventKey, array $data = [])
 *
 * @see NotificationService
 */
class NotificationFacade extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return 'notification.service';
    }
}
