const Layout = () => import("@/layout/index.vue");

export default {
  path: "/notifications/management",
  name: "Notification",
  component: Layout,
  redirect: "/notifications",
  meta: {
    icon: "ri:notification-line",
    title: "Notifications",
    rank: 14
  },
  children: [
    {
      path: "/notifications",
      name: "NotificationIndex",
      component: () => import("@/views/notification/index.vue"),
      meta: {
        icon: "ri:notification-line",
        title: "Notifications",
        showLink: true,
        auths: ["notification:list"]
      }
    }
  ]
} satisfies RouteConfigsTable;
