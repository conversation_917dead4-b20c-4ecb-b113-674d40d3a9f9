<?php

namespace Modules\Location\Tests\Unit;

use Tests\TestCase;
use Modules\Location\Models\Country;
use Modules\Location\Models\GeoDivision;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\Test;

class CountryModelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations for Location module
        $this->artisan('migrate', ['--path' => 'Modules/Location/database/migrations']);
    }

    #[Test]
    public function it_has_correct_fillable_attributes()
    {
        $country = new Country();

        $expectedFillable = [
            'name',
            'native_name',
            'iso_code_2',
            'iso_code_3',
            'iso_numeric',
            'phone_code',
            'region',
            'subregion',
            'latitude',
            'longitude',
            'emoji',
            'emoji_unicode',
            'status',
        ];

        $this->assertEquals($expectedFillable, $country->getFillable());
    }

    #[Test]
    public function it_has_correct_casts()
    {
        $country = new Country();

        $expectedCasts = [
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];

        foreach ($expectedCasts as $attribute => $cast) {
            $this->assertEquals($cast, $country->getCasts()[$attribute]);
        }
    }

    #[Test]
    public function it_has_correct_hidden_attributes()
    {
        $country = new Country();

        $expectedHidden = ['deleted_at'];

        $this->assertEquals($expectedHidden, $country->getHidden());
    }

    #[Test]
    public function it_can_create_a_country()
    {
        $countryData = [
            'name' => 'United States',
            'native_name' => 'United States',
            'iso_code_2' => 'US',
            'iso_code_3' => 'USA',
            'iso_numeric' => '840',
            'phone_code' => '+1',
            'region' => 'Americas',
            'subregion' => 'Northern America',
            'latitude' => 37.0902,
            'longitude' => -95.7129,
            'emoji' => '🇺🇸',
            'emoji_unicode' => 'U+1F1FA U+1F1F8',
            'status' => 'active'
        ];

        $country = Country::create($countryData);

        $this->assertInstanceOf(Country::class, $country);
        $this->assertDatabaseHas('countries', $countryData);
        $this->assertEquals('US', $country->iso_code_2);
        $this->assertEquals('United States', $country->name);
        $this->assertEquals('active', $country->status);
    }

    #[Test]
    public function it_can_scope_active_countries()
    {
        Country::factory()->create(['status' => 'active']);
        Country::factory()->create(['status' => 'inactive']);

        $activeCountries = Country::active()->get();

        $this->assertCount(1, $activeCountries);
        $this->assertEquals('active', $activeCountries->first()->status);
    }

    #[Test]
    public function it_can_scope_by_status()
    {
        Country::factory()->create(['status' => 'active']);
        Country::factory()->create(['status' => 'inactive']);

        $activeCountries = Country::status('active')->get();
        $inactiveCountries = Country::status('inactive')->get();

        $this->assertCount(1, $activeCountries);
        $this->assertCount(1, $inactiveCountries);
        $this->assertEquals('active', $activeCountries->first()->status);
        $this->assertEquals('inactive', $inactiveCountries->first()->status);
    }

    #[Test]
    public function it_can_scope_by_region()
    {
        Country::factory()->create(['region' => 'Asia']);
        Country::factory()->create(['region' => 'Europe']);

        $asianCountries = Country::region('Asia')->get();
        $europeanCountries = Country::region('Europe')->get();

        $this->assertCount(1, $asianCountries);
        $this->assertCount(1, $europeanCountries);
        $this->assertEquals('Asia', $asianCountries->first()->region);
        $this->assertEquals('Europe', $europeanCountries->first()->region);
    }

    #[Test]
    public function it_has_geo_divisions_relationship()
    {
        $country = Country::factory()->create();
        $division = GeoDivision::factory()->forCountry($country->id)->create();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\HasMany::class, $country->geoDivisions());
        $this->assertTrue($country->geoDivisions->contains($division));
    }

    #[Test]
    public function it_has_states_relationship()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();

        $states = $country->states;

        $this->assertTrue($states->contains($state));
        $this->assertFalse($states->contains($city));
    }

    #[Test]
    public function it_has_cities_relationship()
    {
        $country = Country::factory()->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();

        $cities = $country->cities;

        $this->assertTrue($cities->contains($city));
        $this->assertFalse($cities->contains($state));
    }

    #[Test]
    public function it_has_districts_relationship()
    {
        $country = Country::factory()->create();
        $district = GeoDivision::factory()->district()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();

        $districts = $country->districts;

        $this->assertTrue($districts->contains($district));
        $this->assertFalse($districts->contains($city));
    }



    #[Test]
    public function it_uses_iso_code_2_as_route_key()
    {
        $country = new Country();

        $this->assertEquals('iso_code_2', $country->getRouteKeyName());
    }

    #[Test]
    public function it_has_correct_table_structure()
    {
        $this->assertTrue(Schema::hasTable('countries'));

        $expectedColumns = [
            'id', 'name', 'native_name', 'iso_code_2', 'iso_code_3',
            'iso_numeric', 'phone_code', 'region', 'subregion',
            'latitude', 'longitude', 'emoji', 'emoji_unicode', 'status',
            'created_at', 'updated_at', 'deleted_at'
        ];

        foreach ($expectedColumns as $column) {
            $this->assertTrue(Schema::hasColumn('countries', $column));
        }
    }

    #[Test]
    public function it_has_factory()
    {
        $country = Country::factory()->make();

        $this->assertInstanceOf(Country::class, $country);
        $this->assertNotEmpty($country->name);
        $this->assertNotEmpty($country->iso_code_2);
    }

    #[Test]
    public function factory_can_create_active_country()
    {
        $country = Country::factory()->active()->create();

        $this->assertEquals('active', $country->status);
    }

    #[Test]
    public function factory_can_create_inactive_country()
    {
        $country = Country::factory()->inactive()->create();

        $this->assertEquals('inactive', $country->status);
    }

    #[Test]
    public function factory_can_create_country_by_region()
    {
        $country = Country::factory()->region('Asia')->create();

        $this->assertEquals('Asia', $country->region);
    }

    #[Test]
    public function it_validates_iso_code_2_length()
    {
        $country = Country::factory()->create(['iso_code_2' => 'US']);
        $this->assertEquals(2, strlen($country->iso_code_2));
    }

    #[Test]
    public function it_validates_iso_code_3_length()
    {
        $country = Country::factory()->create(['iso_code_3' => 'USA']);
        $this->assertEquals(3, strlen($country->iso_code_3));
    }

    #[Test]
    public function it_validates_status_values()
    {
        $activeCountry = Country::factory()->create(['status' => 'active']);
        $this->assertEquals('active', $activeCountry->status);

        $inactiveCountry = Country::factory()->create(['status' => 'inactive']);
        $this->assertEquals('inactive', $inactiveCountry->status);
    }

    #[Test]
    public function it_can_soft_delete()
    {
        $country = Country::factory()->create();
        $countryId = $country->id;

        $country->delete();

        $this->assertSoftDeleted('countries', ['id' => $countryId]);
        $this->assertCount(0, Country::all());
        $this->assertCount(1, Country::withTrashed()->get());
    }

    #[Test]
    public function it_can_restore_soft_deleted()
    {
        $country = Country::factory()->create();
        $country->delete();

        $country->restore();

        $this->assertCount(1, Country::all());
        $this->assertNull($country->fresh()->deleted_at);
    }
}
