<?php

namespace Modules\ModelAI\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class BulkModelAIRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                'exists:model_ai,id',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('AI Model IDs'),
            'ids.*' => __('AI Model ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one AI model.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one AI model.'),
            'ids.*.required' => __('AI Model ID is required.'),
            'ids.*.integer' => __('AI Model ID must be an integer.'),
            'ids.*.exists' => __('One or more selected AI models do not exist.'),
        ];
    }
}
