<?php

namespace Modules\Notification\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Notification\Models\NotificationPreference;
use Modules\Notification\Models\NotificationType;
use App\Models\User;

class NotificationPreferenceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = NotificationPreference::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $channels = ['database', 'email', 'sms', 'push', 'telegram'];
        $timezones = ['UTC', 'America/New_York', 'Europe/London', 'Asia/Tokyo', 'Australia/Sydney'];
        
        $hasQuietHours = $this->faker->boolean(30); // 30% chance of having quiet hours
        
        return [
            'notifiable_type' => User::class,
            'notifiable_id' => User::factory(),
            'notification_type_id' => NotificationType::factory(),
            'channel' => $this->faker->randomElement($channels),
            'enabled' => $this->faker->boolean(85), // 85% chance of being enabled
            'settings' => $this->generateSettings(),
            'quiet_hours_start' => $hasQuietHours ? $this->faker->time('H:i') : null,
            'quiet_hours_end' => $hasQuietHours ? $this->faker->time('H:i') : null,
            'timezone' => $hasQuietHours ? $this->faker->randomElement($timezones) : null,
        ];
    }

    /**
     * Generate settings array.
     */
    protected function generateSettings(): array
    {
        return [
            'frequency' => $this->faker->randomElement(['immediate', 'daily', 'weekly']),
            'digest' => $this->faker->boolean(20), // 20% chance of digest mode
            'sound_enabled' => $this->faker->boolean(70),
            'vibration_enabled' => $this->faker->boolean(60),
        ];
    }

    /**
     * Indicate that the preference is disabled.
     */
    public function disabled(): static
    {
        return $this->state(fn (array $attributes) => [
            'enabled' => false,
        ]);
    }

    /**
     * Indicate that the preference is enabled.
     */
    public function enabled(): static
    {
        return $this->state(fn (array $attributes) => [
            'enabled' => true,
        ]);
    }

    /**
     * Create preference for email channel.
     */
    public function email(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'email',
            'settings' => [
                'frequency' => 'immediate',
                'digest' => $this->faker->boolean(30),
                'html_format' => true,
            ],
        ]);
    }

    /**
     * Create preference for database channel.
     */
    public function database(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'database',
            'settings' => [
                'mark_as_read_after' => $this->faker->numberBetween(1, 30), // days
                'auto_delete_after' => $this->faker->numberBetween(30, 365), // days
            ],
        ]);
    }

    /**
     * Create preference for SMS channel.
     */
    public function sms(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'sms',
            'settings' => [
                'frequency' => $this->faker->randomElement(['immediate', 'daily']),
                'short_format' => true,
            ],
        ]);
    }

    /**
     * Create preference for push channel.
     */
    public function push(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'push',
            'settings' => [
                'sound_enabled' => $this->faker->boolean(70),
                'vibration_enabled' => $this->faker->boolean(60),
                'badge_enabled' => $this->faker->boolean(80),
                'show_preview' => $this->faker->boolean(50),
            ],
        ]);
    }

    /**
     * Create preference for Telegram channel.
     */
    public function telegram(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => 'telegram',
            'settings' => [
                'disable_notification' => $this->faker->boolean(20),
                'parse_mode' => 'HTML',
            ],
        ]);
    }

    /**
     * Create preference with quiet hours.
     */
    public function withQuietHours(string $start = '22:00', string $end = '08:00', string $timezone = 'UTC'): static
    {
        return $this->state(fn (array $attributes) => [
            'quiet_hours_start' => $start,
            'quiet_hours_end' => $end,
            'timezone' => $timezone,
        ]);
    }

    /**
     * Create preference without quiet hours.
     */
    public function withoutQuietHours(): static
    {
        return $this->state(fn (array $attributes) => [
            'quiet_hours_start' => null,
            'quiet_hours_end' => null,
            'timezone' => null,
        ]);
    }

    /**
     * Create preference for specific user.
     */
    public function forUser($user): static
    {
        return $this->state(fn (array $attributes) => [
            'notifiable_type' => get_class($user),
            'notifiable_id' => $user->id,
        ]);
    }

    /**
     * Create preference for specific notification type.
     */
    public function forType($notificationType): static
    {
        $typeId = is_object($notificationType) ? $notificationType->id : $notificationType;
        
        return $this->state(fn (array $attributes) => [
            'notification_type_id' => $typeId,
        ]);
    }

    /**
     * Create preference with digest mode enabled.
     */
    public function withDigest(string $frequency = 'daily'): static
    {
        return $this->state(fn (array $attributes) => [
            'settings' => array_merge($attributes['settings'] ?? [], [
                'digest' => true,
                'frequency' => $frequency,
            ]),
        ]);
    }

    /**
     * Create preference with immediate notifications.
     */
    public function immediate(): static
    {
        return $this->state(fn (array $attributes) => [
            'settings' => array_merge($attributes['settings'] ?? [], [
                'frequency' => 'immediate',
                'digest' => false,
            ]),
        ]);
    }
}
