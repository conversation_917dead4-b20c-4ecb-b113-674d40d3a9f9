<?php

namespace Modules\ModelAI\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

class ModelAIPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedModelAIPermissions();
    }

    /**
     * Seed model AI module permissions.
     */
    private function seedModelAIPermissions(): void
    {
        // Model AI permissions - following Language module pattern
        $modelAIPermissions = [
            [
                'name' => 'model-ai.view',
                'display_name' => 'View AI Models',
                'description' => 'Permission to view AI models list and details',
                'module_name' => 'model-ai',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-ai.create',
                'display_name' => 'Create AI Models',
                'description' => 'Permission to create new AI models',
                'module_name' => 'model-ai',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-ai.edit',
                'display_name' => 'Edit AI Models',
                'description' => 'Permission to update existing AI models and set default model',
                'module_name' => 'model-ai',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-ai.delete',
                'display_name' => 'Delete AI Models',
                'description' => 'Permission to soft delete and restore AI models',
                'module_name' => 'model-ai',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-ai.destroy',
                'display_name' => 'Destroy AI Models',
                'description' => 'Permission to permanently delete AI models',
                'module_name' => 'model-ai',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
        ];

        // Model Provider permissions
        $modelProviderPermissions = [
            [
                'name' => 'model-provider.view',
                'display_name' => 'View Model Providers',
                'description' => 'Permission to view model providers list and details',
                'module_name' => 'model-ai',
                'sort_order' => 6,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-provider.create',
                'display_name' => 'Create Model Providers',
                'description' => 'Permission to create new model providers',
                'module_name' => 'model-ai',
                'sort_order' => 7,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-provider.edit',
                'display_name' => 'Edit Model Providers',
                'description' => 'Permission to update existing model providers',
                'module_name' => 'model-ai',
                'sort_order' => 8,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-provider.delete',
                'display_name' => 'Delete Model Providers',
                'description' => 'Permission to soft delete and restore model providers',
                'module_name' => 'model-ai',
                'sort_order' => 9,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-provider.destroy',
                'display_name' => 'Destroy Model Providers',
                'description' => 'Permission to permanently delete model providers',
                'module_name' => 'model-ai',
                'sort_order' => 10,
                'guard_name' => 'api',
            ],
        ];

        // Model Category permissions
        $modelCategoryPermissions = [
            [
                'name' => 'model-category.view',
                'display_name' => 'View Model Categories',
                'description' => 'Permission to view model categories list and details',
                'module_name' => 'model-ai',
                'sort_order' => 11,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-category.create',
                'display_name' => 'Create Model Categories',
                'description' => 'Permission to create new model categories',
                'module_name' => 'model-ai',
                'sort_order' => 12,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-category.edit',
                'display_name' => 'Edit Model Categories',
                'description' => 'Permission to update existing model categories',
                'module_name' => 'model-ai',
                'sort_order' => 13,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-category.delete',
                'display_name' => 'Delete Model Categories',
                'description' => 'Permission to soft delete and restore model categories',
                'module_name' => 'model-ai',
                'sort_order' => 14,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-category.destroy',
                'display_name' => 'Destroy Model Categories',
                'description' => 'Permission to permanently delete model categories',
                'module_name' => 'model-ai',
                'sort_order' => 15,
                'guard_name' => 'api',
            ],
        ];

        // Model Tool permissions
        $modelToolPermissions = [
            [
                'name' => 'model-tool.view',
                'display_name' => 'View Model Tools',
                'description' => 'Permission to view model tools list and details',
                'module_name' => 'model-ai',
                'sort_order' => 16,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-tool.create',
                'display_name' => 'Create Model Tools',
                'description' => 'Permission to create new model tools',
                'module_name' => 'model-ai',
                'sort_order' => 17,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-tool.edit',
                'display_name' => 'Edit Model Tools',
                'description' => 'Permission to update existing model tools',
                'module_name' => 'model-ai',
                'sort_order' => 18,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-tool.delete',
                'display_name' => 'Delete Model Tools',
                'description' => 'Permission to soft delete and restore model tools',
                'module_name' => 'model-ai',
                'sort_order' => 19,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-tool.destroy',
                'display_name' => 'Destroy Model Tools',
                'description' => 'Permission to permanently delete model tools',
                'module_name' => 'model-ai',
                'sort_order' => 20,
                'guard_name' => 'api',
            ],
        ];

        // Model Service permissions
        $modelServicePermissions = [
            [
                'name' => 'model-service.view',
                'display_name' => 'View Model Services',
                'description' => 'Permission to view model services list and details',
                'module_name' => 'model-ai',
                'sort_order' => 21,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-service.create',
                'display_name' => 'Create Model Services',
                'description' => 'Permission to create new model services',
                'module_name' => 'model-ai',
                'sort_order' => 22,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-service.edit',
                'display_name' => 'Edit Model Services',
                'description' => 'Permission to update existing model services',
                'module_name' => 'model-ai',
                'sort_order' => 23,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-service.delete',
                'display_name' => 'Delete Model Services',
                'description' => 'Permission to soft delete and restore model services',
                'module_name' => 'model-ai',
                'sort_order' => 24,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-service.destroy',
                'display_name' => 'Destroy Model Services',
                'description' => 'Permission to permanently delete model services',
                'module_name' => 'model-ai',
                'sort_order' => 25,
                'guard_name' => 'api',
            ],
        ];

        // Model AI Tool permissions
        $modelAIToolPermissions = [
            [
                'name' => 'model-ai-tool.view',
                'display_name' => 'View Model AI Tools',
                'description' => 'Permission to view model AI tool relationships',
                'module_name' => 'model-ai',
                'sort_order' => 26,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-ai-tool.create',
                'display_name' => 'Create Model AI Tools',
                'description' => 'Permission to create model AI tool relationships',
                'module_name' => 'model-ai',
                'sort_order' => 27,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-ai-tool.edit',
                'display_name' => 'Edit Model AI Tools',
                'description' => 'Permission to update model AI tool relationships',
                'module_name' => 'model-ai',
                'sort_order' => 28,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-ai-tool.delete',
                'display_name' => 'Delete Model AI Tools',
                'description' => 'Permission to soft delete and restore model AI tool relationships',
                'module_name' => 'model-ai',
                'sort_order' => 29,
                'guard_name' => 'api',
            ],
            [
                'name' => 'model-ai-tool.destroy',
                'display_name' => 'Destroy Model AI Tools',
                'description' => 'Permission to permanently delete model AI tool relationships',
                'module_name' => 'model-ai',
                'sort_order' => 30,
                'guard_name' => 'api',
            ],
        ];

        // Combine all permissions
        $allPermissions = array_merge(
            $modelAIPermissions,
            $modelProviderPermissions,
            $modelCategoryPermissions,
            $modelToolPermissions,
            $modelServicePermissions,
            $modelAIToolPermissions
        );

        // Create permissions using firstOrCreate pattern like Language module
        foreach ($allPermissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }

        // Assign all permissions to super-admin role for api guard
        $superAdminRole = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();
        if ($superAdminRole) {
            $permissionNames = array_column($allPermissions, 'name');
            $permissions = Permission::whereIn('name', $permissionNames)->where('guard_name', 'api')->get();
            $superAdminRole->permissions()->syncWithoutDetaching($permissions);
        }

        $this->command->info('ModelAI module permissions seeded successfully.');
    }
}
