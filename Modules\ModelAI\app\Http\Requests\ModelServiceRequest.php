<?php

namespace Modules\ModelAI\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

/**
 * @property mixed $id
 * @property mixed $model_service_id
 */
class ModelServiceRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'model_ai_id' => [
                'required',
                'integer',
                'exists:model_ai,id',
            ],
            'input_types' => [
                'nullable',
                'array',
            ],
            'input_types.*' => [
                'string',
                Rule::in(['text', 'image', 'audio', 'video', 'pdf', 'document']),
            ],
            'output_types' => [
                'nullable',
                'array',
            ],
            'output_types.*' => [
                'string',
                Rule::in(['text', 'image', 'audio', 'video']),
            ],
            'supported_sources' => [
                'nullable',
                'array',
            ],
            'cost_per_request' => [
                'nullable',
                'integer',
                'min:0',
            ],
            'cost_per1k_tokens' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999.9999',
            ],
            'billing_type' => [
                'required',
                'string',
                Rule::in(['per_request', 'per_token', 'hybrid']),
            ],
            'cost_per1k_input' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.999999',
            ],
            'cost_per1k_output' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.999999',
            ],
            'max_tokens' => [
                'nullable',
                'integer',
                'min:1',
                'max:1000000',
            ],
            'context_window' => [
                'nullable',
                'integer',
                'min:1',
                'max:10000000',
            ],
            'rate_limit_rpm' => [
                'nullable',
                'integer',
                'min:1',
                'max:100000',
            ],
            'timeout_seconds' => [
                'nullable',
                'integer',
                'min:1',
                'max:3600',
            ],
            'parameters' => [
                'nullable',
                'array',
            ],
            'notes' => [
                'nullable',
                'string',
                'max:1000',
            ]
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'model_ai_id' => __('AI Model'),
            'input_types' => __('Input Types'),
            'input_types.*' => __('Input Type'),
            'output_types' => __('Output Types'),
            'output_types.*' => __('Output Type'),
            'supported_sources' => __('Supported Sources'),
            'cost_per_request' => __('Cost Per Request'),
            'cost_per1k_tokens' => __('Cost Per 1K Tokens'),
            'billing_type' => __('Billing Type'),
            'cost_per1k_input' => __('Cost Per 1K Input'),
            'cost_per1k_output' => __('Cost Per 1K Output'),
            'max_tokens' => __('Max Tokens'),
            'context_window' => __('Context Window'),
            'rate_limit_rpm' => __('Rate Limit RPM'),
            'timeout_seconds' => __('Timeout Seconds'),
            'default_parameters' => __('Default Parameters'),
            'allowed_parameters' => __('Allowed Parameters'),
            'priority' => __('Priority'),
            'notes' => __('Notes'),
            'status' => __('Status'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'billing_type.in' => __('The billing type must be per_request, per_token, or hybrid.'),
        ];
    }
}
