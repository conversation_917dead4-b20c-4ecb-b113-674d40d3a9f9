<?php

namespace Modules\ModelAI\Database\Seeders;

use Illuminate\Database\Seeder;

class ModelAIDatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->call([
            // Seed permissions first
            ModelAIPermissionSeeder::class,

            // Seed base data
            ModelProviderSeeder::class,
            ModelCategorySeeder::class,
            ModelToolSeeder::class,

            // Seed AI models
            ModelAISeeder::class,

            // Seed relationships and services
            ModelServiceSeeder::class,
            ModelAIToolSeeder::class,
        ]);

        $this->command->info('ModelAI module seeded successfully.');
    }
}
