<?php

namespace Modules\Currency\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class CurrencyRequest extends BaseFormRequest
{

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'code' => [
                'required',
                'string',
                'size:3',
                'alpha',
                'uppercase',
                Rule::unique('currencies', 'code')->ignore($this->route('currency')),
            ],
            'symbol' => [
                'required',
                'string',
                'max:10',
            ],
            'name' => [
                'required',
                'string',
                'max:50',
                'min:2',
            ],
            'decimal_digits' => [
                'required',
                'integer',
                'min:0',
                'max:8',
            ],
            'decimal_separator' => [
                'required',
                'string',
                'max:5',
                'in:.,',
            ],
            'thousands_separator' => [
                'required',
                'string',
                'max:5',
                'in:","," ","."',
            ],
            'status' => [
                'required',
                'string',
            ],
            'sort_order' => [
                'sometimes',
                'integer',
                'min:1',
                'max:999',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'code' => __('Currency Code'),
            'symbol' => __('Currency Symbol'),
            'name' => __('Currency Name'),
            'decimal_digits' => __('Decimal Digits'),
            'decimal_separator' => __('Decimal Separator'),
            'thousands_separator' => __('Thousands Separator'),
            'status' => __('Status'),
            'sort_order' => __('Sort Order'),
        ];
    }


}
