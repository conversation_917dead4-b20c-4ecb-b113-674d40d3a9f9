<?php

namespace Modules\Currency\Observers;

use Illuminate\Support\Facades\Cache;
use Modules\Currency\Models\Currency;
use Modules\Currency\Models\ExchangeRate;

class ExchangeRateObserver
{
    /**
     * Handle the ExchangeRate "created" event.
     */
    public function created(ExchangeRate $exchangeRate): void
    {
        $this->clearExchangeRateCache($exchangeRate);
    }

    /**
     * Handle the ExchangeRate "updated" event.
     */
    public function updated(ExchangeRate $exchangeRate): void
    {
        $this->clearExchangeRateCache($exchangeRate);
    }

    /**
     * Handle the ExchangeRate "deleted" event.
     */
    public function deleted(ExchangeRate $exchangeRate): void
    {
        $this->clearExchangeRateCache($exchangeRate);
    }

    /**
     * Clear exchange rate cache.
     */
    private function clearExchangeRateCache(ExchangeRate $exchangeRate): void
    {
        if (enabledCache()) {
            // Clear specific exchange rate cache
            Cache::forget("exchange_rates.{$exchangeRate->base_currency}.{$exchangeRate->target_currency}");
            Cache::forget("exchange_rates.{$exchangeRate->target_currency}.{$exchangeRate->base_currency}");

            // Clear all exchange rate related cache patterns
            $this->clearExchangeRatePatterns($exchangeRate);
        }
    }

    /**
     * Clear exchange rate cache patterns.
     */
    private function clearExchangeRatePatterns(ExchangeRate $exchangeRate): void
    {
        // Clear cache for all rates involving these currencies
        $baseCurrency = $exchangeRate->base_currency;
        $targetCurrency = $exchangeRate->target_currency;

        // Get all currency codes to clear cross-rates
        $currencyCodes = Cache::tags(['currencies'])->remember('codes', 60, fn () =>
            Currency::active()->pluck('code')->toArray()
        );

        foreach ($currencyCodes as $code) {
            Cache::forget("exchange_rates.{$baseCurrency}.{$code}");
            Cache::forget("exchange_rates.{$code}.{$baseCurrency}");
            Cache::forget("exchange_rates.{$targetCurrency}.{$code}");
            Cache::forget("exchange_rates.{$code}.{$targetCurrency}");
        }
    }
}
