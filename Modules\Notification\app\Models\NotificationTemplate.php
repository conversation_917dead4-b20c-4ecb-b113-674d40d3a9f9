<?php

namespace Modules\Notification\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Notification\Database\Factories\NotificationTemplateFactory;

class NotificationTemplate extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'notification_type_id',
        'channel',
        'locale',
        'subject',
        'title',
        'content',
        'variables',
        'settings',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'variables' => 'array',
        'settings' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = ['deleted_at'];

    /**
     * Scope a query to only include active templates.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by channel.
     */
    public function scopeByChannel(Builder $query, string $channel): Builder
    {
        return $query->where('channel', $channel);
    }

    /**
     * Scope a query to filter by locale.
     */
    public function scopeByLocale(Builder $query, string $locale): Builder
    {
        return $query->where('locale', $locale);
    }

    /**
     * Scope a query to filter by notification type.
     */
    public function scopeByType(Builder $query, int $typeId): Builder
    {
        return $query->where('notification_type_id', $typeId);
    }

    /**
     * Get the notification type that owns this template.
     */
    public function notificationType(): BelongsTo
    {
        return $this->belongsTo(NotificationType::class);
    }

    /**
     * Render the template content with provided variables.
     */
    public function render(array $data = []): array
    {
        $rendered = [
            'subject' => $this->renderString($this->subject, $data),
            'title' => $this->renderString($this->title, $data),
            'content' => $this->renderString($this->content, $data),
        ];

        return array_filter($rendered, fn($value) => !is_null($value));
    }

    /**
     * Render a string template with variables.
     */
    protected function renderString(?string $template, array $data): ?string
    {
        if (is_null($template)) {
            return null;
        }

        $rendered = $template;
        foreach ($data as $key => $value) {
            $rendered = str_replace('{' . $key . '}', (string) $value, $rendered);
        }

        return $rendered;
    }

    /**
     * Get template variables as array.
     */
    public function getVariables(): array
    {
        return $this->variables ?? [];
    }

    /**
     * Get template settings as array.
     */
    public function getSettings(): array
    {
        return $this->settings ?? [];
    }

    /**
     * Check if template has a specific variable.
     */
    public function hasVariable(string $variable): bool
    {
        return in_array($variable, $this->getVariables());
    }

    /**
     * Get missing variables from provided data.
     */
    public function getMissingVariables(array $data): array
    {
        $required = $this->getVariables();
        $provided = array_keys($data);

        return array_diff($required, $provided);
    }

    /**
     * Validate that all required variables are provided.
     */
    public function validateVariables(array $data): bool
    {
        return empty($this->getMissingVariables($data));
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): NotificationTemplateFactory
    {
        return NotificationTemplateFactory::new();
    }
}
