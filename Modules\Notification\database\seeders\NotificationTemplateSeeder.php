<?php

namespace Modules\Notification\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Notification\Models\NotificationType;

class NotificationTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get notification types (excluding Auth-related types which are now managed by Auth module)
        $notificationTypes = NotificationType::whereIn('key', [
            'system_maintenance', 'general_announcement', 'promotional', 'reminder',
            'order_confirmation', 'payment_notification'
        ])->get()->keyBy('key');

        $templates = [];

        $this->command->info('Creating non-auth notification templates...');

        // System maintenance templates
        if (isset($notificationTypes['system_maintenance'])) {
            $maintenanceType = $notificationTypes['system_maintenance'];

            $templates[] = [
                'notification_type_id' => $maintenanceType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Scheduled Maintenance: {maintenance_date}',
                'content' => 'System maintenance scheduled for {maintenance_date} from {start_time} to {end_time}. {maintenance_description}',
                'variables' => json_encode(['maintenance_date', 'start_time', 'end_time', 'maintenance_description']),
                'settings' => null,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $maintenanceType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Scheduled System Maintenance - {maintenance_date}',
                'title' => 'Scheduled System Maintenance',
                'content' => 'Dear {user_name},\n\nWe will be performing scheduled maintenance on {app_name}:\n\nDate: {maintenance_date}\nTime: {start_time} to {end_time}\n\nMaintenance Details:\n{maintenance_description}\n\nDuring this time, the system may be temporarily unavailable. We apologize for any inconvenience.\n\nBest regards,\nThe {app_name} Team',
                'variables' => json_encode(['app_name', 'user_name', 'maintenance_date', 'start_time', 'end_time', 'maintenance_description']),
                'settings' => json_encode(['from_name' => '{app_name} Operations']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // General announcement templates
        if (isset($notificationTypes['general_announcement'])) {
            $announcementType = $notificationTypes['general_announcement'];

            $templates[] = [
                'notification_type_id' => $announcementType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => '{announcement_title}',
                'content' => '{announcement_message}',
                'variables' => json_encode(['announcement_title', 'announcement_message', 'app_name', 'user_name']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $announcementType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => '{announcement_title}',
                'title' => '{announcement_title}',
                'content' => 'Dear {user_name},\n\n{announcement_message}\n\nBest regards,\nThe {app_name} Team',
                'variables' => json_encode(['announcement_title', 'announcement_message', 'app_name', 'user_name']),
                'settings' => json_encode(['from_name' => '{app_name} Team', 'priority' => 'normal']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Promotional templates
        if (isset($notificationTypes['promotional'])) {
            $promotionalType = $notificationTypes['promotional'];

            $templates[] = [
                'notification_type_id' => $promotionalType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => '{promo_title}',
                'content' => '{promo_message}. Valid until {promo_expires}.',
                'variables' => json_encode(['promo_title', 'promo_message', 'promo_expires', 'promo_code', 'app_name', 'user_name']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $promotionalType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => '{promo_title} - Special Offer!',
                'title' => '{promo_title}',
                'content' => 'Dear {user_name},\n\n{promo_message}\n\nPromo Code: {promo_code}\nValid Until: {promo_expires}\n\nDon\'t miss out on this amazing offer!\n\nBest regards,\nThe {app_name} Team',
                'variables' => json_encode(['promo_title', 'promo_message', 'promo_expires', 'promo_code', 'app_name', 'user_name']),
                'settings' => json_encode(['from_name' => '{app_name} Promotions', 'priority' => 'normal']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Reminder templates
        if (isset($notificationTypes['reminder'])) {
            $reminderType = $notificationTypes['reminder'];

            $templates[] = [
                'notification_type_id' => $reminderType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Reminder: {reminder_title}',
                'content' => '{reminder_message}',
                'variables' => json_encode(['reminder_title', 'reminder_message', 'reminder_date', 'app_name', 'user_name']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $reminderType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Reminder: {reminder_title}',
                'title' => 'Reminder',
                'content' => 'Dear {user_name},\n\nThis is a friendly reminder:\n\n{reminder_message}\n\nReminder Date: {reminder_date}\n\nBest regards,\nThe {app_name} Team',
                'variables' => json_encode(['reminder_title', 'reminder_message', 'reminder_date', 'app_name', 'user_name']),
                'settings' => json_encode(['from_name' => '{app_name} Reminders', 'priority' => 'normal']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Order confirmation templates
        if (isset($notificationTypes['order_confirmation'])) {
            $orderType = $notificationTypes['order_confirmation'];

            $templates[] = [
                'notification_type_id' => $orderType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Order Confirmed - #{order_number}',
                'content' => 'Your order #{order_number} has been confirmed and is being processed.',
                'variables' => json_encode(['order_number', 'order_total', 'user_name', 'app_name']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $orderType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Order Confirmation - #{order_number}',
                'title' => 'Thank you for your order!',
                'content' => 'Dear {user_name},\n\nThank you for your order on {app_name}!\n\nOrder Details:\nOrder Number: #{order_number}\nTotal Amount: {order_total}\n\nYour order is being processed and you will receive updates on its status.\n\nBest regards,\nThe {app_name} Team',
                'variables' => json_encode(['order_number', 'order_total', 'user_name', 'app_name']),
                'settings' => json_encode(['from_name' => '{app_name} Orders', 'priority' => 'high']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $orderType->id,
                'channel' => 'sms',
                'locale' => 'en',
                'subject' => null,
                'title' => null,
                'content' => 'Order #{order_number} confirmed! Total: {order_total}. Thank you for shopping with {app_name}.',
                'variables' => json_encode(['order_number', 'order_total', 'app_name']),
                'settings' => json_encode(['sender_id' => '{app_name}']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Payment notification templates
        if (isset($notificationTypes['payment_notification'])) {
            $paymentType = $notificationTypes['payment_notification'];

            $templates[] = [
                'notification_type_id' => $paymentType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Payment {payment_status}',
                'content' => 'Payment of {payment_amount} for order #{order_number} has been {payment_status}.',
                'variables' => json_encode(['payment_status', 'payment_amount', 'order_number', 'user_name', 'app_name']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $paymentType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Payment {payment_status} - Order #{order_number}',
                'title' => 'Payment Update',
                'content' => 'Dear {user_name},\n\nYour payment for order #{order_number} has been {payment_status}.\n\nPayment Details:\nAmount: {payment_amount}\nStatus: {payment_status}\nOrder: #{order_number}\n\nIf you have any questions, please contact our support team.\n\nBest regards,\nThe {app_name} Team',
                'variables' => json_encode(['payment_status', 'payment_amount', 'order_number', 'user_name', 'app_name']),
                'settings' => json_encode(['from_name' => '{app_name} Payments', 'priority' => 'high']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }



        if (!empty($templates)) {
            DB::table('notification_templates')->insert($templates);
            $this->command->info('Non-auth notification templates seeded successfully. Total: ' . count($templates));
        } else {
            $this->command->info('No non-auth notification templates to seed.');
        }
    }
}
