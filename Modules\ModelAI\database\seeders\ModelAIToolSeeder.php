<?php

namespace Modules\ModelAI\Database\Seeders;

use Illuminate\Database\Seeder;
use Mo<PERSON>les\ModelAI\Models\ModelAI;
use Modules\ModelAI\Models\ModelTool;
use Modules\ModelAI\Models\ModelAITool;

class ModelAIToolSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $configurations = [
            // GPT-4 Turbo tools
            [
                'model_key' => 'gpt-4-turbo',
                'tool_key' => 'web_search',
                'is_enabled' => true,
                'configuration' => [
                    'max_results' => 5,
                    'timeout' => 30,
                ],
                'priority' => 90,
                'max_usage_per_request' => 3,
                'rate_limit_per_minute' => 20,
            ],
            [
                'model_key' => 'gpt-4-turbo',
                'tool_key' => 'code_interpreter',
                'is_enabled' => true,
                'configuration' => [
                    'timeout' => 120,
                    'memory_limit' => '1GB',
                ],
                'priority' => 95,
                'max_usage_per_request' => 5,
                'rate_limit_per_minute' => 10,
            ],
            [
                'model_key' => 'gpt-4-turbo',
                'tool_key' => 'dall_e',
                'is_enabled' => true,
                'configuration' => [
                    'size' => '1024x1024',
                    'quality' => 'hd',
                ],
                'priority' => 80,
                'max_usage_per_request' => 2,
                'rate_limit_per_minute' => 5,
            ],

            // Claude 3 Sonnet tools
            [
                'model_key' => 'claude-3-sonnet',
                'tool_key' => 'web_search',
                'is_enabled' => true,
                'configuration' => [
                    'max_results' => 3,
                    'timeout' => 25,
                ],
                'priority' => 85,
                'max_usage_per_request' => 2,
                'rate_limit_per_minute' => 15,
            ],
            [
                'model_key' => 'claude-3-sonnet',
                'tool_key' => 'weather_api',
                'is_enabled' => true,
                'configuration' => [
                    'units' => 'metric',
                    'timeout' => 10,
                ],
                'priority' => 70,
                'max_usage_per_request' => 5,
                'rate_limit_per_minute' => 30,
            ],

            // CodeLlama tools
            [
                'model_key' => 'codellama-34b',
                'tool_key' => 'code_interpreter',
                'is_enabled' => true,
                'configuration' => [
                    'timeout' => 90,
                    'memory_limit' => '512MB',
                    'language' => 'python',
                ],
                'priority' => 100,
                'max_usage_per_request' => 10,
                'rate_limit_per_minute' => 20,
            ],
            [
                'model_key' => 'codellama-34b',
                'tool_key' => 'database_query',
                'is_enabled' => true,
                'configuration' => [
                    'read_only' => true,
                    'timeout' => 30,
                    'max_rows' => 1000,
                ],
                'priority' => 75,
                'max_usage_per_request' => 3,
                'rate_limit_per_minute' => 10,
            ],
        ];

        foreach ($configurations as $config) {
            $model = ModelAI::where('key', $config['model_key'])->first();
            $tool = ModelTool::where('key', $config['tool_key'])->first();

            if ($model && $tool) {
                ModelAITool::updateOrCreate(
                    [
                        'model_ai_id' => $model->id,
                        'model_tool_id' => $tool->id,
                    ],
                    [
                        'is_enabled' => $config['is_enabled'],
                        'configuration' => $config['configuration'],
                        'priority' => $config['priority'],
                        'max_usage_per_request' => $config['max_usage_per_request'],
                        'rate_limit_per_minute' => $config['rate_limit_per_minute'],
                    ]
                );
            }
        }
    }
}
