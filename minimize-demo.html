<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget Minimize/Maximize Demo</title>
    <link rel="stylesheet" href="dist/widget/procms-chatbot.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .hero {
            padding: 40px 0;
        }
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .hero p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .widget-container {
            width: 400px;
            height: 600px;
            margin: 20px auto;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        .status {
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }
        .status.success { 
            background: rgba(34, 197, 94, 0.2); 
            border: 1px solid rgba(34, 197, 94, 0.4);
        }
        .status.info { 
            background: rgba(59, 130, 246, 0.2); 
            border: 1px solid rgba(59, 130, 246, 0.4);
        }
        .feature-list {
            text-align: left;
            margin: 20px 0;
        }
        .feature-list li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4ade80;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1>🔽 Minimize/Maximize Demo</h1>
            <p>Test the new minimize and maximize functionality</p>
        </div>

        <!-- Embedded Widget Demo -->
        <div class="demo-section">
            <h3>📱 Embedded Widget with Minimize Controls</h3>
            
            <div class="controls">
                <button class="btn" onclick="loadWidget()">Load Widget</button>
                <button class="btn" onclick="minimizeWidget()" id="minimize-btn" disabled>Minimize</button>
                <button class="btn" onclick="maximizeWidget()" id="maximize-btn" disabled>Maximize</button>
                <button class="btn" onclick="toggleWidget()" id="toggle-btn" disabled>Toggle</button>
                <button class="btn" onclick="clearWidget()">Clear</button>
            </div>
            
            <div id="widget-container" class="widget-container"></div>
            <div id="status" class="status" style="display: none;"></div>
        </div>

        <!-- Floating Widget Demo -->
        <div class="demo-section">
            <h3>🎈 Floating Widget Demo</h3>
            <p>Click to create floating widgets that can be minimized</p>
            
            <div class="controls">
                <button class="btn" onclick="createFloating('bottom-right')">Bottom Right</button>
                <button class="btn" onclick="createFloating('bottom-left')">Bottom Left</button>
                <button class="btn" onclick="createFloating('center')">Center</button>
                <button class="btn" onclick="removeFloating()">Remove All</button>
            </div>
            
            <div id="floating-status" class="status" style="display: none;"></div>
        </div>

        <!-- Features -->
        <div class="demo-section">
            <h3>🎯 Minimize/Maximize Features</h3>
            <ul class="feature-list">
                <li>Click header to toggle minimize/maximize</li>
                <li>Dedicated minimize button (−) in header</li>
                <li>Smooth animations with CSS transitions</li>
                <li>Event callbacks for minimize/maximize actions</li>
                <li>Programmatic control via JavaScript API</li>
                <li>Auto-minimize on mobile devices</li>
                <li>Maintains widget state during minimize</li>
                <li>Visual feedback with button state changes</li>
            </ul>
        </div>
    </div>

    <!-- Load Widget Library -->
    <script src="dist/widget/procms-chatbot.umd.js"></script>
    <script>
        let widget = null;
        let floatingWidgets = [];

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            console.log(`[Demo] ${message}`);
        }

        function showFloatingStatus(message, type = 'info') {
            const statusEl = document.getElementById('floating-status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            console.log(`[Floating] ${message}`);
        }

        function updateButtons() {
            const hasWidget = widget && widget.isMounted();
            document.getElementById('minimize-btn').disabled = !hasWidget;
            document.getElementById('maximize-btn').disabled = !hasWidget;
            document.getElementById('toggle-btn').disabled = !hasWidget;
        }

        async function loadWidget() {
            try {
                if (widget) {
                    widget.unmount();
                }

                showStatus('Loading widget...', 'info');

                widget = await ProcmsChatbot.create({
                    botUuid: 'demo-bot-uuid-123',
                    apiKey: 'pk_test_demo_api_key_12345678901234567890',
                    theme: 'light',
                    onReady: () => {
                        showStatus('Widget loaded! Try the minimize controls.', 'success');
                        updateButtons();
                    },
                    onMinimized: () => {
                        showStatus('Widget minimized ✓', 'info');
                        console.log('Widget state:', {
                            mounted: widget.isMounted(),
                            minimized: widget.isMinimized()
                        });
                    },
                    onMaximized: () => {
                        showStatus('Widget maximized ✓', 'info');
                        console.log('Widget state:', {
                            mounted: widget.isMounted(),
                            minimized: widget.isMinimized()
                        });
                    },
                    onCloseRequested: () => {
                        showStatus('Close button clicked - widget will be removed', 'info');
                        setTimeout(() => {
                            clearWidget();
                        }, 1000);
                    }
                }, '#widget-container');

            } catch (error) {
                showStatus(`Error: ${error.message}`, 'error');
                console.error('Widget load error:', error);
            }
        }

        function minimizeWidget() {
            if (widget) {
                widget.minimize();
                showStatus('Minimize command sent', 'info');
            }
        }

        function maximizeWidget() {
            if (widget) {
                widget.maximize();
                showStatus('Maximize command sent', 'info');
            }
        }

        function toggleWidget() {
            if (widget) {
                const wasMinimized = widget.isMinimized();
                widget.toggle();
                showStatus(`Toggle command sent (was ${wasMinimized ? 'minimized' : 'maximized'})`, 'info');
            }
        }

        function clearWidget() {
            if (widget) {
                widget.unmount();
                widget = null;
                showStatus('Widget cleared', 'info');
                updateButtons();
            }
        }

        async function createFloating(position) {
            try {
                showFloatingStatus(`Creating floating widget at ${position}...`, 'info');

                const floatingWidget = await ProcmsChatbot.createFloatingWidget({
                    botUuid: 'floating-bot-uuid-123',
                    apiKey: 'pk_test_floating_api_key_12345678901234567890',
                    theme: 'dark',
                    position: position,
                    autoMinimizeOnMobile: true,
                    onReady: () => {
                        showFloatingStatus(`Floating widget created at ${position}!`, 'success');
                    },
                    onMinimized: () => {
                        console.log(`Floating widget at ${position} minimized`);
                    },
                    onMaximized: () => {
                        console.log(`Floating widget at ${position} maximized`);
                    },
                    onCloseRequested: () => {
                        console.log(`Floating widget at ${position} close requested`);
                        // Widget will auto-remove itself
                        floatingWidgets = floatingWidgets.filter(w => w !== floatingWidget);
                    }
                });

                floatingWidgets.push(floatingWidget);

            } catch (error) {
                showFloatingStatus(`Error: ${error.message}`, 'error');
                console.error('Floating widget error:', error);
            }
        }

        function removeFloating() {
            floatingWidgets.forEach(w => {
                try {
                    w.unmount();
                    const container = document.getElementById('procms-floating-widget');
                    if (container) container.remove();
                } catch (error) {
                    console.error('Error removing floating widget:', error);
                }
            });
            floatingWidgets = [];
            showFloatingStatus('All floating widgets removed', 'info');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Minimize/Maximize Demo loaded');
            console.log('Widget library available:', {
                ProcmsChatbotWidget: typeof ProcmsChatbotWidget !== 'undefined',
                ProcmsChatbot: typeof ProcmsChatbot !== 'undefined'
            });
            updateButtons();
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (widget) widget.unmount();
            floatingWidgets.forEach(w => w.unmount());
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'm':
                        e.preventDefault();
                        if (widget) toggleWidget();
                        break;
                    case 'l':
                        e.preventDefault();
                        loadWidget();
                        break;
                }
            }
        });

        console.log('Keyboard shortcuts:');
        console.log('  Ctrl/Cmd + M: Toggle widget');
        console.log('  Ctrl/Cmd + L: Load widget');
    </script>
</body>
</html>
