<?php

namespace Modules\ModelAI\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Core\Traits\ResponseTrait;
use Modules\ModelAI\Http\Filters\ModelCategoryFilter;
use Modules\ModelAI\Http\Requests\BulkModelCategoryRequest;
use Modules\ModelAI\Http\Requests\BulkModelCategoryDestroyRequest;
use Modules\ModelAI\Http\Requests\ModelCategoryRequest;
use Modules\ModelAI\Models\ModelCategory;

class ModelCategoryController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|model-category.view')->only(['index', 'show', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|model-category.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|model-category.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|model-category.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|model-category.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $categories = ModelCategory::query()
            ->filter(new ModelCategoryFilter($request))
            ->ordered()
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($categories, __('Model categories retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ModelCategoryRequest $request): JsonResponse
    {
        try {
            $category = ModelCategory::create($request->all());
            return $this->successResponse($category, __('Model category created successfully.'), 201);
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to create model category.'));
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        $category = ModelCategory::withTrashed()->findOrFail($id);
        return $this->successResponse($category, __('Model category retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ModelCategoryRequest $request, int $id): JsonResponse
    {
        try {
            $category = ModelCategory::withTrashed()->findOrFail($id);
            $category->update($request->all());
            return $this->successResponse($category->fresh(), __('Model category updated successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to update model category.'));
        }
    }

    /**
     * Soft delete the specified resource.
     */
    public function delete(int $id): JsonResponse
    {
        try {
            $category = ModelCategory::withTrashed()->findOrFail($id);
            $category->delete();

            return $this->successResponse(null, __('Model category deleted successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to delete model category.'));
        }
    }

    /**
     * Restore the specified resource from trash.
     */
    public function restore(int $id): JsonResponse
    {
        try {
            $category = ModelCategory::onlyTrashed()->findOrFail($id);
            $category->restore();

            return $this->successResponse($category->fresh(), __('Model category restored successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to restore model category.'));
        }
    }

    /**
     * Permanently delete the specified resource.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $category = ModelCategory::withTrashed()->findOrFail($id);
            $category->forceDelete();

            return $this->successResponse(null, __('Model category permanently deleted successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to permanently delete model category.'));
        }
    }

    /**
     * Bulk soft delete categories.
     */
    public function bulkDelete(BulkModelCategoryRequest $request): JsonResponse
    {
        $ids = $request->input('ids');
        $categories = ModelCategory::whereIn('id', $ids)->get();

        $deletedCount = 0;
        $errors = [];

        foreach ($categories as $category) {
            try {
                $category->delete();
                $deletedCount++;
            } catch (\Exception $e) {
                $errors[] = "Category '{$category->name}' cannot be deleted.";
            }
        }

        $message = __(':count categories deleted successfully.', ['count' => $deletedCount]);

        if (!empty($errors)) {
            $message .= ' ' . __('Some categories could not be deleted: :errors', ['errors' => implode(', ', $errors)]);
        }

        return $this->successResponse(['deleted_count' => $deletedCount, 'errors' => $errors], $message);
    }

    /**
     * Bulk restore categories.
     */
    public function bulkRestore(BulkModelCategoryRequest $request): JsonResponse
    {
        $ids = $request->input('ids');
        $restoredCount = ModelCategory::onlyTrashed()->whereIn('id', $ids)->restore();

        return $this->successResponse(
            ['restored_count' => $restoredCount],
            __('Categories restored successfully.')
        );
    }

    /**
     * Bulk permanently delete categories.
     */
    public function bulkDestroy(BulkModelCategoryDestroyRequest $request): JsonResponse
    {
        $ids = $request->input('ids');
        $categories = ModelCategory::withTrashed()->whereIn('id', $ids)->get();

        $deletedCount = 0;
        $errors = [];

        foreach ($categories as $category) {
            try {
                $category->forceDelete();
                $deletedCount++;
            } catch (\Exception $e) {
                $errors[] = "Category '{$category->name}' cannot be permanently deleted.";
            }
        }

        $message = __(':count categories permanently deleted.', ['count' => $deletedCount]);

        if (!empty($errors)) {
            $message .= ' ' . __('Some categories could not be deleted: :errors', ['errors' => implode(', ', $errors)]);
        }

        return $this->successResponse(['deleted_count' => $deletedCount, 'errors' => $errors], $message);
    }

    /**
     * Get categories for dropdown.
     */
    public function dropdown(): JsonResponse
    {
        $categories = ModelCategory::query()->active()->ordered()->get();
        return $this->successResponse(
            $categories->makeHidden(['updated_at', 'created_at', 'status']),
            __('Categories retrieved successfully.')
        );
    }
}
