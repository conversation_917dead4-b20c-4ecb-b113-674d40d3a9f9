<?php

namespace Modules\ModelAI\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Modules\Core\Traits\ResponseTrait;
use Modules\ModelAI\Facades\ModelAIFacade;
use Modules\ModelAI\Models\ModelAI;

class ModelAIController extends Controller
{
    use ResponseTrait;

    /**
     * Display a listing of active AI models.
     */
    public function index(Request $request): JsonResponse
    {
        return $this->successResponse(
            ModelAIFacade::getActiveModels(),
            __('AI models retrieved successfully.')
        );
    }

    /**
     * Get default AI model.
     */
    public function default(): JsonResponse
    {
        return $this->successResponse(ModelAIFacade::getDefaultModel());
    }

    /**
     * Display the specified AI model by key.
     */
    public function show(string $key): JsonResponse
    {
        return $this->successResponse(ModelAIFacade::find<PERSON>y<PERSON><PERSON>($key));
    }

    /**
     * Get categories for dropdown.
     */
    public function categoriesDropdown(Request $request): JsonResponse
    {
        $locale = $request->input('locale', App::getLocale());
        $categories = ModelAIFacade::getCategoriesForDropdown($locale);

        return $this->successResponse($categories, __('Model categories retrieved successfully.'));
    }

    /**
     * Get AI models for dropdown.
     */
    public function modelsDropdown(Request $request): JsonResponse
    {
        $locale = $request->input('locale', App::getLocale());
        $models = ModelAIFacade::getModelsForDropdown($locale);

        return $this->successResponse($models, __('AI models retrieved successfully.'));
    }

    /**
     * Get categories with their associated models.
     */
    public function categoriesWithModels(Request $request): JsonResponse
    {
        $locale = $request->input('locale', App::getLocale());
        $data = ModelAIFacade::getCategoriesWithModels($locale);

        return $this->successResponse($data, __('Categories with models retrieved successfully.'));
    }

    /**
     * Get AI models with their associated categories.
     */
    public function modelsWithCategories(Request $request): JsonResponse
    {
        $locale = $request->input('locale', App::getLocale());
        $data = ModelAIFacade::getModelsWithCategories($locale);

        return $this->successResponse($data, __('Models with categories retrieved successfully.'));
    }


}
