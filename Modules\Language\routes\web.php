<?php

use Illuminate\Support\Facades\Route;
use Modules\Language\Http\Controllers\Auth\LanguageController as AuthLanguageController;

Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('languages', AuthLanguageController::class)->names('language');

    // Additional routes for soft delete management
    Route::get('languages/trashed/list', [AuthLanguageController::class, 'trashed'])->name('language.trashed');
    Route::patch('languages/{id}/restore', [AuthLanguageController::class, 'restore'])->name('language.restore');
    Route::delete('languages/{id}/force-delete', [AuthLanguageController::class, 'forceDelete'])->name('language.force-delete');

    // Bulk operation routes
    Route::post('languages/bulk-delete', [AuthLanguageController::class, 'bulkDelete'])->name('language.bulk-delete');
    Route::post('languages/bulk-restore', [AuthLanguageController::class, 'bulkRestore'])->name('language.bulk-restore');
    Route::post('languages/bulk-force-delete', [AuthLanguageController::class, 'bulkForceDelete'])->name('language.bulk-force-delete');
});
