<?php

namespace Modules\Location\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Location\Database\Factories\GeoDivisionFactory;


class GeoDivision extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'code',
        'name',
        'native_name',
        'type',
        'country_id',
        'parent_id',
        'level',
        'path',
        'latitude',
        'longitude',
        'postal_code',
        'sort_order',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'sort_order' => 'integer',
        'level' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = ['deleted_at'];

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        // Automatically set level and path when creating/updating
        static::saving(function ($division) {
            if ($division->parent_id) {
                $parent = static::find($division->parent_id);
                if ($parent) {
                    $division->level = $parent->level + 1;
                    $division->path = $parent->path ? $parent->path . '/' . $parent->id : $parent->id;
                }
            } else {
                $division->level = 1;
                $division->path = null;
            }
        });

        // Update children paths when parent path changes
        static::updated(function ($division) {
            if ($division->isDirty('path')) {
                $division->updateChildrenPaths();
            }
        });


    }

    /**
     * Update paths for all children recursively.
     */
    public function updateChildrenPaths(): void
    {
        $children = $this->children;
        foreach ($children as $child) {
            $child->path = $this->path ? $this->path . '/' . $this->id : $this->id;
            $child->save();
        }
    }

    /**
     * Scope a query to only include active divisions.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to filter by type.
     */
    public function scopeType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to filter by level.
     */
    public function scopeLevel(Builder $query, int $level): Builder
    {
        return $query->where('level', $level);
    }

    /**
     * Scope a query to filter by country.
     */
    public function scopeCountry(Builder $query, int $countryId): Builder
    {
        return $query->where('country_id', $countryId);
    }

    /**
     * Scope a query to filter by parent.
     */
    public function scopeParent(Builder $query, int $parentId): Builder
    {
        return $query->where('parent_id', $parentId);
    }

    /**
     * Get the country that owns this division.
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get the parent division.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(GeoDivision::class, 'parent_id');
    }

    /**
     * Get the children divisions.
     */
    public function children(): HasMany
    {
        return $this->hasMany(GeoDivision::class, 'parent_id');
    }

    /**
     * Get all descendants recursively.
     */
    public function descendants(): HasMany
    {
        return $this->hasMany(GeoDivision::class, 'parent_id')->with('descendants');
    }

    /**
     * Get all ancestors.
     */
    public function ancestors()
    {
        $ancestors = collect();
        $current = $this->parent;
        
        while ($current) {
            $ancestors->prepend($current);
            $current = $current->parent;
        }
        
        return $ancestors;
    }

    /**
     * Check if this division is a state/province.
     */
    public function isState(): bool
    {
        return $this->type === 'state' && $this->level === 1;
    }

    /**
     * Check if this division is a city.
     */
    public function isCity(): bool
    {
        return $this->type === 'city';
    }

    /**
     * Check if this division is a district.
     */
    public function isDistrict(): bool
    {
        return $this->type === 'district';
    }

    /**
     * Get the full hierarchical name.
     */
    public function getFullNameAttribute(): string
    {
        $ancestors = $this->ancestors();
        $names = $ancestors->pluck('name')->toArray();
        $names[] = $this->name;
        
        return implode(', ', $names);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): GeoDivisionFactory
    {
        return GeoDivisionFactory::new();
    }
}
