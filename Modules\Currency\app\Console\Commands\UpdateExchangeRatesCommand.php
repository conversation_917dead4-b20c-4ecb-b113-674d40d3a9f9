<?php

namespace Modules\Currency\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\Currency\Models\Currency;
use Modules\Currency\Models\ExchangeRate;

class UpdateExchangeRatesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'currency:update-rates
                            {--base= : Base currency code (default: from settings)}
                            {--targets= : Comma-separated target currencies (default: from settings)}
                            {--api= : API provider (freecurrency, exchangerate, fixer, currencylayer, mock)}
                            {--force : Force update even if rates are fresh}';

    /**
     * The console command description.
     */
    protected $description = 'Update exchange rates from external API';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $baseCurrency = $this->option('base') ?: setting('currency.currency_base_currency', 'USD');
        $apiProvider = $this->option('api') ?: setting('currency.currency_api_default', 'freecurrency');
        $force = $this->option('force');

        $this->info("Starting exchange rate update...");
        $this->info("Base currency: {$baseCurrency}");
        $this->info("API provider: {$apiProvider}");

        // Get target currencies
        $targetCurrencies = $this->getTargetCurrencies();
        
        if (empty($targetCurrencies)) {
            $this->error('No target currencies found.');
            return self::FAILURE;
        }

        $this->info("Target currencies: " . implode(', ', $targetCurrencies));

        // Check if update is needed
        if (!$force && !$this->shouldUpdate($baseCurrency, $targetCurrencies)) {
            $this->info('Exchange rates are fresh. Use --force to update anyway.');
            return self::SUCCESS;
        }

        // Update rates
        $updatedCount = 0;
        $failedCount = 0;

        foreach ($targetCurrencies as $targetCurrency) {
            if ($baseCurrency === $targetCurrency) continue;

            try {
                $rate = $this->fetchExchangeRate($baseCurrency, $targetCurrency, $apiProvider);
                
                if ($rate) {
                    $this->updateExchangeRate($baseCurrency, $targetCurrency, $rate);
                    $updatedCount++;
                    $this->line("✓ {$baseCurrency}/{$targetCurrency}: {$rate}");
                } else {
                    $failedCount++;
                    $this->error("✗ Failed to get rate for {$baseCurrency}/{$targetCurrency}");
                }
            } catch (\Exception $e) {
                $failedCount++;
                $this->error("✗ Error updating {$baseCurrency}/{$targetCurrency}: " . $e->getMessage());
                Log::error("Exchange rate update failed", [
                    'base' => $baseCurrency,
                    'target' => $targetCurrency,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->info("\nUpdate completed:");
        $this->info("Updated: {$updatedCount}");
        $this->info("Failed: {$failedCount}");

        return $failedCount > 0 ? self::FAILURE : self::SUCCESS;
    }

    /**
     * Get target currencies from option, settings, or all active currencies.
     */
    private function getTargetCurrencies(): array
    {
        $targets = $this->option('targets');

        if ($targets) {
            return array_map('trim', explode(',', $targets));
        }

        // Get from settings
        $settingTargets = setting('currency.currency_target_currencies', 'VND,EUR,GBP,JPY');
        if ($settingTargets) {
            return array_map('trim', explode(',', $settingTargets));
        }

        return Currency::active()->pluck('code')->toArray();
    }

    /**
     * Check if exchange rates should be updated.
     */
    private function shouldUpdate(string $baseCurrency, array $targetCurrencies): bool
    {
        foreach ($targetCurrencies as $targetCurrency) {
            if ($baseCurrency === $targetCurrency) continue;

            $rate = ExchangeRate::where('base_currency', $baseCurrency)
                ->where('target_currency', $targetCurrency)
                ->first();

            // Update if rate doesn't exist or is stale (older than 1 hour)
            if (!$rate || !$rate->fetched_at || $rate->fetched_at->lt(now()->subHour())) {
                return true;
            }
        }

        return false;
    }

    /**
     * Fetch exchange rate from external API.
     */
    private function fetchExchangeRate(string $baseCurrency, string $targetCurrency, string $apiProvider): ?float
    {
        switch ($apiProvider) {
            case 'freecurrency':
                return $this->fetchFromFreeCurrencyAPI($baseCurrency, $targetCurrency);

            case 'fixer':
                return $this->fetchFromFixer($baseCurrency, $targetCurrency);

            case 'exchangerate':
                return $this->fetchFromExchangeRateAPI($baseCurrency, $targetCurrency);

            case 'mock':
                return $this->fetchMockRate($baseCurrency, $targetCurrency);

            default:
                throw new \InvalidArgumentException("Unknown API provider: {$apiProvider}");
        }
    }

    /**
     * Fetch rate from FreeCurrencyAPI.
     */
    private function fetchFromFreeCurrencyAPI(string $baseCurrency, string $targetCurrency): ?float
    {
        $apiKey = setting('currency.currency_freecurrency_api_key');

        if (!$apiKey) {
            $this->warn('FreeCurrencyAPI key not configured. Using mock data.');
            return $this->fetchMockRate($baseCurrency, $targetCurrency);
        }

        $response = Http::get('https://api.freecurrencyapi.com/v1/latest', [
            'apikey' => $apiKey,
            'base_currency' => $baseCurrency,
            'currencies' => $targetCurrency
        ]);

        if ($response->successful()) {
            $data = $response->json();
            return $data['data'][$targetCurrency] ?? null;
        }

        return null;
    }

    /**
     * Fetch rate from Fixer.io API.
     */
    private function fetchFromFixer(string $baseCurrency, string $targetCurrency): ?float
    {
        $apiKey = config('currency.fixer_api_key');
        
        if (!$apiKey) {
            $this->warn('Fixer API key not configured. Using mock data.');
            return $this->fetchMockRate($baseCurrency, $targetCurrency);
        }

        $response = Http::get('http://data.fixer.io/api/latest', [
            'access_key' => $apiKey,
            'base' => $baseCurrency,
            'symbols' => $targetCurrency
        ]);

        if ($response->successful()) {
            $data = $response->json();
            return $data['rates'][$targetCurrency] ?? null;
        }

        return null;
    }

    /**
     * Fetch rate from ExchangeRate-API.
     */
    private function fetchFromExchangeRateAPI(string $baseCurrency, string $targetCurrency): ?float
    {
        $response = Http::get("https://api.exchangerate-api.com/v4/latest/{$baseCurrency}");

        if ($response->successful()) {
            $data = $response->json();
            return $data['rates'][$targetCurrency] ?? null;
        }

        return null;
    }

    /**
     * Generate mock exchange rate for testing.
     */
    private function fetchMockRate(string $baseCurrency, string $targetCurrency): float
    {
        // Mock rates for testing
        $mockRates = [
            'USD' => [
                'VND' => 23000 + rand(-500, 500),
                'EUR' => 0.85 + (rand(-50, 50) / 1000),
                'GBP' => 0.73 + (rand(-30, 30) / 1000),
                'JPY' => 110 + rand(-5, 5),
            ],
            'EUR' => [
                'USD' => 1.18 + (rand(-50, 50) / 1000),
                'VND' => 27000 + rand(-1000, 1000),
                'GBP' => 0.86 + (rand(-30, 30) / 1000),
                'JPY' => 129 + rand(-5, 5),
            ],
        ];

        return $mockRates[$baseCurrency][$targetCurrency] ?? 1.0;
    }

    /**
     * Update exchange rate in database.
     */
    private function updateExchangeRate(string $baseCurrency, string $targetCurrency, float $rate): void
    {
        // Update main rate
        ExchangeRate::updateOrCreate(
            [
                'base_currency' => $baseCurrency,
                'target_currency' => $targetCurrency
            ],
            [
                'rate' => $rate,
                'fetched_at' => now()
            ]
        );

        // Update reverse rate
        ExchangeRate::updateOrCreate(
            [
                'base_currency' => $targetCurrency,
                'target_currency' => $baseCurrency
            ],
            [
                'rate' => 1 / $rate,
                'fetched_at' => now()
            ]
        );
    }
}
