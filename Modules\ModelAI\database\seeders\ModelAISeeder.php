<?php

namespace Modules\ModelAI\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\ModelAI\Models\ModelAI;
use Modules\ModelAI\Models\ModelCategory;
use Modules\ModelAI\Models\ModelProvider;

class ModelAISeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get provider IDs
        $providers = ModelProvider::whereIn('key', ['openai', 'anthropic', 'huggingface'])
            ->pluck('id', 'key')
            ->toArray();

        $models = [
            [
                'key' => 'gpt-4-turbo',
                'name' => 'GPT-4 Turbo',
                'description' => 'Most capable GPT-4 model with improved instruction following',
                'model_provider_id' => $providers['openai'] ?? null,
                'version' => 'turbo-2024-04-09',
                'api_endpoint' => 'https://api.openai.com/v1/chat/completions',
                'streaming' => true,
                'vision' => true,
                'function_calling' => true,
                'is_default' => true,
                'sort_order' => 1,
                'status' => 'active',
                'categories' => ['text-generation', 'multimodal'],
            ],
            [
                'key' => 'claude-3-sonnet',
                'name' => 'Claude 3 Sonnet',
                'description' => 'Anthropic\'s balanced model for complex tasks',
                'model_provider_id' => $providers['anthropic'] ?? null,
                'version' => '20240229',
                'api_endpoint' => 'https://api.anthropic.com/v1/messages',
                'streaming' => true,
                'vision' => true,
                'function_calling' => true,
                'is_default' => false,
                'sort_order' => 2,
                'status' => 'active',
                'categories' => ['text-generation', 'multimodal'],
            ],
            [
                'key' => 'dall-e-3',
                'name' => 'DALL-E 3',
                'description' => 'Advanced image generation model',
                'model_provider_id' => $providers['openai'] ?? null,
                'version' => '3.0',
                'api_endpoint' => 'https://api.openai.com/v1/images/generations',
                'streaming' => false,
                'vision' => false,
                'function_calling' => false,
                'is_default' => false,
                'sort_order' => 3,
                'status' => 'active',
                'categories' => ['image-generation'],
            ],
            [
                'key' => 'codellama-34b',
                'name' => 'CodeLlama 34B',
                'description' => 'Large language model specialized for code',
                'model_provider_id' => $providers['huggingface'] ?? null,
                'version' => '34b-instruct',
                'api_endpoint' => 'https://api-inference.huggingface.co/models/codellama/CodeLlama-34b-Instruct-hf',
                'streaming' => true,
                'vision' => false,
                'function_calling' => false,
                'is_default' => false,
                'sort_order' => 4,
                'status' => 'active',
                'categories' => ['code-generation'],
            ],
        ];

        foreach ($models as $modelData) {
            // Skip if provider not found
            if (empty($modelData['model_provider_id'])) {
                $this->command->warn("Skipping model {$modelData['key']} - provider not found");
                continue;
            }

            $categories = $modelData['categories'];
            unset($modelData['categories']);

            $model = ModelAI::updateOrCreate(
                ['key' => $modelData['key']],
                $modelData
            );

            // Attach categories
            if (!empty($categories)) {
                $categoryIds = ModelCategory::whereIn('key', $categories)->pluck('id');
                $model->categories()->sync($categoryIds);
            }
        }

        $this->command->info('Model AI seeded successfully.');
    }
}
