<?php

namespace Modules\Location\Http\Controllers;

use Modules\Core\Traits\ResponseTrait;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Location\Facades\LocationFacade;

class LocationController extends Controller
{
    use ResponseTrait;

    /**
     * Get all active countries for dropdown.
     */
    public function countries(Request $request): JsonResponse
    {
        return $this->successResponse(
            LocationFacade::getActiveCountriesForDropdown(),
            __('Countries retrieved successfully.')
        );
    }

    /**
     * Get states/provinces for a specific country.
     */
    public function states(Request $request, int $countryId): JsonResponse
    {
        return $this->successResponse(
            LocationFacade::getStatesForCountry($countryId),
            __('States retrieved successfully.')
        );
    }

    /**
     * Get cities for a specific state/province.
     */
    public function cities(Request $request, int $stateId): JsonResponse
    {
        return $this->successResponse(
            LocationFacade::getCitiesForState($stateId),
            __('Cities retrieved successfully.')
        );
    }

    /**
     * Get districts for a specific city.
     */
    public function districts(Request $request, int $cityId): JsonResponse
    {
        return $this->successResponse(
            LocationFacade::getDistrictsForCity($cityId),
            __('Districts retrieved successfully.')
        );
    }

    /**
     * Get hierarchical location data for cascading dropdowns.
     */
    public function hierarchical(Request $request): JsonResponse
    {
        $countryId = $request->input('country_id');
        $stateId = $request->input('state_id');
        $cityId = $request->input('city_id');

        return $this->successResponse(
            LocationFacade::getHierarchicalData($countryId, $stateId, $cityId),
            __('Hierarchical location data retrieved successfully.')
        );
    }

    /**
     * Search locations by name.
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:2|max:255',
            'type' => 'nullable|string|in:state,city,district',
            'country_id' => 'nullable|integer|exists:countries,id',
            'limit' => 'nullable|integer|min:1|max:100',
        ]);

        $results = LocationFacade::searchLocations(
            $request->input('query'),
            $request->input('type'),
            $request->input('country_id'),
            $request->input('limit', 20)
        );

        return $this->successResponse(
            $results,
            __('Location search completed successfully.')
        );
    }

    /**
     * Get a specific country by ISO code.
     */
    public function country(string $isoCode): JsonResponse
    {
        $country = LocationFacade::findCountryByIsoCode($isoCode);

        if (!$country) {
            return $this->notFoundResponse(null, __('Country not found.'));
        }

        return $this->successResponse($country, __('Country retrieved successfully.'));
    }

    /**
     * Get a specific geographic division by ID.
     */
    public function division(int $id): JsonResponse
    {
        $division = LocationFacade::findGeoDivisionById($id);

        if (!$division) {
            return $this->notFoundResponse(null, __('Geographic division not found.'));
        }

        return $this->successResponse($division, __('Geographic division retrieved successfully.'));
    }

    /**
     * Get all geographic divisions for a country.
     */
    public function countryDivisions(Request $request, int $countryId): JsonResponse
    {
        $type = $request->input('type');

        return $this->successResponse(
            LocationFacade::getGeoDivisionsForCountry($countryId, $type),
            __('Geographic divisions retrieved successfully.')
        );
    }
}
