<?php

namespace Modules\ModelAI\Observers;

use Illuminate\Support\Facades\Cache;
use Modules\ModelAI\Models\ModelAI;

class ModelAIObserver
{
    /**
     * Cache tags for model AI
     */
    private const CACHE_TAGS = ['model-ai'];

    /**
     * Handle the ModelAI "created" event.
     */
    public function created(ModelAI $modelAI): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelAI "updated" event.
     */
    public function updated(ModelAI $modelAI): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelAI "deleted" event.
     */
    public function deleted(ModelAI $modelAI): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelAI "restored" event.
     */
    public function restored(ModelAI $modelAI): void
    {
        $this->clearCache();
    }

    /**
     * Handle the ModelAI "force deleted" event.
     */
    public function forceDeleted(ModelAI $modelAI): void
    {
        $this->clearCache();
    }

    /**
     * Clear model AI related cache.
     */
    private function clearCache(): void
    {
        try {
            if (function_exists('enabledCache') && enabledCache()) {
                Cache::tags(self::CACHE_TAGS)->flush();
            }
        } catch (\Exception $e) {
            // Silently handle cache errors
        }
    }
}
