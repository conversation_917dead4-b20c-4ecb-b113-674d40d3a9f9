<?php

namespace Modules\ModelAI\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Modules\ModelAI\Models\ModelTool;
use Modules\ModelAI\Models\ModelCategory;

class ModelToolSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tools = [
            // AI Native Tools
            [
                'uuid' => Str::uuid(),
                'key' => 'web_search',
                'name' => 'Web Search',
                'description' => 'Search the web for current information',
                'type' => 'ai_native',
                'provider' => 'openai',
                'version' => '1.0',
                'api_endpoint' => null,
                'configuration' => [
                    'max_results' => 10,
                    'timeout' => 30,
                ],
                'input_schema' => [
                    'type' => 'object',
                    'properties' => [
                        'query' => ['type' => 'string'],
                        'max_results' => ['type' => 'integer', 'default' => 5]
                    ],
                    'required' => ['query']
                ],
                'output_schema' => [
                    'type' => 'object',
                    'properties' => [
                        'results' => [
                            'type' => 'array',
                            'items' => [
                                'type' => 'object',
                                'properties' => [
                                    'title' => ['type' => 'string'],
                                    'url' => ['type' => 'string'],
                                    'snippet' => ['type' => 'string']
                                ]
                            ]
                        ]
                    ]
                ],
                'is_enabled' => true,
                'is_public' => true,
                'sort_order' => 1,
                'status' => 'active',
                'categories' => ['web-tools'],
            ],
            [
                'uuid' => Str::uuid(),
                'key' => 'code_interpreter',
                'name' => 'Code Interpreter',
                'description' => 'Execute Python code and analyze data',
                'type' => 'ai_native',
                'provider' => 'openai',
                'version' => '1.0',
                'api_endpoint' => null,
                'configuration' => [
                    'timeout' => 120,
                    'memory_limit' => '512MB',
                ],
                'input_schema' => [
                    'type' => 'object',
                    'properties' => [
                        'code' => ['type' => 'string'],
                        'language' => ['type' => 'string', 'default' => 'python']
                    ],
                    'required' => ['code']
                ],
                'output_schema' => [
                    'type' => 'object',
                    'properties' => [
                        'result' => ['type' => 'string'],
                        'output' => ['type' => 'string'],
                        'error' => ['type' => 'string']
                    ]
                ],
                'is_enabled' => true,
                'is_public' => true,
                'sort_order' => 2,
                'status' => 'active',
                'categories' => ['code-tools'],
            ],
            [
                'uuid' => Str::uuid(),
                'key' => 'dall_e',
                'name' => 'DALL-E Image Generation',
                'description' => 'Generate images from text descriptions',
                'type' => 'ai_native',
                'provider' => 'openai',
                'version' => '3.0',
                'api_endpoint' => null,
                'configuration' => [
                    'size' => '1024x1024',
                    'quality' => 'standard',
                ],
                'input_schema' => [
                    'type' => 'object',
                    'properties' => [
                        'prompt' => ['type' => 'string'],
                        'size' => ['type' => 'string', 'default' => '1024x1024'],
                        'quality' => ['type' => 'string', 'default' => 'standard']
                    ],
                    'required' => ['prompt']
                ],
                'output_schema' => [
                    'type' => 'object',
                    'properties' => [
                        'image_url' => ['type' => 'string'],
                        'revised_prompt' => ['type' => 'string']
                    ]
                ],
                'is_enabled' => true,
                'is_public' => false,
                'sort_order' => 3,
                'status' => 'active',
                'categories' => ['integration-tools'],
            ],

            // Custom Tools
            [
                'uuid' => Str::uuid(),
                'key' => 'weather_api',
                'name' => 'Weather API',
                'description' => 'Get current weather information',
                'type' => 'custom',
                'provider' => 'custom',
                'version' => '1.0',
                'api_endpoint' => 'https://api.openweathermap.org/data/2.5/weather',
                'configuration' => [
                    'api_key_required' => true,
                    'timeout' => 10,
                ],
                'input_schema' => [
                    'type' => 'object',
                    'properties' => [
                        'location' => ['type' => 'string'],
                        'units' => ['type' => 'string', 'default' => 'metric']
                    ],
                    'required' => ['location']
                ],
                'output_schema' => [
                    'type' => 'object',
                    'properties' => [
                        'temperature' => ['type' => 'number'],
                        'description' => ['type' => 'string'],
                        'humidity' => ['type' => 'number']
                    ]
                ],
                'is_enabled' => true,
                'is_public' => true,
                'sort_order' => 4,
                'status' => 'active',
                'categories' => ['data-tools'],
            ],
            [
                'uuid' => Str::uuid(),
                'key' => 'database_query',
                'name' => 'Database Query Tool',
                'description' => 'Execute database queries safely',
                'type' => 'custom',
                'provider' => 'custom',
                'version' => '1.0',
                'api_endpoint' => 'https://api.example.com/db/query',
                'configuration' => [
                    'read_only' => true,
                    'timeout' => 30,
                ],
                'input_schema' => [
                    'type' => 'object',
                    'properties' => [
                        'query' => ['type' => 'string'],
                        'database' => ['type' => 'string']
                    ],
                    'required' => ['query']
                ],
                'output_schema' => [
                    'type' => 'object',
                    'properties' => [
                        'rows' => ['type' => 'array'],
                        'count' => ['type' => 'integer']
                    ]
                ],
                'is_enabled' => true,
                'is_public' => false,
                'sort_order' => 5,
                'status' => 'active',
                'categories' => ['data-tools'],
            ],
        ];

        foreach ($tools as $toolData) {
            $categories = $toolData['categories'];
            unset($toolData['categories']);

            $tool = ModelTool::updateOrCreate(
                ['key' => $toolData['key']],
                $toolData
            );

            // Attach categories
            if (!empty($categories)) {
                $categoryIds = ModelCategory::whereIn('key', $categories)->pluck('id');
                $tool->categories()->sync($categoryIds);
            }
        }
    }
}
