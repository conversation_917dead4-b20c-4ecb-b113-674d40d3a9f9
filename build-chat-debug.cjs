#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const distDir = path.join(__dirname, 'dist', 'widget');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

const debugChatWidget = `
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ProcmsChatbot = {}));
})(this, (function (exports) {
  'use strict';

  // Debug logging
  function debugLog(message, data = null) {
    console.log('[ProcMS Debug]', message, data || '');
  }

  class EventEmitter {
    constructor() { 
      this.events = {};
      debugLog('EventEmitter created');
    }
    on(event, callback) {
      if (!this.events[event]) this.events[event] = [];
      this.events[event].push(callback);
      debugLog('Event listener added:', event);
    }
    off(event, callback) {
      if (!this.events[event]) return;
      if (callback) {
        this.events[event] = this.events[event].filter(cb => cb !== callback);
      } else {
        this.events[event] = [];
      }
      debugLog('Event listener removed:', event);
    }
    emit(event, ...args) {
      if (!this.events[event]) return;
      debugLog('Event emitted:', event, args);
      this.events[event].forEach(callback => {
        try { 
          callback(...args); 
        } catch (error) { 
          console.error('Event callback error:', error);
        }
      });
    }
  }

  class ChatAPI {
    constructor(config) {
      this.config = config;
      this.conversationId = null;
      this.messages = [];
      debugLog('ChatAPI initialized', config);
    }

    async sendMessage(content) {
      debugLog('Sending message:', content);
      
      try {
        // Create user message
        const userMessage = {
          id: Date.now().toString(),
          type: 'user',
          content: content,
          timestamp: new Date(),
          conversationId: this.conversationId || 'conv_' + Date.now()
        };

        this.conversationId = userMessage.conversationId;
        this.messages.push(userMessage);
        debugLog('User message created:', userMessage);

        // Simulate API delay
        const delay = 1000 + Math.random() * 2000;
        debugLog('Simulating API delay:', delay + 'ms');
        await new Promise(resolve => setTimeout(resolve, delay));

        // Generate bot response
        const botResponse = this.generateBotResponse(content);
        debugLog('Bot response generated:', botResponse);
        
        const botMessage = {
          id: (Date.now() + 1).toString(),
          type: 'bot',
          content: botResponse,
          timestamp: new Date(),
          conversationId: this.conversationId
        };

        this.messages.push(botMessage);
        debugLog('Bot message created:', botMessage);

        return { userMessage, botMessage };
      } catch (error) {
        console.error('ChatAPI sendMessage error:', error);
        throw error;
      }
    }

    generateBotResponse(userInput) {
      debugLog('Generating response for input:', userInput);
      
      const input = userInput.toLowerCase();
      let response = '';
      
      // Vietnamese greetings
      if (input.includes('xin chào') || input.includes('chào') || input.includes('hello') || input.includes('hi')) {
        response = "Xin chào! 👋 Tôi là trợ lý AI của bạn. Tôi có thể giúp gì cho bạn hôm nay?";
      }
      // Vietnamese help
      else if (input.includes('giúp') || input.includes('hỗ trợ') || input.includes('help')) {
        response = "Tôi có thể giúp bạn về:\\n\\n• Thông tin sản phẩm\\n• Hỗ trợ kỹ thuật\\n• Câu hỏi tài khoản\\n• Tư vấn chung\\n\\nBạn muốn biết về điều gì?";
      }
      // Vietnamese product questions
      else if (input.includes('sản phẩm') || input.includes('tính năng') || input.includes('product')) {
        response = "Nền tảng của chúng tôi cung cấp các tính năng mạnh mẽ:\\n\\n✅ Chatbot được hỗ trợ AI\\n✅ Phân tích thời gian thực\\n✅ Hỗ trợ đa kênh\\n✅ Tích hợp tùy chỉnh\\n\\nBạn muốn tìm hiểu thêm về tính năng nào?";
      }
      // Vietnamese pricing
      else if (input.includes('giá') || input.includes('chi phí') || input.includes('gói') || input.includes('price')) {
        response = "Chúng tôi có các gói giá linh hoạt:\\n\\n💡 **Cơ bản**: 700,000đ/tháng\\n🚀 **Chuyên nghiệp**: 2,300,000đ/tháng\\n🏢 **Doanh nghiệp**: Giá tùy chỉnh\\n\\nMỗi gói bao gồm các tính năng và giới hạn sử dụng khác nhau. Bạn có muốn tôi giúp chọn gói phù hợp?";
      }
      // Test messages
      else if (input.includes('test') || input.includes('thử') || input.includes('coi')) {
        response = "Tôi đã nhận được tin nhắn test của bạn! 🎯\\n\\nWidget đang hoạt động bình thường. Bạn có thể thử:\\n\\n• Hỏi về sản phẩm\\n• Hỏi về giá cả\\n• Yêu cầu hỗ trợ\\n• Chat bình thường\\n\\nTôi sẵn sàng trò chuyện!";
      }
      // Vietnamese goodbye
      else if (input.includes('tạm biệt') || input.includes('bye') || input.includes('cảm ơn')) {
        response = "Cảm ơn bạn đã trò chuyện với tôi! 😊 Nếu có thêm câu hỏi nào, đừng ngần ngại hỏi bất cứ lúc nào. Chúc bạn một ngày tốt lành!";
      }
      // Default response
      else {
        const defaultResponses = [
          "Đó là một câu hỏi thú vị! Bạn có thể cung cấp thêm chi tiết để tôi có thể đưa ra câu trả lời tốt nhất?",
          "Tôi rất vui được giúp đỡ! Bạn có thể nói rõ hơn về những gì bạn đang tìm kiếm?",
          "Câu hỏi hay! Để đưa ra thông tin chính xác nhất, bạn có thể làm rõ khía cạnh cụ thể mà bạn quan tâm?",
          "Tôi muốn đảm bảo hiểu đúng ý bạn. Bạn có thể diễn đạt lại hoặc cung cấp thêm ngữ cảnh?",
          "Cảm ơn bạn đã hỏi! Để giúp bạn tốt nhất, thông tin cụ thể nào sẽ hữu ích nhất cho bạn?"
        ];
        
        response = defaultResponses[Math.floor(Math.random() * defaultResponses.length)] + 
                 "\\n\\nBạn cũng có thể hỏi tôi về:\\n• Tính năng sản phẩm\\n• Gói giá cả\\n• Tài liệu API\\n• Đặt lịch demo";
      }
      
      debugLog('Generated response:', response);
      return response;
    }

    getMessages() {
      return this.messages;
    }
  }

  class ChatWidget {
    constructor(config, container) {
      this.config = config;
      this.container = container;
      this.chatAPI = new ChatAPI(config);
      this.isTyping = false;
      this.messages = [];
      this.widget = null;
      this.messagesContainer = null;
      this.inputField = null;
      this.sendButton = null;
      debugLog('ChatWidget created', { config, container });
    }

    render() {
      debugLog('Rendering widget...');
      
      this.widget = document.createElement('div');
      this.widget.className = 'procms-chatbot-widget';
      this.widget.setAttribute('data-procms-theme', this.config.theme || 'light');
      this.widget.setAttribute('data-minimized', 'false');

      this.widget.innerHTML = \`
        <div class="procms-widget-header" style="
          display: flex; align-items: center; justify-content: space-between;
          height: 60px; padding: 0 16px; background: var(--procms-primary, #3b82f6);
          color: white; cursor: pointer; user-select: none; flex-shrink: 0;
        ">
          <div style="display: flex; align-items: center; gap: 12px;">
            <div style="
              width: 32px; height: 32px; border-radius: 50%;
              background: rgba(255,255,255,0.2);
              display: flex; align-items: center; justify-content: center;
            ">🤖</div>
            <div>
              <div style="font-weight: 600;">ProcMS Assistant</div>
              <div style="font-size: 12px; opacity: 0.8;" class="status-indicator">Online</div>
            </div>
          </div>
          <div style="display: flex; gap: 8px;">
            <button class="procms-minimize-btn" style="
              background: none; border: none; color: white; cursor: pointer;
              padding: 6px 10px; border-radius: 4px; font-size: 18px;
              font-weight: bold; transition: all 0.2s; line-height: 1;
            " title="Minimize">−</button>
            <button class="procms-close-btn" style="
              background: none; border: none; color: white; cursor: pointer;
              padding: 6px 10px; border-radius: 4px; font-size: 18px;
              font-weight: bold; transition: all 0.2s; line-height: 1;
            " title="Close">×</button>
          </div>
        </div>
        
        <div class="procms-messages-container" style="
          flex: 1; padding: 16px; background: var(--procms-widget-bg, #f9fafb);
          overflow-y: auto; display: flex; flex-direction: column; gap: 12px;
          min-height: 0; max-height: 400px;
        ">
          <div class="procms-message procms-message--bot" style="display: flex; gap: 8px;">
            <div style="
              width: 32px; height: 32px; border-radius: 50%;
              background: var(--procms-primary, #3b82f6); color: white;
              display: flex; align-items: center; justify-content: center;
              font-size: 14px; flex-shrink: 0;
            ">🤖</div>
            <div style="
              background: white; padding: 12px 16px; border-radius: 18px;
              border-bottom-left-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);
              max-width: 80%; color: var(--procms-text-primary, #111827);
            ">
              <div>Xin chào! 👋 Tôi là trợ lý AI của bạn. Tôi có thể giúp gì cho bạn hôm nay?</div>
              <div style="font-size: 11px; color: #666; margin-top: 4px;">
                \${new Date().toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>
        
        <div class="procms-widget-footer" style="
          padding: 16px; background: var(--procms-widget-surface, white);
          border-top: 1px solid var(--procms-widget-border, #e5e7eb);
          flex-shrink: 0;
        ">
          <div style="display: flex; gap: 8px; align-items: flex-end;">
            <div style="flex: 1;">
              <textarea class="procms-input" placeholder="Nhập tin nhắn của bạn..." style="
                width: 100%; padding: 12px 16px; border: 1px solid var(--procms-widget-border, #e5e7eb);
                border-radius: 24px; outline: none; font-size: 14px; resize: none;
                background: var(--procms-widget-bg, #f9fafb); color: var(--procms-text-primary, #111827);
                font-family: inherit; line-height: 1.4; min-height: 44px; max-height: 120px;
                box-sizing: border-box;
              "></textarea>
            </div>
            <button class="procms-send-btn" style="
              width: 44px; height: 44px; border-radius: 50%;
              background: var(--procms-primary, #3b82f6); color: white;
              border: none; cursor: pointer; display: flex;
              align-items: center; justify-content: center;
              font-size: 16px; transition: all 0.2s; flex-shrink: 0;
            " disabled>
              <span class="send-icon">→</span>
              <span class="loading-icon" style="display: none;">⏳</span>
            </button>
          </div>
        </div>
      \`;

      this.widget.style.cssText = \`
        display: flex; flex-direction: column; height: 100%;
        border-radius: 8px; overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      \`;

      this.container.appendChild(this.widget);
      debugLog('Widget HTML added to container');
      
      this.setupEventListeners();
      this.setupAutoResize();
      
      debugLog('Widget rendered successfully');
      return this.widget;
    }

    setupEventListeners() {
      debugLog('Setting up event listeners...');
      
      this.messagesContainer = this.widget.querySelector('.procms-messages-container');
      this.inputField = this.widget.querySelector('.procms-input');
      this.sendButton = this.widget.querySelector('.procms-send-btn');

      if (!this.messagesContainer || !this.inputField || !this.sendButton) {
        console.error('Failed to find widget elements:', {
          messagesContainer: !!this.messagesContainer,
          inputField: !!this.inputField,
          sendButton: !!this.sendButton
        });
        return;
      }

      // Send message on button click
      this.sendButton.addEventListener('click', () => {
        debugLog('Send button clicked');
        this.sendMessage();
      });

      // Send message on Enter
      this.inputField.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          debugLog('Enter key pressed');
          this.sendMessage();
        }
      });

      // Enable/disable send button
      this.inputField.addEventListener('input', () => {
        const hasText = this.inputField.value.trim().length > 0;
        this.sendButton.disabled = !hasText || this.isTyping;
        this.sendButton.style.opacity = hasText && !this.isTyping ? '1' : '0.5';
      });

      debugLog('Event listeners set up successfully');
    }

    setupAutoResize() {
      this.inputField.addEventListener('input', () => {
        this.inputField.style.height = 'auto';
        this.inputField.style.height = Math.min(this.inputField.scrollHeight, 120) + 'px';
      });
    }

    async sendMessage() {
      const content = this.inputField.value.trim();
      debugLog('sendMessage called with content:', content);
      
      if (!content || this.isTyping) {
        debugLog('Message not sent - empty content or typing in progress');
        return;
      }

      // Clear input and disable send button
      this.inputField.value = '';
      this.inputField.style.height = '44px';
      this.isTyping = true;
      this.updateSendButton();

      try {
        debugLog('Adding user message to UI...');
        // Add user message immediately
        this.addMessage({
          type: 'user',
          content: content,
          timestamp: new Date()
        });

        // Show typing indicator
        debugLog('Showing typing indicator...');
        this.showTypingIndicator();

        // Send to API and get response
        debugLog('Calling ChatAPI.sendMessage...');
        const result = await this.chatAPI.sendMessage(content);
        debugLog('ChatAPI response received:', result);

        // Remove typing indicator and add bot response
        debugLog('Hiding typing indicator and adding bot message...');
        this.hideTypingIndicator();
        this.addMessage(result.botMessage);

      } catch (error) {
        console.error('Chat error:', error);
        this.hideTypingIndicator();
        this.addMessage({
          type: 'bot',
          content: 'Xin lỗi, tôi gặp lỗi. Vui lòng thử lại.',
          timestamp: new Date()
        });
      } finally {
        this.isTyping = false;
        this.updateSendButton();
        this.inputField.focus();
        debugLog('sendMessage completed');
      }
    }

    addMessage(message) {
      debugLog('Adding message to UI:', message);
      
      const messageEl = document.createElement('div');
      messageEl.className = \`procms-message procms-message--\${message.type}\`;
      
      if (message.type === 'user') {
        messageEl.innerHTML = \`
          <div style="display: flex; gap: 8px; justify-content: flex-end;">
            <div style="
              background: var(--procms-primary, #3b82f6); color: white;
              padding: 12px 16px; border-radius: 18px; border-bottom-right-radius: 4px;
              max-width: 80%; word-wrap: break-word; white-space: pre-wrap;
            ">
              <div>\${this.escapeHtml(message.content)}</div>
              <div style="font-size: 11px; opacity: 0.8; margin-top: 4px;">
                \${message.timestamp.toLocaleTimeString()}
              </div>
            </div>
            <div style="
              width: 32px; height: 32px; border-radius: 50%;
              background: var(--procms-primary, #3b82f6); color: white;
              display: flex; align-items: center; justify-content: center;
              font-size: 14px; flex-shrink: 0;
            ">👤</div>
          </div>
        \`;
      } else {
        messageEl.innerHTML = \`
          <div style="display: flex; gap: 8px;">
            <div style="
              width: 32px; height: 32px; border-radius: 50%;
              background: var(--procms-primary, #3b82f6); color: white;
              display: flex; align-items: center; justify-content: center;
              font-size: 14px; flex-shrink: 0;
            ">🤖</div>
            <div style="
              background: white; padding: 12px 16px; border-radius: 18px;
              border-bottom-left-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);
              max-width: 80%; color: var(--procms-text-primary, #111827);
              word-wrap: break-word; white-space: pre-wrap;
            ">
              <div>\${this.escapeHtml(message.content)}</div>
              <div style="font-size: 11px; color: #666; margin-top: 4px;">
                \${message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        \`;
      }

      this.messagesContainer.appendChild(messageEl);
      this.scrollToBottom();
      debugLog('Message added to UI successfully');
    }

    showTypingIndicator() {
      debugLog('Showing typing indicator...');
      
      const typingEl = document.createElement('div');
      typingEl.className = 'procms-typing-indicator';
      typingEl.innerHTML = \`
        <div style="display: flex; gap: 8px;">
          <div style="
            width: 32px; height: 32px; border-radius: 50%;
            background: var(--procms-primary, #3b82f6); color: white;
            display: flex; align-items: center; justify-content: center;
            font-size: 14px; flex-shrink: 0;
          ">🤖</div>
          <div style="
            background: white; padding: 12px 16px; border-radius: 18px;
            border-bottom-left-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            color: var(--procms-text-primary, #111827);
          ">
            <div style="display: flex; gap: 4px; align-items: center;">
              <span>Đang nhập</span>
              <div style="display: flex; gap: 2px;">
                <div style="width: 4px; height: 4px; border-radius: 50%; background: #666; animation: typing-bounce 1.4s infinite ease-in-out;"></div>
                <div style="width: 4px; height: 4px; border-radius: 50%; background: #666; animation: typing-bounce 1.4s infinite ease-in-out 0.2s;"></div>
                <div style="width: 4px; height: 4px; border-radius: 50%; background: #666; animation: typing-bounce 1.4s infinite ease-in-out 0.4s;"></div>
              </div>
            </div>
          </div>
        </div>
      \`;

      this.messagesContainer.appendChild(typingEl);
      this.scrollToBottom();

      const statusIndicator = this.widget.querySelector('.status-indicator');
      if (statusIndicator) {
        statusIndicator.textContent = 'Đang nhập...';
      }
    }

    hideTypingIndicator() {
      debugLog('Hiding typing indicator...');
      
      const typingEl = this.widget.querySelector('.procms-typing-indicator');
      if (typingEl) {
        typingEl.remove();
      }

      const statusIndicator = this.widget.querySelector('.status-indicator');
      if (statusIndicator) {
        statusIndicator.textContent = 'Trực tuyến';
      }
    }

    updateSendButton() {
      const sendIcon = this.sendButton.querySelector('.send-icon');
      const loadingIcon = this.sendButton.querySelector('.loading-icon');
      
      if (this.isTyping) {
        sendIcon.style.display = 'none';
        loadingIcon.style.display = 'block';
        this.sendButton.disabled = true;
      } else {
        sendIcon.style.display = 'block';
        loadingIcon.style.display = 'none';
        this.sendButton.disabled = this.inputField.value.trim().length === 0;
      }
      
      this.sendButton.style.opacity = this.sendButton.disabled ? '0.5' : '1';
    }

    scrollToBottom() {
      setTimeout(() => {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
      }, 100);
    }

    escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    toggleMinimize() {
      // Minimize functionality (same as before)
    }
  }

  class ProcmsChatbotWidget extends EventEmitter {
    constructor(config) {
      super();
      if (!config.botUuid) throw new Error('Bot UUID is required');
      if (!config.apiKey) throw new Error('API Key is required');
      this.config = config;
      this.mounted = false;
      this.chatWidget = null;
      debugLog('ProcmsChatbotWidget created', config);
    }

    async mount(selector) {
      debugLog('Mounting widget to selector:', selector);
      
      const container = typeof selector === 'string' ? document.querySelector(selector) : selector;
      if (!container) {
        const error = 'Container not found: ' + selector;
        debugLog('Mount failed:', error);
        throw new Error(error);
      }

      this.container = container;
      this.chatWidget = new ChatWidget(this.config, container);
      this.chatWidget.onClose = () => this.unmount();
      this.chatWidget.originalHeight = container.style.height || '600px';
      
      this.chatWidget.render();
      this.mounted = true;

      debugLog('Widget mounted successfully');
      this.emit('ready');
      if (this.config.onReady) this.config.onReady();
    }

    unmount() {
      debugLog('Unmounting widget...');
      if (this.chatWidget && this.chatWidget.widget) {
        this.chatWidget.widget.remove();
      }
      this.mounted = false;
      this.emit('unmounted');
      debugLog('Widget unmounted');
    }

    isMounted() { return this.mounted; }
  }

  class ProcmsChatbot {
    static async create(config, selector) {
      debugLog('ProcmsChatbot.create called', { config, selector });
      const widget = new ProcmsChatbotWidget(config);
      await widget.mount(selector);
      return widget;
    }

    static async createFloatingWidget(config) {
      debugLog('ProcmsChatbot.createFloatingWidget called', config);
      const container = document.createElement('div');
      container.id = 'procms-floating-widget';
      container.style.cssText = \`
        position: fixed; bottom: 20px; right: 20px;
        width: 400px; height: 600px; z-index: 9999;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        border-radius: 8px; overflow: hidden;
      \`;
      document.body.appendChild(container);

      const widget = new ProcmsChatbotWidget(config);
      await widget.mount(container);
      return widget;
    }
  }

  // Add CSS animations
  const style = document.createElement('style');
  style.textContent = \`
    @keyframes typing-bounce {
      0%, 60%, 100% { transform: translateY(0); }
      30% { transform: translateY(-10px); }
    }
  \`;
  document.head.appendChild(style);

  exports.ProcmsChatbotWidget = ProcmsChatbotWidget;
  exports.ProcmsChatbot = ProcmsChatbot;

  if (typeof window !== 'undefined') {
    window.ProcmsChatbotWidget = ProcmsChatbotWidget;
    window.ProcmsChatbot = ProcmsChatbot;
    debugLog('Global objects assigned to window');
  }

  debugLog('ProcMS Chatbot library loaded successfully');

}));
`;

fs.writeFileSync(path.join(distDir, 'procms-chatbot.umd.js'), debugChatWidget);
console.log('✅ Debug Chat Widget built!');
console.log('🔍 Features:');
console.log('  - Extensive debug logging');
console.log('  - Vietnamese language support');
console.log('  - Error handling improvements');
console.log('  - Better element validation');
console.log('  - Fixed async/await issues');
