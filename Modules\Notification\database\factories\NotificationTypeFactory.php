<?php

namespace Modules\Notification\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Notification\Models\NotificationType;

class NotificationTypeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = NotificationType::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $channels = ['database', 'email', 'sms', 'push', 'telegram'];
        $selectedChannels = $this->faker->randomElements($channels, $this->faker->numberBetween(1, 3));

        return [
            'key' => $this->faker->unique()->slug(2),
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->sentence(),
            'channels' => $selectedChannels,
            'default_settings' => [
                'priority' => $this->faker->randomElement(['low', 'normal', 'high', 'urgent']),
                'auto_send' => $this->faker->boolean(),
                'retry_attempts' => $this->faker->numberBetween(1, 5),
            ],
            'is_system' => false,
            'status' => 'active',
        ];
    }

    /**
     * Indicate that the notification type is a system type.
     */
    public function system(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_system' => true,
        ]);
    }

    /**
     * Indicate that the notification type is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the notification type is for email only.
     */
    public function emailOnly(): static
    {
        return $this->state(fn (array $attributes) => [
            'channels' => ['email'],
        ]);
    }

    /**
     * Indicate that the notification type supports all channels.
     */
    public function allChannels(): static
    {
        return $this->state(fn (array $attributes) => [
            'channels' => ['database', 'email', 'sms', 'push', 'telegram'],
        ]);
    }

    /**
     * Indicate that the notification type has high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'default_settings' => array_merge($attributes['default_settings'] ?? [], [
                'priority' => 'high',
                'auto_send' => true,
            ]),
        ]);
    }

    /**
     * Create a welcome notification type.
     */
    public function welcome(): static
    {
        return $this->state(fn (array $attributes) => [
            'key' => 'welcome',
            'name' => 'Welcome Notification',
            'description' => 'Welcome message sent to new users',
            'channels' => ['database', 'email'],
            'is_system' => true,
        ]);
    }

    /**
     * Create a password reset notification type.
     */
    public function passwordReset(): static
    {
        return $this->state(fn (array $attributes) => [
            'key' => 'password_reset',
            'name' => 'Password Reset',
            'description' => 'Password reset notifications',
            'channels' => ['email'],
            'is_system' => true,
        ]);
    }

    /**
     * Create an email verification notification type.
     */
    public function emailVerification(): static
    {
        return $this->state(fn (array $attributes) => [
            'key' => 'email_verification',
            'name' => 'Email Verification',
            'description' => 'Email address verification',
            'channels' => ['email'],
            'is_system' => true,
        ]);
    }
}
