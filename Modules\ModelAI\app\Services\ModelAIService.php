<?php

namespace Modules\ModelAI\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;
use Modules\ModelAI\Models\ModelCategory;
use Modules\ModelAI\Models\ModelAI;
use Modules\ModelAI\Models\ModelTool;

class ModelAIService
{
    /**
     * Cache tags for model AI data - using consistent naming convention
     */
    private const CACHE_TAG_CATEGORIES = 'model-categories';
    private const CACHE_TAG_MODELS = 'model-ai';
    private const CACHE_TAG_PROVIDERS = 'model-providers';  // Fixed: consistent with others
    private const CACHE_TAG_TOOLS = 'model-tools';
    private const CACHE_TAG_SERVICES = 'model-services';    // Added: for completeness

    /**
     * Get active AI models for public API.
     */
    public function getActiveModels(): Collection
    {
        return Cache::tags(self::CACHE_TAG_MODELS)->remember(
            key: 'modelai.models.active.public',
            ttl: now()->addMinutes(20), // Longer TTL for public data
            callback: function () {
                return ModelAI::query()
                    ->active()
                    ->with([
                        'provider:id,name,key',
                        'categories:id,name,key'
                    ])
                    ->ordered()
                    ->select([
                        'id', 'key', 'name', 'description', 'model_provider_id',
                        'version', 'streaming', 'vision', 'function_calling',
                        'is_default', 'status', 'sort_order'
                    ])
                    ->get();
            }
        );
    }

    /**
     * Get the default AI model.
     */
    public function getDefaultModel(): ?ModelAI
    {
        return Cache::tags(self::CACHE_TAG_MODELS)->remember(
            key: 'modelai.model.default',
            ttl: now()->addMinutes(30), // Longer TTL for default model
            callback: function () {
                return ModelAI::query()
                    ->with([
                        'provider:id,name,key,description,base_url,api_key,credentials',
                        'categories:id,name,key',
                        'services:id,model_ai_id,max_tokens,context_window,cost_per1k_input,cost_per1k_output,rate_limit_rpm,default_parameters'
                    ])
                    ->active()
                    ->where('is_default', true)
                    ->first();
            }
        );
    }

    /**
     * Find AI model by key for public API.
     */
    public function findByKey(string $key): ?ModelAI
    {
        return Cache::tags(self::CACHE_TAG_MODELS)->remember(
            key: "modelai.model.public.{$key}",
            ttl: now()->addMinutes(15), // Increased TTL for relatively static data
            callback: function () use ($key) {
                return ModelAI::query()
                    ->where('key', $key)
                    ->active()
                    ->with([
                        'provider:id,name,key',
                        'categories:id,name,key',
                        'services:id,model_ai_id,max_tokens,context_window,cost_per1k_input,cost_per1k_output'
                    ])
                    ->first();
            }
        );
    }

    /**
     * Get active categories for dropdown.
     */
    public function getCategoriesForDropdown(string $locale): Collection
    {
        return Cache::tags(self::CACHE_TAG_CATEGORIES)->remember(
            key: 'modelai.categories.dropdown',
            ttl: now()->addMinutes(5),
            callback: function () {
                return ModelCategory::query()
                    ->active()
                    ->ordered()
                    ->select(['id', 'key', 'name', 'icon', 'color'])
                    ->get();
            }
        );
    }

    /**
     * Get active AI models for dropdown.
     */
    public function getModelsForDropdown(string $locale): Collection
    {
        return Cache::tags(self::CACHE_TAG_MODELS)->remember(
            key: 'modelai.models.dropdown',
            ttl: now()->addMinutes(5),
            callback: function () {
                return ModelAI::query()
                    ->active()
                    ->with(['provider:id,key,name'])
                    ->orderBy('name')
                    ->select(['id', 'key', 'name', 'model_provider_id'])
                    ->get()
                    ->map(function ($model) {
                        return [
                            'value' => $model->key,
                            'label' => $model->name,
                            'provider' => $model->provider->name,
                        ];
                    });
            }
        );
    }

    /**
     * Get categories with their associated models.
     */
    public function getCategoriesWithModels(string $locale): Collection
    {
        return Cache::tags([self::CACHE_TAG_CATEGORIES, self::CACHE_TAG_MODELS])->remember(
            key: 'modelai.categories.with_models',
            ttl: now()->addMinutes(5),
            callback: function () {
                return ModelCategory::query()
                    ->active()
                    ->with(['modelAIs' => function ($query) {
                        $query->active()->ordered()->with(['provider:id,key,name'])->select(['id', 'key', 'name', 'model_provider_id', 'is_default']);
                    }])
                    ->ordered()
                    ->get();
            }
        );
    }

    /**
     * Get AI models with their associated categories.
     */
    public function getModelsWithCategories(string $locale): Collection
    {
        return Cache::tags([self::CACHE_TAG_MODELS, self::CACHE_TAG_CATEGORIES])->remember(
            key: 'modelai.models.with_categories',
            ttl: now()->addMinutes(5),
            callback: function () {
                return ModelAI::query()
                    ->active()
                    ->with(['categories' => function ($query) {
                        $query->active()->ordered()->select(['id', 'key', 'name', 'icon', 'color']);
                    }])
                    ->ordered()
                    ->get();
            }
        );
    }

    /**
     * Get active tools for dropdown.
     */
    public function getToolsForDropdown(string $locale): Collection
    {
        return Cache::tags(self::CACHE_TAG_TOOLS)->remember(
            key: 'modelai.tools.dropdown',
            ttl: now()->addMinutes(5),
            callback: function () {
                return ModelTool::query()
                    ->active()
                    ->enabled()
                    ->ordered()
                    ->select(['id', 'uuid', 'key', 'name', 'type', 'is_public'])
                    ->get();
            }
        );
    }

    /**
     * Get categories with their associated tools.
     */
    public function getCategoriesWithTools(string $locale): Collection
    {
        return Cache::tags([self::CACHE_TAG_CATEGORIES, self::CACHE_TAG_TOOLS])->remember(
            key: 'modelai.categories.with_tools',
            ttl: now()->addMinutes(5),
            callback: function () {
                return ModelCategory::query()
                    ->tools()
                    ->active()
                    ->with(['tools' => function ($query) {
                        $query->active()->enabled()->ordered()->select(['id', 'uuid', 'key', 'name', 'type', 'is_public']);
                    }])
                    ->ordered()
                    ->get();
            }
        );
    }

    /**
     * Get AI models with their associated tools.
     */
    public function getModelsWithTools(string $locale): Collection
    {
        return Cache::tags([self::CACHE_TAG_MODELS, self::CACHE_TAG_TOOLS])->remember(
            key: 'modelai.models.with_tools',
            ttl: now()->addMinutes(5),
            callback: function () {
                return ModelAI::query()
                    ->active()
                    ->with(['tools' => function ($query) {
                        $query->where('model_tools.status', 'active')
                            ->where('model_tools.is_enabled', true)
                            ->wherePivot('is_enabled', true)
                            ->orderByPivot('priority', 'desc')
                            ->select(['model_tools.id', 'uuid', 'key', 'name', 'type', 'is_public']);
                    }])
                    ->ordered()
                    ->get();
            }
        );
    }

    /**
     * Get available tools for a specific model.
     */
    public function getAvailableToolsForModel(int $modelId): Collection
    {
        $cacheKey = "modelai.model.{$modelId}.available_tools";

        return Cache::tags([self::CACHE_TAG_MODELS, self::CACHE_TAG_TOOLS])->remember(
            key: $cacheKey,
            ttl: now()->addMinutes(5),
            callback: function () use ($modelId) {
                $model = ModelAI::find($modelId);

                if (!$model) {
                    return collect();
                }

                return $model->getAllAvailableTools();
            }
        );
    }

    /**
     * Attach tool to model with configuration.
     */
    public function attachToolToModel(int $modelId, int $toolId, array $config = []): bool
    {
        try {
            $model = ModelAI::findOrFail($modelId);
            $tool = ModelTool::findOrFail($toolId);

            // Check if already attached
            if ($model->tools()->where('model_tool_id', $toolId)->exists()) {
                return false;
            }

            $model->tools()->attach($toolId, array_merge([
                'is_enabled' => true,
                'priority' => 0,
                'configuration' => null,
                'max_usage_per_request' => null,
                'rate_limit_per_minute' => null,
            ], $config));

            // Clear cache
            $this->clearModelToolsCache($modelId);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Detach tool from model.
     */
    public function detachToolFromModel(int $modelId, int $toolId): bool
    {
        try {
            $model = ModelAI::findOrFail($modelId);
            $model->tools()->detach($toolId);

            // Clear cache
            $this->clearModelToolsCache($modelId);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Clear model-specific tools cache.
     */
    private function clearModelToolsCache(int $modelId): void
    {
        try {
            if (function_exists('enabledCache') && enabledCache()) {
                Cache::tags([self::CACHE_TAG_MODELS, self::CACHE_TAG_TOOLS])->flush();
                Cache::forget("modelai.model.{$modelId}.available_tools");
            }
        } catch (\Exception $e) {
            // Silently handle cache errors
        }
    }

    /**
     * Get active providers for dropdown.
     */
    public function getProvidersForDropdown(): Collection
    {
        return Cache::tags(self::CACHE_TAG_PROVIDERS)->remember(
            key: 'modelai.providers.dropdown',
            ttl: now()->addMinutes(30),
            callback: function () {
                return \Modules\ModelAI\Models\ModelProvider::active()
                    ->orderBy('name')
                    ->get(['id', 'key', 'name']);
            }
        );
    }

    /**
     * Get providers with their models count.
     */
    public function getProvidersWithModelsCount(): Collection
    {
        return Cache::tags([self::CACHE_TAG_PROVIDERS, self::CACHE_TAG_MODELS])->remember(
            key: 'modelai.providers.with_models_count',
            ttl: now()->addMinutes(15),
            callback: function () {
                return \Modules\ModelAI\Models\ModelProvider::withCount([
                    'modelAIs',
                    'modelAIs as active_models_count' => function ($query) {
                        $query->where('status', 'active');
                    }
                ])
                    ->orderBy('name')
                    ->get();
            }
        );
    }

    /**
     * Get models with pagination for public API.
     */
    public function getModelsWithPagination($request, string $locale): array
    {
        $perPage = $request->input('limit', 10);
        $models = ModelAI::query()
            ->with(['provider', 'categories', 'services'])
            ->active()
            ->ordered()
            ->paginate($perPage);

        return [
            'data' => $models->items(),
            'total' => $models->total(),
            'limit' => $models->perPage(),
            'current_page' => $models->currentPage(),
            'last_page' => $models->lastPage(),
        ];
    }

    /**
     * Get model by key for admin/detailed view.
     */
    public function getModelByKey(string $key): ?ModelAI
    {
        return Cache::tags(self::CACHE_TAG_MODELS)->remember(
            key: "modelai.model.admin.{$key}",
            ttl: now()->addMinutes(10),
            callback: function () use ($key) {
                return ModelAI::query()
                    ->where('key', $key)
                    ->active()
                    ->with([
                        'provider:id,name,key,description',
                        'categories:id,name,key,description',
                        'services:id,model_ai_id,max_tokens,context_window,cost_per1k_input,cost_per1k_output,rate_limit_rpm'
                    ])
                    ->first();
            }
        );
    }

    /**
     * Alias for getDefaultModel() for backward compatibility.
     * @deprecated Use getDefaultModel() instead
     */
    public function getModelAIDefault(): ?ModelAI
    {
        return $this->getDefaultModel();
    }
}
