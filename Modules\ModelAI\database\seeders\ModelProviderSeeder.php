<?php

namespace Modules\ModelAI\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\ModelAI\Models\ModelProvider;

class ModelProviderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $providers = [
            [
                'key' => 'openai',
                'name' => 'OpenAI',
                'description' => 'OpenAI provides cutting-edge AI models including GPT-4, GPT-3.5, DALL-E, and Whisper for various AI applications.',
                'base_url' => 'https://api.openai.com/v1/',
                'api_key' => null, // To be configured by admin
                'credentials' => null,
                'status' => 'active',
            ],
            [
                'key' => 'anthropic',
                'name' => 'Anthropic',
                'description' => 'Anthropic develops AI safety-focused language models including Claude series for helpful, harmless, and honest AI interactions.',
                'base_url' => 'https://api.anthropic.com/',
                'api_key' => null, // To be configured by admin
                'credentials' => null,
                'status' => 'active',
            ],
            [
                'key' => 'google',
                'name' => 'Google AI',
                'description' => 'Google AI offers Gemini and PaLM models for advanced language understanding and generation capabilities.',
                'base_url' => 'https://generativelanguage.googleapis.com/v1/',
                'api_key' => null, // To be configured by admin
                'credentials' => null,
                'status' => 'active',
            ],
            [
                'key' => 'cohere',
                'name' => 'Cohere',
                'description' => 'Cohere provides enterprise-grade language models for text generation, classification, and semantic search.',
                'base_url' => 'https://api.cohere.ai/v1/',
                'api_key' => null, // To be configured by admin
                'credentials' => null,
                'status' => 'active',
            ],
            [
                'key' => 'huggingface',
                'name' => 'Hugging Face',
                'description' => 'Hugging Face offers access to thousands of open-source models through their inference API.',
                'base_url' => 'https://api-inference.huggingface.co/',
                'api_key' => null, // To be configured by admin
                'credentials' => null,
                'status' => 'active',
            ],
            [
                'key' => 'azure',
                'name' => 'Azure OpenAI',
                'description' => 'Microsoft Azure OpenAI Service provides enterprise-grade OpenAI models with enhanced security and compliance.',
                'base_url' => null, // To be configured by admin (varies by deployment)
                'api_key' => null, // To be configured by admin
                'credentials' => null,
                'status' => 'inactive',
            ],
            [
                'key' => 'aws',
                'name' => 'AWS Bedrock',
                'description' => 'Amazon Bedrock provides access to foundation models from leading AI companies through a unified API.',
                'base_url' => null, // Uses AWS SDK
                'api_key' => null, // Not used for AWS (uses access keys)
                'credentials' => null,
                'status' => 'inactive',
            ],
            [
                'key' => 'ollama',
                'name' => 'Ollama',
                'description' => 'Ollama allows running large language models locally with easy setup and management.',
                'base_url' => 'http://localhost:11434/',
                'api_key' => null, // No API key needed for local Ollama
                'credentials' => null, // No credentials needed for local Ollama
                'status' => 'inactive',
            ],
            [
                'key' => 'custom',
                'name' => 'Custom Provider',
                'description' => 'Custom AI model provider for proprietary or self-hosted models.',
                'base_url' => null, // To be configured by admin
                'api_key' => null, // To be configured by admin
                'credentials' => null,
                'status' => 'inactive',
            ],
        ];

        foreach ($providers as $providerData) {
            ModelProvider::updateOrCreate(
                ['key' => $providerData['key']],
                $providerData
            );
        }

        $this->command->info('Model providers seeded successfully.');
    }
}
