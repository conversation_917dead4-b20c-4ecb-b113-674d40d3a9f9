<?php

use Illuminate\Support\Facades\Route;
use Modules\Location\Http\Controllers\Auth\CountryController as AuthCountryController;
use Modules\Location\Http\Controllers\Auth\GeoDivisionController as AuthGeoDivisionController;

Route::middleware(['auth', 'verified'])->group(function () {
    // Countries management
    Route::resource('countries', AuthCountryController::class)->names('location.countries');
    Route::get('countries/trashed/list', [AuthCountryController::class, 'trashed'])->name('location.countries.trashed');
    Route::patch('countries/{id}/restore', [AuthCountryController::class, 'restore'])->name('location.countries.restore');
    Route::delete('countries/{id}/force-delete', [AuthCountryController::class, 'forceDelete'])->name('location.countries.force-delete');

    // Geographic divisions management
    Route::resource('geo-divisions', AuthGeoDivisionController::class)->names('location.geo-divisions');
    Route::get('geo-divisions/trashed/list', [AuthGeoDivisionController::class, 'trashed'])->name('location.geo-divisions.trashed');
    Route::patch('geo-divisions/{id}/restore', [AuthGeoDivisionController::class, 'restore'])->name('location.geo-divisions.restore');
    Route::delete('geo-divisions/{id}/force-delete', [AuthGeoDivisionController::class, 'forceDelete'])->name('location.geo-divisions.force-delete');
});
