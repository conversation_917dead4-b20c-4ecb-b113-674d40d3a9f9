<?php

namespace Modules\ModelAI\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\ModelAI\Models\ModelTool;

class ModelToolFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = ModelTool::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $types = ['ai_native', 'custom', 'model'];
        $providers = ['openai', 'anthropic', 'google', 'custom', null];
        $statuses = ['active', 'inactive', 'draft'];

        return [
            'uuid' => $this->faker->uuid(),
            'key' => $this->faker->unique()->slug(2),
            'name' => $this->faker->words(2, true),
            'description' => $this->faker->optional()->sentence(),
            'type' => $this->faker->randomElement($types),
            'provider' => $this->faker->randomElement($providers),
            'version' => $this->faker->optional()->semver(),
            'api_endpoint' => $this->faker->optional()->url(),
            'configuration' => $this->faker->optional()->randomElement([
                ['temperature' => 0.7, 'max_tokens' => 1000],
                ['timeout' => 30, 'retries' => 3],
                null
            ]),
            'input_schema' => $this->faker->optional()->randomElement([
                ['type' => 'object', 'properties' => ['query' => ['type' => 'string']]],
                ['type' => 'string'],
                null
            ]),
            'output_schema' => $this->faker->optional()->randomElement([
                ['type' => 'object', 'properties' => ['result' => ['type' => 'string']]],
                ['type' => 'string'],
                null
            ]),
            'is_enabled' => $this->faker->boolean(80),
            'is_public' => $this->faker->boolean(30),
            'sort_order' => $this->faker->numberBetween(1, 100),
            'status' => $this->faker->randomElement($statuses),
        ];
    }

    /**
     * Indicate that the tool is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the tool is enabled.
     */
    public function enabled(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_enabled' => true,
        ]);
    }

    /**
     * Indicate that the tool is public.
     */
    public function public(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_public' => true,
        ]);
    }

    /**
     * Indicate that the tool is AI native.
     */
    public function aiNative(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'ai_native',
            'provider' => $this->faker->randomElement(['openai', 'anthropic', 'google']),
        ]);
    }

    /**
     * Indicate that the tool is custom.
     */
    public function custom(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'custom',
            'provider' => 'custom',
            'api_endpoint' => $this->faker->url(),
        ]);
    }

    /**
     * Indicate that the tool is model-based.
     */
    public function model(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'model',
            'provider' => null,
            'api_endpoint' => null,
        ]);
    }
}
