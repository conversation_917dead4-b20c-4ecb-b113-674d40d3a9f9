# 🔗 Real API Widget Integration Guide

## 🚀 Overview

Widget hiện tại đã được tích hợp hoàn toàn với backend API thực tế. Không còn là mock data mà sử dụng các API endpoints thật để:

- <PERSON><PERSON><PERSON> thực bot access
- Tạo và quản lý conversations
- Gửi và nhận messages
- Load existing messages
- Real-time message status polling

## 📋 API Endpoints Used

### **Authentication & Validation**
```http
GET /api/v1/widget/health
GET /api/v1/widget/bot/{uuid}/validate
GET /api/v1/widget/bot/{uuid}/config
```

### **Conversation Management**
```http
POST /api/v1/widget/conversations
GET /api/v1/widget/conversations/{uuid}
```

### **Message Management**
```http
GET /api/v1/widget/conversations/{uuid}/messages
POST /api/v1/widget/conversations/{uuid}/messages
GET /api/v1/widget/conversations/{uuid}/messages/{messageUuid}/status
```

## 🔐 Authentication

Widget sử dụng **API Key Authentication**:

```javascript
headers: {
  'Authorization': 'Bearer pk_live_your_api_key_here',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
```

## 🛠️ Usage Examples

### **1. Basic Integration**

```html
<!DOCTYPE html>
<html>
<head>
    <title>My Website</title>
</head>
<body>
    <div id="chat-widget" style="width: 400px; height: 600px;"></div>
    
    <script src="https://your-domain.com/dist/widget/procms-chatbot.umd.js"></script>
    <script>
        async function loadChat() {
            const widget = await ProcmsChatbot.create({
                botUuid: 'your-bot-uuid-here',
                apiKey: 'pk_live_your_api_key_here',
                apiBaseUrl: 'https://your-domain.com/api/v1/widget',
                userId: 'user_123', // Optional
                theme: 'light'
            }, '#chat-widget');
        }
        
        loadChat();
    </script>
</body>
</html>
```

### **2. Floating Widget**

```javascript
const floatingWidget = await ProcmsChatbot.createFloatingWidget({
    botUuid: 'your-bot-uuid-here',
    apiKey: 'pk_live_your_api_key_here',
    apiBaseUrl: 'https://your-domain.com/api/v1/widget',
    theme: 'dark',
    position: 'bottom-right',
    onReady: () => console.log('Widget ready'),
    onMessage: (message) => console.log('New message:', message)
});
```

### **3. With Error Handling**

```javascript
try {
    const widget = await ProcmsChatbot.create({
        botUuid: 'your-bot-uuid-here',
        apiKey: 'pk_live_your_api_key_here',
        apiBaseUrl: 'https://your-domain.com/api/v1/widget',
        onReady: () => {
            console.log('✅ Widget loaded successfully');
        },
        onError: (error) => {
            console.error('❌ Widget error:', error);
        }
    }, '#chat-container');
} catch (error) {
    console.error('Failed to load widget:', error);
    // Show fallback UI or error message
}
```

## 🔧 Configuration Options

```javascript
const config = {
    // Required
    botUuid: 'your-bot-uuid-here',
    apiKey: 'pk_live_your_api_key_here',
    
    // Optional
    apiBaseUrl: 'https://your-domain.com/api/v1/widget', // Auto-detected if not provided
    userId: 'user_123', // For user identification
    theme: 'light', // 'light' | 'dark' | 'auto'
    
    // Events
    onReady: () => console.log('Widget ready'),
    onMessage: (message) => console.log('Message:', message),
    onError: (error) => console.error('Error:', error),
    onMinimized: () => console.log('Widget minimized'),
    onMaximized: () => console.log('Widget maximized')
};
```

## 🎯 Real Features

### **✅ Bot Access Validation**
- Validates API key against bot UUID
- Supports both API key and share token authentication
- Returns bot configuration and permissions

### **✅ Conversation Management**
- Creates new conversations automatically
- Retrieves existing conversations for returning users
- Manages conversation state and metadata

### **✅ Real Message Flow**
```javascript
// 1. User sends message
const userMessage = await api.sendMessage("Hello");

// 2. API creates user message and triggers AI response
// 3. Widget shows typing indicator while AI processes

// 4. AI response is generated and returned
const botMessage = userMessage.botMessage;

// 5. If processing takes time, widget polls for status
if (botMessage.isProcessing) {
    await pollMessageStatus(botMessage.id);
}
```

### **✅ Message Status Polling**
- Polls message status for long-running AI responses
- Updates message content when processing completes
- Handles timeout and error scenarios

### **✅ Existing Message Loading**
- Loads conversation history on widget initialization
- Displays previous messages in correct order
- Maintains conversation context

## 🚨 Error Handling

### **Common Errors & Solutions**

#### **1. Authentication Failed (401)**
```
Error: Unauthorized. Valid API key or share token required.
```
**Solution:** Check API key format and bot UUID

#### **2. Bot Not Found (404)**
```
Error: Bot not found or access denied
```
**Solution:** Verify bot UUID and ensure bot is active

#### **3. Network Errors**
```
Error: Failed to fetch
```
**Solution:** Check API base URL and network connectivity

#### **4. CORS Issues**
```
Error: CORS policy blocked
```
**Solution:** Configure CORS headers on backend

## 📊 API Response Examples

### **Bot Config Response**
```json
{
    "success": true,
    "data": {
        "uuid": "bot-uuid-123",
        "name": "Customer Support Bot",
        "greeting_message": "Hello! How can I help you?",
        "theme": {
            "primaryColor": "#3b82f6",
            "backgroundColor": "#f9fafb"
        }
    }
}
```

### **Send Message Response**
```json
{
    "success": true,
    "data": {
        "response": "Thank you for your message!",
        "messageId": "msg-user-123",
        "botMessageId": "msg-bot-456",
        "status": "completed",
        "isProcessing": false,
        "messages": [
            {
                "id": "msg-user-123",
                "type": "user",
                "content": "Hello",
                "timestamp": "2024-01-15T10:30:00Z"
            },
            {
                "id": "msg-bot-456", 
                "type": "bot",
                "content": "Thank you for your message!",
                "timestamp": "2024-01-15T10:30:02Z",
                "status": "completed"
            }
        ]
    }
}
```

## 🔄 Message Status Flow

```mermaid
sequenceDiagram
    participant U as User
    participant W as Widget
    participant A as API
    participant AI as AI Service

    U->>W: Types message
    W->>A: POST /messages
    A->>AI: Process message
    A-->>W: Response (processing=true)
    W->>W: Show typing indicator
    
    loop Poll Status
        W->>A: GET /messages/{id}/status
        A-->>W: Status update
    end
    
    AI-->>A: Processing complete
    A-->>W: Final response
    W->>W: Update message content
    W->>U: Show final response
```

## 🧪 Testing

### **1. Test Connection**
```javascript
async function testConnection() {
    try {
        const response = await fetch('/api/v1/widget/health');
        console.log('Health check:', response.ok ? '✅' : '❌');
    } catch (error) {
        console.error('Connection failed:', error);
    }
}
```

### **2. Test Bot Access**
```javascript
async function testBotAccess(botUuid, apiKey) {
    try {
        const response = await fetch(`/api/v1/widget/bot/${botUuid}/validate`, {
            headers: {
                'Authorization': `Bearer ${apiKey}`
            }
        });
        const data = await response.json();
        console.log('Bot access:', data.success ? '✅' : '❌');
    } catch (error) {
        console.error('Bot access test failed:', error);
    }
}
```

## 🚀 Deployment

### **1. Production Setup**
- Use `pk_live_` API keys (not `pk_test_`)
- Configure proper CORS headers
- Set up SSL/HTTPS
- Monitor API rate limits

### **2. CDN Distribution**
```html
<script src="https://cdn.your-domain.com/widget/procms-chatbot.umd.js"></script>
```

### **3. Self-hosted**
```html
<script src="/dist/widget/procms-chatbot.umd.js"></script>
```

## 📈 Analytics & Monitoring

Widget emits events for tracking:

```javascript
const widget = await ProcmsChatbot.create({
    // ... config
    onMessage: (message) => {
        // Track message events
        analytics.track('Widget Message', {
            type: message.type,
            conversationId: message.conversationId
        });
    },
    onReady: () => {
        analytics.track('Widget Loaded');
    }
});
```

## 🔧 Troubleshooting

### **Debug Mode**
Enable debug logging:
```javascript
// Widget automatically logs to console with [ProcMS Widget] prefix
// Check browser console for detailed API call logs
```

### **Common Issues**
1. **Widget not loading:** Check console for JavaScript errors
2. **API calls failing:** Verify network tab in DevTools
3. **Authentication issues:** Confirm API key format and permissions
4. **CORS errors:** Check server CORS configuration

Widget hiện tại đã sẵn sàng cho production với full API integration! 🎉
