<?php

namespace Modules\Language\Http\Controllers;

use Modules\Core\Traits\ResponseTrait;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Language\Facades\LanguageFacade;

class LanguageController extends Controller
{
    use ResponseTrait;

    public function index(Request $request): JsonResponse
    {
        return $this->successResponse(
            LanguageFacade::getActiveLanguages(),
            __('Languages retrieved successfully.')
        );
    }

    public function default(): JsonResponse
    {
        return $this->successResponse(LanguageFacade::getDefaultLanguage());
    }

    public function show(string $code): JsonResponse
    {
        return $this->successResponse(LanguageFacade::findByCode($code));
    }
}
