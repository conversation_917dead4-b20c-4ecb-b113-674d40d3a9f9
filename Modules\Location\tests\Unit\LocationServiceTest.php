<?php

namespace Modules\Location\Tests\Unit;

use Tests\TestCase;
use Modules\Location\Models\Country;
use Modules\Location\Models\GeoDivision;
use Modules\Location\Services\LocationService;
use Illuminate\Foundation\Testing\RefreshDatabase;

use PHPUnit\Framework\Attributes\Test;

class LocationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected LocationService $locationService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations for Location module
        $this->artisan('migrate', ['--path' => 'Modules/Location/database/migrations']);
        
        $this->locationService = new LocationService();
    }

    #[Test]
    public function it_can_get_active_countries_for_dropdown()
    {
        Country::factory()->count(3)->active()->create();
        Country::factory()->inactive()->create();

        $countries = $this->locationService->getActiveCountriesForDropdown();

        $this->assertCount(3, $countries);
        $this->assertTrue($countries->every(fn($country) => $country->status === 'active'));
        
        // Check structure
        $firstCountry = $countries->first();
        $this->assertArrayHasKey('id', $firstCountry->toArray());
        $this->assertArrayHasKey('name', $firstCountry->toArray());
        $this->assertArrayHasKey('native_name', $firstCountry->toArray());
        $this->assertArrayHasKey('iso_code_2', $firstCountry->toArray());
    }



    #[Test]
    public function it_can_get_states_for_country()
    {
        $country = Country::factory()->create();
        $state1 = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $state2 = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();

        $states = $this->locationService->getStatesForCountry($country->id);

        $this->assertCount(2, $states);
        $this->assertTrue($states->contains($state1));
        $this->assertTrue($states->contains($state2));
        $this->assertFalse($states->contains($city));
    }

    #[Test]
    public function it_can_get_cities_for_state()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city1 = GeoDivision::factory()->city()->forCountry($country->id)->childOf($state->id)->create();
        $city2 = GeoDivision::factory()->city()->forCountry($country->id)->childOf($state->id)->create();
        $otherCity = GeoDivision::factory()->city()->forCountry($country->id)->create();

        $cities = $this->locationService->getCitiesForState($state->id);

        $this->assertCount(2, $cities);
        $this->assertTrue($cities->contains($city1));
        $this->assertTrue($cities->contains($city2));
        $this->assertFalse($cities->contains($otherCity));
    }

    #[Test]
    public function it_can_get_districts_for_city()
    {
        $country = Country::factory()->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();
        $district1 = GeoDivision::factory()->district()->forCountry($country->id)->childOf($city->id)->create();
        $district2 = GeoDivision::factory()->district()->forCountry($country->id)->childOf($city->id)->create();
        $otherDistrict = GeoDivision::factory()->district()->forCountry($country->id)->create();

        $districts = $this->locationService->getDistrictsForCity($city->id);

        $this->assertCount(2, $districts);
        $this->assertTrue($districts->contains($district1));
        $this->assertTrue($districts->contains($district2));
        $this->assertFalse($districts->contains($otherDistrict));
    }

    #[Test]
    public function it_can_get_geo_divisions_for_country()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();
        $otherCountryDivision = GeoDivision::factory()->create();

        $divisions = $this->locationService->getGeoDivisionsForCountry($country->id);

        $this->assertCount(2, $divisions);
        $this->assertTrue($divisions->contains($state));
        $this->assertTrue($divisions->contains($city));
        $this->assertFalse($divisions->contains($otherCountryDivision));
    }

    #[Test]
    public function it_can_get_geo_divisions_for_country_by_type()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();

        $states = $this->locationService->getGeoDivisionsForCountry($country->id, 'state');
        $cities = $this->locationService->getGeoDivisionsForCountry($country->id, 'city');

        $this->assertCount(1, $states);
        $this->assertCount(1, $cities);
        $this->assertTrue($states->contains($state));
        $this->assertTrue($cities->contains($city));
    }

    #[Test]
    public function it_can_find_country_by_iso_code()
    {
        $country = Country::factory()->create([
            'iso_code_2' => 'US',
            'iso_code_3' => 'USA'
        ]);

        $foundByIso2 = $this->locationService->findCountryByIsoCode('US');
        $foundByIso3 = $this->locationService->findCountryByIsoCode('USA');

        $this->assertEquals($country->id, $foundByIso2->id);
        $this->assertEquals($country->id, $foundByIso3->id);
    }

    #[Test]
    public function it_returns_null_for_invalid_iso_code()
    {
        $result = $this->locationService->findCountryByIsoCode('INVALID');

        $this->assertNull($result);
    }

    #[Test]
    public function it_can_find_geo_division_by_id()
    {
        $country = Country::factory()->create();
        $division = GeoDivision::factory()->forCountry($country->id)->create();

        $found = $this->locationService->findGeoDivisionById($division->id);

        $this->assertEquals($division->id, $found->id);
    }

    #[Test]
    public function it_returns_null_for_invalid_division_id()
    {
        $result = $this->locationService->findGeoDivisionById(999);

        $this->assertNull($result);
    }

    #[Test]
    public function it_can_get_hierarchical_data()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->childOf($state->id)->create();

        $data = $this->locationService->getHierarchicalData($country->id, $state->id, $city->id);

        $this->assertArrayHasKey('countries', $data);
        $this->assertArrayHasKey('states', $data);
        $this->assertArrayHasKey('cities', $data);
        $this->assertArrayHasKey('districts', $data);
        
        $this->assertNotEmpty($data['countries']);
        $this->assertNotEmpty($data['states']);
        $this->assertNotEmpty($data['cities']);
    }

    #[Test]
    public function it_can_search_locations()
    {
        $country = Country::factory()->create(['name' => 'United States']);
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create(['name' => 'California']);
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create(['name' => 'Los Angeles']);

        $results = $this->locationService->searchLocations('United');

        $this->assertGreaterThan(0, $results->count());
        $this->assertTrue($results->contains('name', 'United States'));
    }

    #[Test]
    public function it_can_search_locations_by_type()
    {
        $country = Country::factory()->create(['name' => 'United States']);
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create(['name' => 'California']);
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create(['name' => 'California City']);

        $stateResults = $this->locationService->searchLocations('California', 'state');
        $cityResults = $this->locationService->searchLocations('California', 'city');

        $this->assertTrue($stateResults->contains('name', 'California'));
        $this->assertTrue($cityResults->contains('name', 'California City'));
    }

    #[Test]
    public function it_can_search_locations_by_country()
    {
        $country1 = Country::factory()->create();
        $country2 = Country::factory()->create();
        
        $state1 = GeoDivision::factory()->state()->forCountry($country1->id)->create(['name' => 'Test State']);
        $state2 = GeoDivision::factory()->state()->forCountry($country2->id)->create(['name' => 'Test State']);

        $results = $this->locationService->searchLocations('Test', null, $country1->id);

        $this->assertCount(1, $results);
        $this->assertEquals('Test State', $results->first()['name']);
    }

    #[Test]
    public function it_respects_search_limit()
    {
        $country = Country::factory()->create();
        GeoDivision::factory()->count(10)->forCountry($country->id)->create(['name' => 'Test Division']);

        $results = $this->locationService->searchLocations('Test', null, null, 5);

        $this->assertCount(5, $results);
    }



    #[Test]
    public function search_returns_correct_structure()
    {
        $country = Country::factory()->create(['name' => 'Test Country']);

        $results = $this->locationService->searchLocations('Test');

        $this->assertNotEmpty($results);
        
        $firstResult = $results->first();
        $this->assertArrayHasKey('id', $firstResult);
        $this->assertArrayHasKey('name', $firstResult);
        $this->assertArrayHasKey('type', $firstResult);
        $this->assertArrayHasKey('full_name', $firstResult);
    }

    #[Test]
    public function it_searches_both_name_and_native_name()
    {
        $country = Country::factory()->create([
            'name' => 'Germany',
            'native_name' => 'Deutschland'
        ]);

        $resultsName = $this->locationService->searchLocations('Germany');
        $resultsNative = $this->locationService->searchLocations('Deutschland');

        $this->assertNotEmpty($resultsName);
        $this->assertNotEmpty($resultsNative);
        $this->assertEquals($resultsName->first()['id'], $resultsNative->first()['id']);
    }

    #[Test]
    public function hierarchical_data_returns_empty_collections_when_no_ids_provided()
    {
        $data = $this->locationService->getHierarchicalData();

        $this->assertNotEmpty($data['countries']);
        $this->assertEmpty($data['states']);
        $this->assertEmpty($data['cities']);
        $this->assertEmpty($data['districts']);
    }


}
