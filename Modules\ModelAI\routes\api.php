<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\ModelAI\Http\Controllers\ModelAIController;
use Mo<PERSON>les\ModelAI\Http\Controllers\ModelToolController;
use Modules\ModelAI\Http\Controllers\Auth\ModelCategoryController as AuthModelCategoryController;
use Modules\ModelAI\Http\Controllers\Auth\ModelAIController as AuthModelAIController;
use Modules\ModelAI\Http\Controllers\Auth\ProviderController as AuthProviderController;

// Public API routes (no authentication required)
Route::prefix('v1')->group(function () {
    // AI Models
    Route::get('model-ai', [ModelAIController::class, 'index'])->name('api.model-ai.index');
    Route::get('model-ai/default', [ModelAIController::class, 'default'])->name('api.model-ai.default');
    Route::get('model-ai/{key}', [ModelAIController::class, 'show'])->name('api.model-ai.show');

    // Tools
    Route::get('model-tools', [ModelToolController::class, 'index'])->name('api.model-tools.index');
    Route::get('model-tools/{id}', [ModelToolController::class, 'show'])->name('api.model-tools.show');

    // Legacy routes for backward compatibility
    Route::get('dropdown', [ModelAIController::class, 'modelsDropdown'])->name('model-ai.dropdown');
    Route::get('dropdown', [ModelAIController::class, 'categoriesDropdown'])->name('model-categories.dropdown');
    Route::get('categories-with-models', [ModelAIController::class, 'categoriesWithModels'])->name('categories-with-models');
    Route::get('models-with-categories', [ModelAIController::class, 'modelsWithCategories'])->name('models-with-categories');
    Route::get('model-tools/dropdown', [ModelToolController::class, 'toolsDropdown'])->name('model-tools.dropdown');
    Route::get('categories-with-tools', [ModelToolController::class, 'categoriesWithTools'])->name('categories-with-tools');
    Route::get('models-with-tools', [ModelToolController::class, 'modelsWithTools'])->name('models-with-tools');
    Route::get('{modelKey}/tools', [ModelToolController::class, 'modelTools'])->name('model-ai.tools');
});

// Authenticated API routes (admin/auth)
/*Route::middleware(['auth:api'])->prefix('v1/auth')->group(function () {

    // ===== AI MODELS MANAGEMENT =====
    // Dropdown
    Route::get('dropdown', [AuthModelAIController::class, 'dropdown'])->name('dropdown');

    // Standard CRUD routes
    Route::get('model-ai', [AuthModelAIController::class, 'index'])->name('index');
    Route::post('model-ai', [AuthModelAIController::class, 'store'])->name('store');
    Route::get('{id}', [AuthModelAIController::class, 'show'])->name('show');
    Route::put('{id}', [AuthModelAIController::class, 'update'])->name('update');
    Route::patch('{id}', [AuthModelAIController::class, 'update'])->name('update');

    // Bulk operations
    Route::delete('bulk/delete', [AuthModelAIController::class, 'bulkDelete'])->name('bulk-delete');
    Route::delete('bulk/force', [AuthModelAIController::class, 'bulkDestroy'])->name('bulk-destroy');
    Route::put('bulk/restore', [AuthModelAIController::class, 'bulkRestore'])->name('bulk-restore');

    // Custom operations
    Route::put('{id}/set-default', [AuthModelAIController::class, 'setDefault'])->name('set-default');
    Route::get('provider/{providerId}', [AuthModelAIController::class, 'byProvider'])->name('by-provider');
    Route::post('service', [AuthModelAIController::class, 'updateOrCreateService'])->name('service');

    // Delete operations
    Route::delete('{id}/delete', [AuthModelAIController::class, 'delete'])->name('delete');
    Route::delete('{id}/force', [AuthModelAIController::class, 'destroy'])->name('destroy');
    Route::put('{id}/restore', [AuthModelAIController::class, 'restore'])->name('restore');

    // ===== PROVIDERS MANAGEMENT =====
    // Dropdown
    Route::get('dropdown', [AuthProviderController::class, 'dropdown'])->name('dropdown');

    // Standard CRUD routes
    Route::get('providers', [AuthProviderController::class, 'index'])->name('index');
    Route::post('providers', [AuthProviderController::class, 'store'])->name('store');
    Route::get('{id}', [AuthProviderController::class, 'show'])->name('show');
    Route::put('{id}', [AuthProviderController::class, 'update'])->name('update');
    Route::patch('{id}', [AuthProviderController::class, 'update'])->name('update');

    // Bulk operations
    Route::delete('bulk/delete', [AuthProviderController::class, 'bulkDelete'])->name('bulk-delete');
    Route::delete('bulk/force', [AuthProviderController::class, 'bulkDestroy'])->name('bulk-destroy');
    Route::put('bulk/restore', [AuthProviderController::class, 'bulkRestore'])->name('bulk-restore');

    // Delete operations
    Route::delete('{id}/delete', [AuthProviderController::class, 'delete'])->name('delete');
    Route::delete('{id}/force', [AuthProviderController::class, 'destroy'])->name('destroy');
    Route::put('{id}/restore', [AuthProviderController::class, 'restore'])->name('restore');

    // ===== CATEGORIES MANAGEMENT =====
    // Dropdown
    Route::get('dropdown', [AuthModelCategoryController::class, 'dropdown'])->name('dropdown');

    // Standard CRUD routes
    Route::get('model-categories', [AuthModelCategoryController::class, 'index'])->name('index');
    Route::post('model-categories', [AuthModelCategoryController::class, 'store'])->name('store');
    Route::get('{id}', [AuthModelCategoryController::class, 'show'])->name('show');
    Route::put('{id}', [AuthModelCategoryController::class, 'update'])->name('update');
    Route::patch('{id}', [AuthModelCategoryController::class, 'update'])->name('update');

    // Bulk operations
    Route::delete('bulk/delete', [AuthModelCategoryController::class, 'bulkDelete'])->name('bulk-delete');
    Route::delete('bulk/force', [AuthModelCategoryController::class, 'bulkDestroy'])->name('bulk-destroy');
    Route::put('bulk/restore', [AuthModelCategoryController::class, 'bulkRestore'])->name('bulk-restore');

    // Delete operations
    Route::delete('{id}/delete', [AuthModelCategoryController::class, 'delete'])->name('delete');
    Route::delete('{id}/force', [AuthModelCategoryController::class, 'destroy'])->name('destroy');
    Route::put('{id}/restore', [AuthModelCategoryController::class, 'restore'])->name('restore');

    // ===== TOOLS MANAGEMENT =====
    // Dropdown
    Route::get('model-tools/dropdown', [AuthModelToolController::class, 'dropdown'])->name('auth.model-tools.dropdown');

    // Standard CRUD routes
    Route::get('model-tools', [AuthModelToolController::class, 'index'])->name('auth.model-tools.index');
    Route::post('model-tools', [AuthModelToolController::class, 'store'])->name('auth.model-tools.store');
    Route::get('model-tools/{id}', [AuthModelToolController::class, 'show'])->name('auth.model-tools.show');
    Route::put('model-tools/{id}', [AuthModelToolController::class, 'update'])->name('auth.model-tools.update');
    Route::patch('model-tools/{id}', [AuthModelToolController::class, 'update'])->name('auth.model-tools.update');

    // Bulk operations
    Route::delete('model-tools/bulk/delete', [AuthModelToolController::class, 'bulkDelete'])->name('auth.model-tools.bulk-delete');
    Route::delete('model-tools/bulk/force', [AuthModelToolController::class, 'bulkDestroy'])->name('auth.model-tools.bulk-destroy');
    Route::put('model-tools/bulk/restore', [AuthModelToolController::class, 'bulkRestore'])->name('auth.model-tools.bulk-restore');

    // Delete operations
    Route::delete('model-tools/{id}/delete', [AuthModelToolController::class, 'delete'])->name('auth.model-tools.delete');
    Route::delete('model-tools/{id}/force', [AuthModelToolController::class, 'destroy'])->name('auth.model-tools.destroy');
    Route::put('model-tools/{id}/restore', [AuthModelToolController::class, 'restore'])->name('auth.model-tools.restore');

    // ===== SERVICES MANAGEMENT =====
    // Dropdown
    Route::get('model-services/dropdown', [AuthModelServiceController::class, 'dropdown'])->name('auth.model-services.dropdown');

    // Standard CRUD routes
    Route::get('model-services', [AuthModelServiceController::class, 'index'])->name('auth.model-services.index');
    Route::post('model-services', [AuthModelServiceController::class, 'store'])->name('auth.model-services.store');
    Route::get('model-services/{id}', [AuthModelServiceController::class, 'show'])->name('auth.model-services.show');
    Route::put('model-services/{id}', [AuthModelServiceController::class, 'update'])->name('auth.model-services.update');
    Route::patch('model-services/{id}', [AuthModelServiceController::class, 'update'])->name('auth.model-services.update');

    // Bulk operations
    Route::delete('model-services/bulk/delete', [AuthModelServiceController::class, 'bulkDelete'])->name('auth.model-services.bulk-delete');
    Route::delete('model-services/bulk/force', [AuthModelServiceController::class, 'bulkDestroy'])->name('auth.model-services.bulk-destroy');
    Route::put('model-services/bulk/restore', [AuthModelServiceController::class, 'bulkRestore'])->name('auth.model-services.bulk-restore');

    // Delete operations
    Route::delete('model-services/{id}/delete', [AuthModelServiceController::class, 'delete'])->name('auth.model-services.delete');
    Route::delete('model-services/{id}/force', [AuthModelServiceController::class, 'destroy'])->name('auth.model-services.destroy');
    Route::put('model-services/{id}/restore', [AuthModelServiceController::class, 'restore'])->name('auth.model-services.restore');

    // ===== MODEL-TOOL RELATIONSHIPS =====
    // Standard CRUD routes
    Route::get('model-ai-tools', [AuthModelAIToolController::class, 'index'])->name('auth.model-ai-tools.index');
    Route::post('model-ai-tools', [AuthModelAIToolController::class, 'store'])->name('auth.model-ai-tools.store');
    Route::get('model-ai-tools/{id}', [AuthModelAIToolController::class, 'show'])->name('auth.model-ai-tools.show');
    Route::put('model-ai-tools/{id}', [AuthModelAIToolController::class, 'update'])->name('auth.model-ai-tools.update');
    Route::patch('model-ai-tools/{id}', [AuthModelAIToolController::class, 'update'])->name('auth.model-ai-tools.update');
    Route::delete('model-ai-tools/{id}', [AuthModelAIToolController::class, 'destroy'])->name('auth.model-ai-tools.destroy');

    // Relationship operations
    Route::post('{modelAI}/tools/attach', [AuthModelAIToolController::class, 'attachTools'])->name('tools.attach');
    Route::delete('{modelAI}/tools/detach', [AuthModelAIToolController::class, 'detachTools'])->name('tools.detach');
    Route::get('{modelAI}/tools', [AuthModelAIToolController::class, 'modelTools'])->name('tools.index');
    Route::get('model-tools/{modelTool}/models', [AuthModelAIToolController::class, 'toolModels'])->name('auth.model-tools.models.index');
    Route::put('{modelAI}/tools/{modelTool}/toggle', [AuthModelAIToolController::class, 'toggleTool'])->name('tools.toggle');
});*/

// ===== AUTHENTICATED API ROUTES =====
Route::middleware(['auth:api'])->prefix('v1/auth')->name('auth.')->group(function () {

    // ===== AI MODELS MANAGEMENT =====
    Route::prefix('model-ai')->name('model-ai.')->group(function () {
        // Utility routes
        // ===== AI MODELS MANAGEMENT =====
        // Dropdown
        Route::get('/dropdown', [AuthModelAIController::class, 'dropdown'])->name('dropdown');

        // Standard CRUD routes
        Route::get('/', [AuthModelAIController::class, 'index'])->name('index');
        Route::post('/', [AuthModelAIController::class, 'store'])->name('store');

        // Bulk operations
        Route::delete('bulk/delete', [AuthModelAIController::class, 'bulkDelete'])->name('bulk-delete');
        Route::delete('bulk/force', [AuthModelAIController::class, 'bulkDestroy'])->name('bulk-destroy');
        Route::put('bulk/restore', [AuthModelAIController::class, 'bulkRestore'])->name('bulk-restore');

        // Custom operations
        Route::get('provider/{providerId}', [AuthModelAIController::class, 'byProvider'])->name('by-provider');
        Route::post('service', [AuthModelAIController::class, 'updateOrCreateService'])->name('service');

        // Delete operations
        Route::delete('{id}/delete', [AuthModelAIController::class, 'delete'])->name('delete');
        Route::delete('{id}/force', [AuthModelAIController::class, 'destroy'])->name('destroy');
        Route::put('{id}/restore', [AuthModelAIController::class, 'restore'])->name('restore');
        Route::put('{id}/set-default', [AuthModelAIController::class, 'setDefault'])->name('set-default');
        Route::get('{id}', [AuthModelAIController::class, 'show'])->name('show');
        Route::put('{id}', [AuthModelAIController::class, 'update'])->name('update');
        Route::patch('{id}', [AuthModelAIController::class, 'update'])->name('update');
    });

    // ===== PROVIDERS MANAGEMENT =====
    Route::prefix('providers')->name('providers.')->group(function () {
        // ===== PROVIDERS MANAGEMENT =====
        // Dropdown
        Route::get('dropdown', [AuthProviderController::class, 'dropdown'])->name('dropdown');

        // Standard CRUD routes
        Route::get('/', [AuthProviderController::class, 'index'])->name('index');
        Route::post('/', [AuthProviderController::class, 'store'])->name('store');
        Route::get('{id}', [AuthProviderController::class, 'show'])->name('show');
        Route::put('{id}', [AuthProviderController::class, 'update'])->name('update');
        Route::patch('{id}', [AuthProviderController::class, 'update'])->name('update');

        // Bulk operations
        Route::delete('bulk/delete', [AuthProviderController::class, 'bulkDelete'])->name('bulk-delete');
        Route::delete('bulk/force', [AuthProviderController::class, 'bulkDestroy'])->name('bulk-destroy');
        Route::put('bulk/restore', [AuthProviderController::class, 'bulkRestore'])->name('bulk-restore');

        // Delete operations
        Route::delete('{id}/delete', [AuthProviderController::class, 'delete'])->name('delete');
        Route::delete('{id}/force', [AuthProviderController::class, 'destroy'])->name('destroy');
        Route::put('{id}/restore', [AuthProviderController::class, 'restore'])->name('restore');
    });

    // ===== CATEGORIES MANAGEMENT =====
    Route::prefix('model-categories')->name('model-categories.')->group(function () {
        // ===== CATEGORIES MANAGEMENT =====
        // Dropdown
        Route::get('dropdown', [AuthModelCategoryController::class, 'dropdown'])->name('dropdown');

        // Standard CRUD routes
        Route::get('/', [AuthModelCategoryController::class, 'index'])->name('index');
        Route::post('/', [AuthModelCategoryController::class, 'store'])->name('store');
        Route::get('{id}', [AuthModelCategoryController::class, 'show'])->name('show');
        Route::put('{id}', [AuthModelCategoryController::class, 'update'])->name('update');
        Route::patch('{id}', [AuthModelCategoryController::class, 'update'])->name('update');

        // Bulk operations
        Route::delete('bulk/delete', [AuthModelCategoryController::class, 'bulkDelete'])->name('bulk-delete');
        Route::delete('bulk/force', [AuthModelCategoryController::class, 'bulkDestroy'])->name('bulk-destroy');
        Route::put('bulk/restore', [AuthModelCategoryController::class, 'bulkRestore'])->name('bulk-restore');

        // Delete operations
        Route::delete('{id}/delete', [AuthModelCategoryController::class, 'delete'])->name('delete');
        Route::delete('{id}/force', [AuthModelCategoryController::class, 'destroy'])->name('destroy');
        Route::put('{id}/restore', [AuthModelCategoryController::class, 'restore'])->name('restore');
    });
});
