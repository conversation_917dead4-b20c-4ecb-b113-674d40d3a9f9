<?php

namespace Modules\ModelAI\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Core\Traits\ResponseTrait;
use Modules\ModelAI\Http\Filters\ModelToolFilter;
use Modules\ModelAI\Http\Requests\ModelToolRequest;
use Modules\ModelAI\Http\Requests\BulkModelToolRequest;
use Modules\ModelAI\Models\ModelTool;

class ModelToolController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|model-tool.view')->only(['index', 'show', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|model-tool.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|model-tool.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|model-tool.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|model-tool.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $tools = ModelTool::query()
            ->with(['categories', 'modelAIs'])
            ->filter(new ModelToolFilter($request))
            ->ordered()
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($tools, __('Tools retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ModelToolRequest $request): JsonResponse
    {
        DB::beginTransaction();
        
        try {
            $tool = ModelTool::create($request->except('categories'));
            
            // Attach categories if provided
            if ($request->has('categories')) {
                $tool->categories()->sync($request->categories);
            }
            
            $tool->load(['categories', 'modelAIs']);
            
            DB::commit();
            
            return $this->successResponse($tool, __('Tool created successfully.'), 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse(__('Failed to create tool.'), 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(ModelTool $modelTool): JsonResponse
    {
        $modelTool->load(['categories', 'modelAIs', 'aiToolConfigs']);

        return $this->successResponse($modelTool, __('Tool retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ModelToolRequest $request, ModelTool $modelTool): JsonResponse
    {
        DB::beginTransaction();
        
        try {
            $modelTool->update($request->except('categories'));
            
            // Sync categories if provided
            if ($request->has('categories')) {
                $modelTool->categories()->sync($request->categories);
            }
            
            $modelTool->load(['categories', 'modelAIs']);
            
            DB::commit();
            
            return $this->successResponse($modelTool, __('Tool updated successfully.'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse(__('Failed to update tool.'), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ModelTool $modelTool): JsonResponse
    {
        try {
            $modelTool->delete();
            return $this->successResponse(null, __('Tool deleted successfully.'));
        } catch (\Exception $e) {
            return $this->errorResponse(__('Failed to delete tool.'), 500);
        }
    }

    /**
     * Get tools for dropdown.
     */
    public function dropdown(Request $request): JsonResponse
    {
        $tools = ModelTool::query()
            ->select(['id', 'uuid', 'key', 'name', 'type', 'is_public'])
            ->active()
            ->enabled()
            ->ordered()
            ->get();

        return $this->successResponse($tools, __('Tools dropdown retrieved successfully.'));
    }

    /**
     * Restore the specified resource from trash.
     */
    public function restore(int $id): JsonResponse
    {
        try {
            $tool = ModelTool::withTrashed()->findOrFail($id);
            $tool->restore();
            
            return $this->successResponse($tool, __('Tool restored successfully.'));
        } catch (\Exception $e) {
            return $this->errorResponse(__('Failed to restore tool.'), 500);
        }
    }

    /**
     * Permanently delete the specified resource.
     */
    public function forceDelete(int $id): JsonResponse
    {
        try {
            $tool = ModelTool::withTrashed()->findOrFail($id);
            $tool->forceDelete();
            
            return $this->successResponse(null, __('Tool permanently deleted.'));
        } catch (\Exception $e) {
            return $this->errorResponse(__('Failed to permanently delete tool.'), 500);
        }
    }

    /**
     * Bulk delete tools.
     */
    public function bulkDestroy(BulkModelToolRequest $request): JsonResponse
    {
        try {
            ModelTool::whereIn('id', $request->ids)->delete();
            
            return $this->successResponse(null, __('Tools deleted successfully.'));
        } catch (\Exception $e) {
            return $this->errorResponse(__('Failed to delete tools.'), 500);
        }
    }

    /**
     * Bulk restore tools.
     */
    public function bulkRestore(BulkModelToolRequest $request): JsonResponse
    {
        try {
            ModelTool::withTrashed()->whereIn('id', $request->ids)->restore();
            
            return $this->successResponse(null, __('Tools restored successfully.'));
        } catch (\Exception $e) {
            return $this->errorResponse(__('Failed to restore tools.'), 500);
        }
    }

    /**
     * Bulk force delete tools.
     */
    public function bulkForceDelete(BulkModelToolRequest $request): JsonResponse
    {
        try {
            ModelTool::withTrashed()->whereIn('id', $request->ids)->forceDelete();
            
            return $this->successResponse(null, __('Tools permanently deleted.'));
        } catch (\Exception $e) {
            return $this->errorResponse(__('Failed to permanently delete tools.'), 500);
        }
    }
}
