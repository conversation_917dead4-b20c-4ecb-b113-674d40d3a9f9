<?php

namespace Modules\Location\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static \Illuminate\Support\Collection getActiveCountriesForDropdown()
 * @method static \Illuminate\Support\Collection getStatesForCountry(int $countryId)
 * @method static \Illuminate\Support\Collection getCitiesForState(int $stateId)
 * @method static \Illuminate\Support\Collection getDistrictsForCity(int $cityId)
 * @method static \Illuminate\Support\Collection getGeoDivisionsForCountry(int $countryId, ?string $type = null)
 * @method static \Modules\Location\Models\Country|null findCountryByIsoCode(string $isoCode)
 * @method static \Modules\Location\Models\GeoDivision|null findGeoDivisionById(int $id)
 * @method static array getHierarchicalData(?int $countryId = null, ?int $stateId = null, ?int $cityId = null)
 * @method static \Illuminate\Support\Collection searchLocations(string $query, ?string $type = null, ?int $countryId = null, int $limit = 20)
 */
class LocationFacade extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'location.service';
    }
}
