<?php

namespace Modules\Language\Database\Seeders;

use Modules\Language\Models\Language;
use Illuminate\Database\Seeder;

class LanguageDatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed permissions first
        $this->call(LanguagePermissionSeeder::class);

        // Then seed languages data
        $this->seedLanguages();
    }

    /**
     * Seed languages data.
     */
    private function seedLanguages(): void
    {
        $languages = [
            [
                'code' => 'en',
                'name' => 'English',
                'native_name' => 'English',
                'flag' => 'us',
                'direction' => 'ltr',
                'is_default' => 1,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'vi',
                'name' => 'Vietnamese',
                'native_name' => 'Tiếng Việt',
                'flag' => 'vn',
                'direction' => 'ltr',
                'is_default' => 0,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($languages as $language) {
            Language::updateOrCreate(
                ['code' => $language['code']],
                $language
            );
        }
    }
}
