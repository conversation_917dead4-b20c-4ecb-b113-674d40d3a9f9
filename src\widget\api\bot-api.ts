/**
 * Bot API for Widget
 */

import type { ApiResponse, BotConfig } from '../types';

export class BotApiClient {
  private baseUrl: string;
  private apiKey: string;

  constructor(apiKey: string, baseUrl?: string) {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl || this.getDefaultBaseUrl();
  }

  private getDefaultBaseUrl(): string {
    // Auto-detect base URL from current domain or use default
    if (typeof window !== 'undefined') {
      const currentDomain = window.location.origin;
      // Check if we're on the same domain as the main app
      if (currentDomain.includes('procms.com') || currentDomain.includes('localhost')) {
        return currentDomain;
      }
    }
    return 'https://api.procms.com';
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  /**
   * Get bot configuration by UUID
   */
  async getBotConfig(botUuid: string): Promise<ApiResponse<BotConfig>> {
    return this.request<BotConfig>(`/api/public/bots/${botUuid}/config`);
  }

  /**
   * Validate API key and bot access
   */
  async validateAccess(botUuid: string): Promise<ApiResponse<{ valid: boolean; permissions: string[] }>> {
    return this.request(`/api/public/bots/${botUuid}/validate`);
  }

  /**
   * Start a new conversation
   */
  async startConversation(botUuid: string, userId?: string): Promise<ApiResponse<{ conversationId: string }>> {
    return this.request(`/api/public/bots/${botUuid}/conversations`, {
      method: 'POST',
      body: JSON.stringify({
        userId,
        metadata: {
          userAgent: navigator.userAgent,
          referrer: document.referrer,
          timestamp: new Date().toISOString()
        }
      })
    });
  }

  /**
   * Send message to bot
   */
  async sendMessage(
    botUuid: string, 
    conversationId: string, 
    message: string
  ): Promise<ApiResponse<{ response: string; messageId: string; tokens?: number }>> {
    return this.request(`/api/public/bots/${botUuid}/conversations/${conversationId}/messages`, {
      method: 'POST',
      body: JSON.stringify({
        message,
        timestamp: new Date().toISOString()
      })
    });
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(
    botUuid: string, 
    conversationId: string
  ): Promise<ApiResponse<{ messages: any[] }>> {
    return this.request(`/api/public/bots/${botUuid}/conversations/${conversationId}/messages`);
  }

  /**
   * End conversation
   */
  async endConversation(botUuid: string, conversationId: string): Promise<ApiResponse<void>> {
    return this.request(`/api/public/bots/${botUuid}/conversations/${conversationId}/end`, {
      method: 'POST'
    });
  }
}

// Singleton instance for easy access
let apiClient: BotApiClient | null = null;

export function getBotApiClient(apiKey: string, baseUrl?: string): BotApiClient {
  if (!apiClient || apiClient['apiKey'] !== apiKey) {
    apiClient = new BotApiClient(apiKey, baseUrl);
  }
  return apiClient;
}

// Convenience functions
export async function getBotConfiguration(botUuid: string, apiKey: string): Promise<ApiResponse<BotConfig>> {
  const client = getBotApiClient(apiKey);
  return client.getBotConfig(botUuid);
}

export async function validateBotAccess(botUuid: string, apiKey: string): Promise<ApiResponse<{ valid: boolean; permissions: string[] }>> {
  const client = getBotApiClient(apiKey);
  return client.validateAccess(botUuid);
}
