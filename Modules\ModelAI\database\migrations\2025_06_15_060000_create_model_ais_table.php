<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('model_categories', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique()->comment('URL-friendly identifier');
            $table->string('name')->comment('Category name: Text Generation, Image Creation, etc.');
            $table->text('description')->nullable();
            $table->enum('type', ['ModelAI', 'Tools'])->default('ModelAI')->comment('Category type: ModelAI for AI models, Tools for tools');
            $table->string('icon', 50)->nullable()->comment('Icon class or emoji');
            $table->string('color', 20)->nullable()->comment('UI color theme');
            $table->integer('sort_order')->default(1);
            $table->string('status')->default('active')->comment('active, inactive, draft');
            $table->timestamps();
            $table->softDeletes();
            $table->index(['type', 'status', 'sort_order']);
            $table->index(['status', 'sort_order']);
        });

        // NEW TABLE: model_providers
        Schema::create('model_providers', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique()->comment('Unique identifier for the provider (e.g., openai, anthropic)');
            $table->string('name')->comment('Display name of the provider (e.g., OpenAI, Anthropic)');
            $table->text('description')->nullable();
            $table->string('base_url')->nullable()->comment('Base API URL for the provider (e.g., https://api.openai.com/v1/)');
            // Using TEXT for encrypted credentials to allow flexibility (e.g., API key, OAuth token, service account JSON)
            // IMPORTANT: This field stores encrypted data using Laravel's encrypted cast
            $table->text('api_key')->nullable();
            $table->text('credentials')->nullable()->comment('Encrypted API keys or other authentication details for the provider');
            $table->string('status')->default('active')->comment('active, inactive');
            $table->timestamps();
            $table->softDeletes();
            $table->index('status');
        });

        Schema::create('model_ai', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique()->comment('Model identifier: gpt-4, claude-3-sonnet');
            $table->string('name')->comment('Display name');
            $table->text('description')->nullable();
            // CHANGED: Replaced 'provider' string with foreign key to 'model_providers'
            $table->foreignId('model_provider_id')->constrained('model_providers')->onDelete('restrict')->comment('Foreign key to model_providers table');
            $table->string('version')->nullable();
            $table->string('api_endpoint')->comment('Specific API endpoint path for this model (e.g., /chat/completions)');
            $table->boolean('streaming')->default(true);
            $table->boolean('vision')->default(false);
            $table->boolean('function_calling')->default(false);
            $table->boolean('is_default')->default(false)->comment('Is this the default model overall?');
            $table->integer('sort_order')->default(1);
            $table->string('status')->default('active')->comment('active, inactive, draft');
            $table->timestamps();
            $table->softDeletes();
            // CHANGED: Updated index to use model_provider_id
            $table->index(['model_provider_id', 'status']);
            $table->index(['is_default', 'status']);
            $table->index('sort_order');
        });

        Schema::create('model_categorizations', function (Blueprint $table) {
            $table->foreignId('model_ai_id')->constrained('model_ai')->onDelete('cascade');
            $table->foreignId('model_category_id')->constrained('model_categories')->onDelete('cascade');

            $table->primary(['model_ai_id', 'model_category_id'], 'model_category_primary');
        });

        Schema::create('model_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('model_ai_id')->constrained('model_ai')->onDelete('cascade');

            $table->json('input_types')->nullable()->comment('Supported input content types for this service: ["text", "image", "pdf"]');
            $table->json('output_types')->nullable()->comment('Supported output content types for this service: ["text", "image"]');
            $table->json('supported_sources')->nullable()->comment('Supported input sources, e.g., [{"type":"upload"}, {"type":"oauth", "provider":"google_drive"}]');

            $table->integer('cost_per_request')->default(0)->comment('Credits per request (fixed pricing)');
            $table->decimal('cost_per1k_tokens', 8, 4)->default(0)->comment('Credits per 1K tokens (dynamic pricing)');
            $table->enum('billing_type', ['per_request', 'per_token', 'hybrid'])->default('per_request');
            $table->decimal('cost_per1k_input', 10, 6)->default(0)->comment('Provider cost USD');
            $table->decimal('cost_per1k_output', 10, 6)->default(0)->comment('Provider cost USD');
            $table->integer('max_tokens')->default(4096);
            $table->integer('context_window')->default(8192);
            $table->integer('rate_limit_rpm')->default(60);
            $table->integer('timeout_seconds')->default(60);
            $table->json('default_parameters')->nullable()->comment('Default: temperature, top_p, max_tokens, etc.');
            $table->json('allowed_parameters')->nullable()->comment('Parameters users can override');
            $table->integer('priority')->default(0);
            $table->text('notes')->nullable();
            $table->string('status')->default('active')->comment('active, inactive, draft');
            $table->timestamps();
            $table->softDeletes();
            $table->index(['model_ai_id', 'status']);
            $table->index(['billing_type', 'status']);
            $table->index(['status', 'priority']);
        });

        Schema::create('model_tools', function (Blueprint $table) {
            $table->id();
            $table->string('uuid', 36)->unique()->comment('UUID for external references');
            $table->string('key')->unique()->comment('Tool identifier: web_search, code_interpreter');
            $table->string('name')->comment('Display name');
            $table->text('description')->nullable();
            $table->enum('type', ['ai_native', 'custom', 'model'])->default('custom')->comment('Tool type');
            $table->string('provider')->nullable()->comment('Tool provider: openai, custom, etc.');
            $table->string('version', 50)->nullable();
            $table->string('api_endpoint', 500)->nullable()->comment('API endpoint for custom tools');
            $table->json('configuration')->nullable()->comment('Tool configuration parameters');
            $table->json('input_schema')->nullable()->comment('Expected input schema');
            $table->json('output_schema')->nullable()->comment('Expected output schema');
            $table->boolean('is_enabled')->default(true);
            $table->boolean('is_public')->default(false)->comment('Public tools available to all models');
            $table->integer('sort_order')->default(1);
            $table->string('status')->default('active')->comment('active, inactive, draft');
            $table->timestamps();
            $table->softDeletes();
            $table->index(['type', 'status']);
            $table->index(['provider', 'status']);
            $table->index(['is_public', 'status']);
            $table->index(['status', 'sort_order']);
        });

        Schema::create('model_ai_tools', function (Blueprint $table) {
            $table->id();
            $table->foreignId('model_ai_id')->constrained('model_ai')->onDelete('cascade');
            $table->foreignId('model_tool_id')->constrained('model_tools')->onDelete('cascade');
            $table->boolean('is_enabled')->default(true);
            $table->json('configuration')->nullable()->comment('Model-specific tool configuration');
            $table->integer('priority')->default(0)->comment('Tool priority for this model');
            $table->integer('max_usage_per_request')->nullable()->comment('Max tool calls per request');
            $table->integer('rate_limit_per_minute')->nullable()->comment('Rate limit for this tool');
            $table->timestamps();

            $table->unique(['model_ai_id', 'model_tool_id'], 'unique_model_tool');
            $table->index(['model_ai_id', 'is_enabled']);
            $table->index(['model_tool_id', 'is_enabled']);
            $table->index('priority');
        });

        Schema::create('model_tool_categorizations', function (Blueprint $table) {
            $table->foreignId('model_tool_id')->constrained('model_tools')->onDelete('cascade');
            $table->foreignId('model_category_id')->constrained('model_categories')->onDelete('cascade');

            $table->primary(['model_tool_id', 'model_category_id'], 'model_tool_category_primary');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('model_tool_categorizations');
        Schema::dropIfExists('model_ai_tools');
        Schema::dropIfExists('model_tools');
        Schema::dropIfExists('model_services');
        Schema::dropIfExists('model_categorizations');
        Schema::dropIfExists('model_ai');
        Schema::dropIfExists('model_providers'); // NEW TABLE
        Schema::dropIfExists('model_categories');
    }
};
