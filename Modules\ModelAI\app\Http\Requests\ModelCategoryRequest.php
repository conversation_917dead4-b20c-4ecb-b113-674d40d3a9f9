<?php

namespace Modules\ModelAI\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

/**
 * @property mixed $id
 * @property mixed $model_category_id
 */
class ModelCategoryRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'key' => [
                'required',
                'string',
                'max:255',
                'alpha_dash',
                Rule::unique('model_categories')->ignore($this->id)->whereNull('deleted_at'),
            ],
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'type' => [
                'required',
                'string',
                Rule::in(['ModelAI', 'Tools']),
            ],
            'icon' => [
                'nullable',
                'string',
                'max:50',
            ],
            'color' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            ],
            'sort_order' => [
                'required',
                'integer',
                'min:1',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(['active', 'inactive', 'draft']),
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'key' => __('Category Key'),
            'name' => __('Category Name'),
            'description' => __('Description'),
            'type' => __('Category Type'),
            'icon' => __('Icon'),
            'color' => __('Color'),
            'sort_order' => __('Sort Order'),
            'status' => __('Status'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'key.alpha_dash' => __('The category key may only contain letters, numbers, dashes and underscores.'),
            'color.regex' => __('The color must be a valid hex color code (e.g., #FF0000 or #F00).'),
        ];
    }
}
