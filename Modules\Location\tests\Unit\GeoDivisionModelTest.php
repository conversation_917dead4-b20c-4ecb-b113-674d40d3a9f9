<?php

namespace Modules\Location\Tests\Unit;

use Tests\TestCase;
use Modules\Location\Models\Country;
use Modules\Location\Models\GeoDivision;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\Test;

class GeoDivisionModelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations for Location module
        $this->artisan('migrate', ['--path' => 'Modules/Location/database/migrations']);
    }

    #[Test]
    public function it_has_correct_fillable_attributes()
    {
        $division = new GeoDivision();

        $expectedFillable = [
            'code',
            'name',
            'native_name',
            'type',
            'country_id',
            'parent_id',
            'level',
            'path',
            'latitude',
            'longitude',
            'postal_code',
            'sort_order',
            'status',
        ];

        $this->assertEquals($expectedFillable, $division->getFillable());
    }

    #[Test]
    public function it_has_correct_casts()
    {
        $division = new GeoDivision();

        $expectedCasts = [
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
            'sort_order' => 'integer',
            'level' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];

        foreach ($expectedCasts as $attribute => $cast) {
            $this->assertEquals($cast, $division->getCasts()[$attribute]);
        }
    }

    #[Test]
    public function it_can_create_a_geo_division()
    {
        $country = Country::factory()->create();
        
        $divisionData = [
            'code' => 'CA',
            'name' => 'California',
            'native_name' => 'California',
            'type' => 'state',
            'country_id' => $country->id,
            'level' => 1,
            'latitude' => 36.7783,
            'longitude' => -119.4179,
            'postal_code' => '90210',
            'sort_order' => 1,
            'status' => 'active'
        ];

        $division = GeoDivision::create($divisionData);

        $this->assertInstanceOf(GeoDivision::class, $division);
        $this->assertDatabaseHas('geo_divisions', $divisionData);
        $this->assertEquals('CA', $division->code);
        $this->assertEquals('California', $division->name);
        $this->assertEquals('state', $division->type);
    }

    #[Test]
    public function it_automatically_sets_level_and_path_on_creation()
    {
        $country = Country::factory()->create();
        
        // Create parent state
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        
        // Create child city
        $city = GeoDivision::create([
            'name' => 'Los Angeles',
            'type' => 'city',
            'country_id' => $country->id,
            'parent_id' => $state->id,
            'status' => 'active'
        ]);

        $this->assertEquals(2, $city->level);
        $this->assertEquals($state->id, $city->path);
    }

    #[Test]
    public function it_updates_children_paths_when_parent_path_changes()
    {
        $country = Country::factory()->create();
        
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->childOf($state->id)->create();
        
        // Update state path (simulating a hierarchy change)
        $state->path = '999';
        $state->save();

        $city->refresh();
        $this->assertEquals('999/' . $state->id, $city->path);
    }

    #[Test]
    public function it_can_scope_active_divisions()
    {
        $country = Country::factory()->create();
        GeoDivision::factory()->forCountry($country->id)->create(['status' => 'active']);
        GeoDivision::factory()->forCountry($country->id)->create(['status' => 'inactive']);

        $activeDivisions = GeoDivision::active()->get();

        $this->assertCount(1, $activeDivisions);
        $this->assertEquals('active', $activeDivisions->first()->status);
    }

    #[Test]
    public function it_can_scope_by_type()
    {
        $country = Country::factory()->create();
        GeoDivision::factory()->state()->forCountry($country->id)->create();
        GeoDivision::factory()->city()->forCountry($country->id)->create();

        $states = GeoDivision::type('state')->get();
        $cities = GeoDivision::type('city')->get();

        $this->assertCount(1, $states);
        $this->assertCount(1, $cities);
        $this->assertEquals('state', $states->first()->type);
        $this->assertEquals('city', $cities->first()->type);
    }

    #[Test]
    public function it_can_scope_by_level()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        GeoDivision::factory()->city()->forCountry($country->id)->childOf($state->id)->create();

        $level1 = GeoDivision::level(1)->get();
        $level2 = GeoDivision::level(2)->get();

        $this->assertCount(1, $level1);
        $this->assertCount(1, $level2);
        $this->assertEquals(1, $level1->first()->level);
        $this->assertEquals(2, $level2->first()->level);
    }

    #[Test]
    public function it_can_scope_by_country()
    {
        $country1 = Country::factory()->create();
        $country2 = Country::factory()->create();
        
        GeoDivision::factory()->forCountry($country1->id)->create();
        GeoDivision::factory()->forCountry($country2->id)->create();

        $country1Divisions = GeoDivision::country($country1->id)->get();
        $country2Divisions = GeoDivision::country($country2->id)->get();

        $this->assertCount(1, $country1Divisions);
        $this->assertCount(1, $country2Divisions);
        $this->assertEquals($country1->id, $country1Divisions->first()->country_id);
        $this->assertEquals($country2->id, $country2Divisions->first()->country_id);
    }

    #[Test]
    public function it_can_scope_by_parent()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        
        GeoDivision::factory()->city()->forCountry($country->id)->childOf($state->id)->create();
        GeoDivision::factory()->city()->forCountry($country->id)->create(); // No parent

        $stateChildren = GeoDivision::parent($state->id)->get();

        $this->assertCount(1, $stateChildren);
        $this->assertEquals($state->id, $stateChildren->first()->parent_id);
    }

    #[Test]
    public function it_has_country_relationship()
    {
        $country = Country::factory()->create();
        $division = GeoDivision::factory()->forCountry($country->id)->create();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsTo::class, $division->country());
        $this->assertEquals($country->id, $division->country->id);
    }

    #[Test]
    public function it_has_parent_relationship()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->childOf($state->id)->create();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsTo::class, $city->parent());
        $this->assertEquals($state->id, $city->parent->id);
    }

    #[Test]
    public function it_has_children_relationship()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->childOf($state->id)->create();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\HasMany::class, $state->children());
        $this->assertTrue($state->children->contains($city));
    }

    #[Test]
    public function it_has_descendants_relationship()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->childOf($state->id)->create();
        $district = GeoDivision::factory()->district()->forCountry($country->id)->childOf($city->id)->create();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\HasMany::class, $state->descendants());
        $descendants = $state->descendants()->get();
        
        $this->assertTrue($descendants->contains($city));
    }

    #[Test]
    public function it_can_get_ancestors()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->childOf($state->id)->create();
        $district = GeoDivision::factory()->district()->forCountry($country->id)->childOf($city->id)->create();

        $ancestors = $district->ancestors();

        $this->assertCount(2, $ancestors);
        $this->assertEquals($state->id, $ancestors->first()->id);
        $this->assertEquals($city->id, $ancestors->last()->id);
    }

    #[Test]
    public function it_can_check_if_is_state()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();

        $this->assertTrue($state->isState());
        $this->assertFalse($city->isState());
    }

    #[Test]
    public function it_can_check_if_is_city()
    {
        $country = Country::factory()->create();
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();

        $this->assertTrue($city->isCity());
        $this->assertFalse($state->isCity());
    }

    #[Test]
    public function it_can_check_if_is_district()
    {
        $country = Country::factory()->create();
        $city = GeoDivision::factory()->city()->forCountry($country->id)->create();
        $district = GeoDivision::factory()->district()->forCountry($country->id)->create();

        $this->assertTrue($district->isDistrict());
        $this->assertFalse($city->isDistrict());
    }

    #[Test]
    public function it_can_get_full_name_attribute()
    {
        $country = Country::factory()->create(['name' => 'United States']);
        $state = GeoDivision::factory()->state()->forCountry($country->id)->create(['name' => 'California']);
        $city = GeoDivision::factory()->city()->forCountry($country->id)->childOf($state->id)->create(['name' => 'Los Angeles']);

        $fullName = $city->full_name;

        $this->assertEquals('California, Los Angeles', $fullName);
    }

    #[Test]
    public function it_has_correct_table_structure()
    {
        $this->assertTrue(Schema::hasTable('geo_divisions'));

        $expectedColumns = [
            'id', 'code', 'name', 'native_name', 'type', 'country_id',
            'parent_id', 'level', 'path', 'latitude', 'longitude',
            'postal_code', 'sort_order', 'status', 'created_at', 'updated_at', 'deleted_at'
        ];

        foreach ($expectedColumns as $column) {
            $this->assertTrue(Schema::hasColumn('geo_divisions', $column));
        }
    }

    #[Test]
    public function it_has_factory()
    {
        $division = GeoDivision::factory()->make();

        $this->assertInstanceOf(GeoDivision::class, $division);
        $this->assertNotEmpty($division->name);
        $this->assertNotEmpty($division->type);
    }

    #[Test]
    public function factory_can_create_state()
    {
        $division = GeoDivision::factory()->state()->create();

        $this->assertEquals('state', $division->type);
        $this->assertEquals(1, $division->level);
        $this->assertNull($division->parent_id);
    }

    #[Test]
    public function factory_can_create_city()
    {
        $division = GeoDivision::factory()->city()->create();

        $this->assertEquals('city', $division->type);
        $this->assertEquals(2, $division->level);
    }

    #[Test]
    public function factory_can_create_district()
    {
        $division = GeoDivision::factory()->district()->create();

        $this->assertEquals('district', $division->type);
        $this->assertEquals(3, $division->level);
    }



    #[Test]
    public function it_can_soft_delete()
    {
        $country = Country::factory()->create();
        $division = GeoDivision::factory()->forCountry($country->id)->create();
        $divisionId = $division->id;

        $division->delete();

        $this->assertSoftDeleted('geo_divisions', ['id' => $divisionId]);
        $this->assertCount(0, GeoDivision::all());
        $this->assertCount(1, GeoDivision::withTrashed()->get());
    }

    #[Test]
    public function it_can_restore_soft_deleted()
    {
        $country = Country::factory()->create();
        $division = GeoDivision::factory()->forCountry($country->id)->create();
        $division->delete();

        $division->restore();

        $this->assertCount(1, GeoDivision::all());
        $this->assertNull($division->fresh()->deleted_at);
    }
}
