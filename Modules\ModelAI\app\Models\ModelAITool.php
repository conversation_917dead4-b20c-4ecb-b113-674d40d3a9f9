<?php

namespace Modules\ModelAI\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\ModelAI\Database\Factories\ModelAIToolFactory;

class ModelAITool extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'model_ai_tools';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'model_ai_id',
        'model_tool_id',
        'is_enabled',
        'configuration',
        'priority',
        'max_usage_per_request',
        'rate_limit_per_minute',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'model_ai_id' => 'integer',
        'model_tool_id' => 'integer',
        'is_enabled' => 'boolean',
        'configuration' => 'array',
        'priority' => 'integer',
        'max_usage_per_request' => 'integer',
        'rate_limit_per_minute' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope a query to only include enabled configurations.
     */
    public function scopeEnabled(Builder $query): Builder
    {
        return $query->where('is_enabled', true);
    }

    /**
     * Scope a query to only include disabled configurations.
     */
    public function scopeDisabled(Builder $query): Builder
    {
        return $query->where('is_enabled', false);
    }

    /**
     * Scope a query to order by priority.
     */
    public function scopeOrderedByPriority(Builder $query): Builder
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Scope a query to filter by model AI.
     */
    public function scopeForModel(Builder $query, int $modelId): Builder
    {
        return $query->where('model_ai_id', $modelId);
    }

    /**
     * Scope a query to filter by tool.
     */
    public function scopeForTool(Builder $query, int $toolId): Builder
    {
        return $query->where('model_tool_id', $toolId);
    }

    /**
     * Get the AI model that owns this configuration.
     */
    public function modelAI(): BelongsTo
    {
        return $this->belongsTo(ModelAI::class, 'model_ai_id');
    }

    /**
     * Get the tool that belongs to this configuration.
     */
    public function tool(): BelongsTo
    {
        return $this->belongsTo(ModelTool::class, 'model_tool_id');
    }

    /**
     * Check if configuration is enabled.
     */
    public function isEnabled(): bool
    {
        return $this->is_enabled;
    }

    /**
     * Check if tool usage is limited per request.
     */
    public function hasUsageLimit(): bool
    {
        return !is_null($this->max_usage_per_request) && $this->max_usage_per_request > 0;
    }

    /**
     * Check if tool has rate limit.
     */
    public function hasRateLimit(): bool
    {
        return !is_null($this->rate_limit_per_minute) && $this->rate_limit_per_minute > 0;
    }

    /**
     * Get configuration value by key.
     */
    public function getConfigValue(string $key, mixed $default = null): mixed
    {
        return data_get($this->configuration, $key, $default);
    }

    /**
     * Set configuration value by key.
     */
    public function setConfigValue(string $key, mixed $value): void
    {
        $config = $this->configuration ?? [];
        data_set($config, $key, $value);
        $this->configuration = $config;
    }

    /**
     * Check if usage is within limits.
     */
    public function isUsageWithinLimits(int $currentUsage): bool
    {
        if (!$this->hasUsageLimit()) {
            return true;
        }

        return $currentUsage < $this->max_usage_per_request;
    }

    /**
     * Get effective configuration (merge tool default with model-specific).
     */
    public function getEffectiveConfiguration(): array
    {
        $toolConfig = $this->tool->configuration ?? [];
        $modelConfig = $this->configuration ?? [];

        return array_merge($toolConfig, $modelConfig);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): ModelAIToolFactory
    {
        return ModelAIToolFactory::new();
    }
}
