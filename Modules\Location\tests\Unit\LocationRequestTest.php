<?php

namespace Modules\Location\Tests\Unit;

use Tests\TestCase;
use Modules\Location\Models\Country;
use Modules\Location\Models\GeoDivision;
use Modules\Location\Http\Requests\CountryRequest;
use Modules\Location\Http\Requests\GeoDivisionRequest;
use Modules\Location\Http\Requests\BulkLocationRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use PHPUnit\Framework\Attributes\Test;

class LocationRequestTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations for Location module
        $this->artisan('migrate', ['--path' => 'Modules/Location/database/migrations']);
    }

    #[Test]
    public function country_request_validates_required_fields()
    {
        $request = new CountryRequest();
        $rules = $request->rules();

        $validator = Validator::make([], $rules);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
        $this->assertArrayHasKey('iso_code_2', $validator->errors()->toArray());
        $this->assertArrayHasKey('status', $validator->errors()->toArray());
    }

    #[Test]
    public function country_request_validates_iso_code_2_format()
    {
        $request = new CountryRequest();
        $rules = $request->rules();

        // Test invalid length
        $validator = Validator::make(['iso_code_2' => 'USA'], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('iso_code_2', $validator->errors()->toArray());

        // Test non-alpha characters
        $validator = Validator::make(['iso_code_2' => '12'], $rules);
        $this->assertFalse($validator->passes());

        // Test valid format
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'status' => 'active'
        ], $rules);
        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function country_request_validates_iso_code_3_format()
    {
        $request = new CountryRequest();
        $rules = $request->rules();

        // Test invalid length
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'iso_code_3' => 'US',
            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('iso_code_3', $validator->errors()->toArray());

        // Test valid format
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'iso_code_3' => 'USA',
            'status' => 'active'
        ], $rules);
        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function country_request_validates_iso_numeric_format()
    {
        $request = new CountryRequest();
        $rules = $request->rules();

        // Test invalid format (non-numeric)
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'iso_numeric' => 'ABC',
            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());

        // Test invalid length
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'iso_numeric' => '12',
            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());

        // Test valid format
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'iso_numeric' => '840',
            'status' => 'active'
        ], $rules);
        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function country_request_validates_phone_code_format()
    {
        $request = new CountryRequest();
        $rules = $request->rules();

        // Test invalid format (letters)
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'phone_code' => 'ABC',
            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());

        // Test valid formats
        $validPhoneCodes = ['+1', '1', '+44', '86'];
        
        foreach ($validPhoneCodes as $phoneCode) {
            $validator = Validator::make([
                'name' => 'Test Country',
                'iso_code_2' => 'US',
                'phone_code' => $phoneCode,
                'status' => 'active'
            ], $rules);
            $this->assertTrue($validator->passes(), "Phone code {$phoneCode} should be valid");
        }
    }

    #[Test]
    public function country_request_validates_coordinates()
    {
        $request = new CountryRequest();
        $rules = $request->rules();

        // Test invalid latitude (out of range)
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'latitude' => 91,
            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());

        // Test invalid longitude (out of range)
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'longitude' => 181,
            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());

        // Test valid coordinates
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'latitude' => 37.0902,
            'longitude' => -95.7129,
            'status' => 'active'
        ], $rules);
        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function country_request_validates_status_values()
    {
        $request = new CountryRequest();
        $rules = $request->rules();

        // Test invalid status
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'status' => 'invalid'
        ], $rules);
        $this->assertFalse($validator->passes());

        // Test valid statuses
        $validStatuses = ['active', 'inactive'];
        
        foreach ($validStatuses as $status) {
            $validator = Validator::make([
                'name' => 'Test Country',
                'iso_code_2' => 'US',
                'status' => $status
            ], $rules);
            $this->assertTrue($validator->passes(), "Status {$status} should be valid");
        }
    }

    #[Test]
    public function country_request_validates_unique_iso_codes()
    {
        $existingCountry = Country::factory()->create([
            'iso_code_2' => 'US',
            'iso_code_3' => 'USA'
        ]);

        $request = new CountryRequest();
        $rules = $request->rules();

        // Test duplicate iso_code_2
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('iso_code_2', $validator->errors()->toArray());

        // Test duplicate iso_code_3
        $validator = Validator::make([
            'name' => 'Test Country',
            'iso_code_2' => 'CA',
            'iso_code_3' => 'USA',
            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('iso_code_3', $validator->errors()->toArray());
    }

    #[Test]
    public function geo_division_request_validates_required_fields()
    {
        $request = new GeoDivisionRequest();
        $rules = $request->rules();

        $validator = Validator::make([], $rules);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
        $this->assertArrayHasKey('type', $validator->errors()->toArray());
        $this->assertArrayHasKey('country_id', $validator->errors()->toArray());

        $this->assertArrayHasKey('status', $validator->errors()->toArray());
    }

    #[Test]
    public function geo_division_request_validates_type_values()
    {
        $country = Country::factory()->create();
        $request = new GeoDivisionRequest();
        $rules = $request->rules();

        // Test invalid type
        $validator = Validator::make([
            'name' => 'Test Division',
            'type' => 'invalid',
            'country_id' => $country->id,

            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());

        // Test valid types
        $validTypes = ['state', 'province', 'city', 'district', 'ward', 'commune'];
        
        foreach ($validTypes as $type) {
            $validator = Validator::make([
                'name' => 'Test Division',
                'type' => $type,
                'country_id' => $country->id,

                'status' => 'active'
            ], $rules);
            $this->assertTrue($validator->passes(), "Type {$type} should be valid");
        }
    }

    #[Test]
    public function geo_division_request_validates_country_exists()
    {
        $request = new GeoDivisionRequest();
        $rules = $request->rules();

        $validator = Validator::make([
            'name' => 'Test Division',
            'type' => 'state',
            'country_id' => 999,

            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('country_id', $validator->errors()->toArray());
    }

    #[Test]
    public function geo_division_request_validates_parent_exists()
    {
        $country = Country::factory()->create();
        $request = new GeoDivisionRequest();
        $rules = $request->rules();

        $validator = Validator::make([
            'name' => 'Test Division',
            'type' => 'city',
            'country_id' => $country->id,
            'parent_id' => 999,

            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('parent_id', $validator->errors()->toArray());
    }

    #[Test]
    public function geo_division_request_validates_coordinates()
    {
        $country = Country::factory()->create();
        $request = new GeoDivisionRequest();
        $rules = $request->rules();

        // Test invalid latitude
        $validator = Validator::make([
            'name' => 'Test Division',
            'type' => 'state',
            'country_id' => $country->id,
            'latitude' => 91,
            'is_capital' => false,
            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());

        // Test invalid longitude
        $validator = Validator::make([
            'name' => 'Test Division',
            'type' => 'state',
            'country_id' => $country->id,
            'longitude' => 181,
            'is_capital' => false,
            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());
    }



    #[Test]
    public function geo_division_request_validates_sort_order_minimum()
    {
        $country = Country::factory()->create();
        $request = new GeoDivisionRequest();
        $rules = $request->rules();

        $validator = Validator::make([
            'name' => 'Test Division',
            'type' => 'state',
            'country_id' => $country->id,
            'sort_order' => 0,
            'is_capital' => false,
            'status' => 'active'
        ], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('sort_order', $validator->errors()->toArray());
    }

    #[Test]
    public function bulk_location_request_validates_ids_array()
    {
        $request = new BulkLocationRequest();
        $rules = $request->rules();

        // Test missing ids
        $validator = Validator::make([], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids', $validator->errors()->toArray());

        // Test empty array
        $validator = Validator::make(['ids' => []], $rules);
        $this->assertFalse($validator->passes());

        // Test non-array
        $validator = Validator::make(['ids' => 'not-array'], $rules);
        $this->assertFalse($validator->passes());
    }

    #[Test]
    public function bulk_location_request_validates_ids_exist()
    {
        $country = Country::factory()->create();
        
        $request = new BulkLocationRequest();
        $request->setRouteResolver(function () {
            return new class {
                public function getName() { return 'countries.bulk-delete'; }
            };
        });
        
        $rules = $request->rules();

        // Test non-existent IDs
        $validator = Validator::make(['ids' => [999, 998]], $rules);
        $this->assertFalse($validator->passes());

        // Test valid IDs
        $validator = Validator::make(['ids' => [$country->id]], $rules);
        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function country_request_has_correct_attributes()
    {
        $request = new CountryRequest();
        $attributes = $request->attributes();

        $expectedAttributes = [
            'name' => __('Country Name'),
            'native_name' => __('Native Name'),
            'iso_code_2' => __('ISO Code (2 letters)'),
            'iso_code_3' => __('ISO Code (3 letters)'),
            'status' => __('Status'),
        ];

        foreach ($expectedAttributes as $key => $value) {
            $this->assertArrayHasKey($key, $attributes);
        }
    }

    #[Test]
    public function geo_division_request_has_correct_attributes()
    {
        $request = new GeoDivisionRequest();
        $attributes = $request->attributes();

        $expectedAttributes = [
            'name' => __('Name'),
            'type' => __('Type'),
            'country_id' => __('Country'),
            'parent_id' => __('Parent Division'),
            'status' => __('Status'),
        ];

        foreach ($expectedAttributes as $key => $value) {
            $this->assertArrayHasKey($key, $attributes);
        }
    }

    #[Test]
    public function country_request_has_custom_messages()
    {
        $request = new CountryRequest();
        $messages = $request->messages();

        $this->assertArrayHasKey('iso_code_2.size', $messages);
        $this->assertArrayHasKey('iso_code_3.size', $messages);
        $this->assertArrayHasKey('latitude.between', $messages);
        $this->assertArrayHasKey('longitude.between', $messages);
    }

    #[Test]
    public function geo_division_request_has_custom_messages()
    {
        $request = new GeoDivisionRequest();
        $messages = $request->messages();

        $this->assertArrayHasKey('latitude.between', $messages);
        $this->assertArrayHasKey('longitude.between', $messages);
        $this->assertArrayHasKey('parent_id.different', $messages);

    }
}
