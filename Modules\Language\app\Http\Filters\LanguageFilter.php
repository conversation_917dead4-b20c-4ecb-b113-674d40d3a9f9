<?php

namespace Modules\Language\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class LanguageFilter extends AbstractFilter
{

    protected function filters(): array
    {
        return [
            'code' => 'like',
            'name' => 'like',
            'native_name' => 'like',
            'status' => 'exact',
            'is_default' => 'exact',
            'is_trashed' => 'trashed',
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
        ];
    }
}
