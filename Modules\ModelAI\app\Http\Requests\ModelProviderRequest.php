<?php

namespace Modules\ModelAI\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

/**
 * @property mixed $id
 * @property mixed $provider_id
 */
class ModelProviderRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $providerId = $this->route('provider') ?? $this->route('id');

        return [
            'key' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-z0-9_-]+$/',
                Rule::unique('model_providers', 'key')->ignore($providerId),
            ],
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'base_url' => [
                'nullable',
                'string',
                'url',
                'max:500',
            ],
            'api_key' => [
                'nullable',
                'string',
                'max:500',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(['active', 'inactive']),
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'key' => 'provider key',
            'name' => 'provider name',
            'description' => 'description',
            'base_url' => 'base URL',
            'credentials.api_key' => 'API key',
            'credentials.organization_id' => 'organization ID',
            'status' => 'status',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'key.required' => 'The provider key is required.',
            'key.unique' => 'This provider key is already taken.',
            'key.regex' => 'The provider key may only contain lowercase letters, numbers, hyphens, and underscores.',
            'name.required' => 'The provider name is required.',
            'base_url.url' => 'The base URL must be a valid URL.',
            'status.in' => 'The status must be either active or inactive.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert key to lowercase and replace spaces with underscores
        if ($this->has('key')) {
            $this->merge([
                'key' => strtolower(str_replace(' ', '_', $this->input('key')))
            ]);
        }

        // Ensure base_url ends with slash if provided
        if ($this->has('base_url') && $this->input('base_url')) {
            $baseUrl = rtrim($this->input('base_url'), '/') . '/';
            $this->merge(['base_url' => $baseUrl]);
        }
    }
}
