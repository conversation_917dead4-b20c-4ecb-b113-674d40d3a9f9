<?php

namespace Modules\Currency\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Modules\Currency\Http\Filters\CurrencyFilter;
use Modules\Currency\Models\Currency;
use PHPUnit\Framework\Attributes\Test;

class CurrencyFilterTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Currency module
        $this->artisan('migrate', ['--path' => 'Modules/Currency/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_can_filter_by_code()
    {
        $usdCurrency = Currency::factory()->create(['code' => 'USD']);
        $eurCurrency = Currency::factory()->create(['code' => 'EUR']);
        $gbpCurrency = Currency::factory()->create(['code' => 'GBP']);

        $request = new Request(['code' => 'USD']);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertTrue($results->contains($usdCurrency));
        $this->assertFalse($results->contains($eurCurrency));
        $this->assertFalse($results->contains($gbpCurrency));
    }

    #[Test]
    public function it_can_filter_by_partial_code()
    {
        $usdCurrency = Currency::factory()->create(['code' => 'USD']);
        $eurCurrency = Currency::factory()->create(['code' => 'EUR']);
        $gbpCurrency = Currency::factory()->create(['code' => 'GBP']);

        $request = new Request(['code' => 'US']);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertTrue($results->contains($usdCurrency));
        $this->assertFalse($results->contains($eurCurrency));
        $this->assertFalse($results->contains($gbpCurrency));
    }

    #[Test]
    public function it_can_filter_by_name()
    {
        $usdCurrency = Currency::factory()->create(['name' => 'US Dollar']);
        $eurCurrency = Currency::factory()->create(['name' => 'Euro']);
        $gbpCurrency = Currency::factory()->create(['name' => 'British Pound']);

        $request = new Request(['name' => 'Dollar']);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertTrue($results->contains($usdCurrency));
        $this->assertFalse($results->contains($eurCurrency));
        $this->assertFalse($results->contains($gbpCurrency));
    }

    #[Test]
    public function it_can_filter_by_partial_name()
    {
        $usdCurrency = Currency::factory()->create(['name' => 'US Dollar']);
        $eurCurrency = Currency::factory()->create(['name' => 'Euro']);
        $gbpCurrency = Currency::factory()->create(['name' => 'British Pound']);

        $request = new Request(['name' => 'US']);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertTrue($results->contains($usdCurrency));
        $this->assertFalse($results->contains($eurCurrency));
        $this->assertFalse($results->contains($gbpCurrency));
    }

    #[Test]
    public function it_can_filter_by_symbol()
    {
        $usdCurrency = Currency::factory()->create(['symbol' => '$']);
        $eurCurrency = Currency::factory()->create(['symbol' => '€']);
        $gbpCurrency = Currency::factory()->create(['symbol' => '£']);

        $request = new Request(['symbol' => '$']);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertTrue($results->contains($usdCurrency));
        $this->assertFalse($results->contains($eurCurrency));
        $this->assertFalse($results->contains($gbpCurrency));
    }

    #[Test]
    public function it_can_filter_by_status()
    {
        $activeCurrency = Currency::factory()->create(['status' => 'active']);
        $inactiveCurrency = Currency::factory()->create(['status' => 'inactive']);

        $request = new Request(['status' => 'active']);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertTrue($results->contains($activeCurrency));
        $this->assertFalse($results->contains($inactiveCurrency));
    }

    #[Test]
    public function it_can_filter_by_decimal_digits()
    {
        $currency2Digits = Currency::factory()->create(['decimal_digits' => 2]);
        $currency0Digits = Currency::factory()->create(['decimal_digits' => 0]);
        $currency3Digits = Currency::factory()->create(['decimal_digits' => 3]);

        $request = new Request(['decimal_digits' => 2]);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertTrue($results->contains($currency2Digits));
        $this->assertFalse($results->contains($currency0Digits));
        $this->assertFalse($results->contains($currency3Digits));
    }

    #[Test]
    public function it_can_filter_by_sort_order()
    {
        $currency1 = Currency::factory()->create(['sort_order' => 1]);
        $currency2 = Currency::factory()->create(['sort_order' => 2]);
        $currency3 = Currency::factory()->create(['sort_order' => 3]);

        $request = new Request(['sort_order' => 2]);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertFalse($results->contains($currency1));
        $this->assertTrue($results->contains($currency2));
        $this->assertFalse($results->contains($currency3));
    }

    #[Test]
    public function it_can_filter_by_created_from_date()
    {
        $oldCurrency = Currency::factory()->create(['created_at' => now()->subDays(10)]);
        $newCurrency = Currency::factory()->create(['created_at' => now()->subDays(2)]);

        $request = new Request(['created_from' => now()->subDays(5)->format('Y-m-d')]);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertFalse($results->contains($oldCurrency));
        $this->assertTrue($results->contains($newCurrency));
    }

    #[Test]
    public function it_can_filter_by_created_to_date()
    {
        $oldCurrency = Currency::factory()->create(['created_at' => now()->subDays(10)]);
        $newCurrency = Currency::factory()->create(['created_at' => now()->subDays(2)]);

        $request = new Request(['created_to' => now()->subDays(5)->format('Y-m-d')]);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertTrue($results->contains($oldCurrency));
        $this->assertFalse($results->contains($newCurrency));
    }

    #[Test]
    public function it_can_filter_by_date_range()
    {
        $oldCurrency = Currency::factory()->create(['created_at' => now()->subDays(10)]);
        $middleCurrency = Currency::factory()->create(['created_at' => now()->subDays(5)]);
        $newCurrency = Currency::factory()->create(['created_at' => now()->subDays(1)]);

        $request = new Request([
            'created_from' => now()->subDays(7)->format('Y-m-d'),
            'created_to' => now()->subDays(3)->format('Y-m-d'),
        ]);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertFalse($results->contains($oldCurrency));
        $this->assertTrue($results->contains($middleCurrency));
        $this->assertFalse($results->contains($newCurrency));
    }

    #[Test]
    public function it_can_combine_multiple_filters()
    {
        $matchingCurrency = Currency::factory()->create([
            'code' => 'USD',
            'name' => 'US Dollar',
            'status' => 'active',
            'decimal_digits' => 2,
        ]);
        $nonMatchingCurrency1 = Currency::factory()->create([
            'code' => 'EUR',
            'name' => 'Euro',
            'status' => 'active',
            'decimal_digits' => 2,
        ]);
        $nonMatchingCurrency2 = Currency::factory()->create([
            'code' => 'USD',
            'name' => 'US Dollar',
            'status' => 'inactive',
            'decimal_digits' => 2,
        ]);

        $request = new Request([
            'code' => 'USD',
            'status' => 'active',
            'decimal_digits' => 2,
        ]);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertTrue($results->contains($matchingCurrency));
        $this->assertFalse($results->contains($nonMatchingCurrency1));
        $this->assertFalse($results->contains($nonMatchingCurrency2));
    }

    #[Test]
    public function it_returns_all_currencies_when_no_filters_applied()
    {
        $currency1 = Currency::factory()->create();
        $currency2 = Currency::factory()->create();
        $currency3 = Currency::factory()->create();

        $request = new Request([]);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertTrue($results->contains($currency1));
        $this->assertTrue($results->contains($currency2));
        $this->assertTrue($results->contains($currency3));
    }

    #[Test]
    public function it_handles_empty_filter_values()
    {
        $currency1 = Currency::factory()->create();
        $currency2 = Currency::factory()->create();

        $request = new Request([
            'code' => '',
            'name' => '',
            'status' => '',
        ]);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertTrue($results->contains($currency1));
        $this->assertTrue($results->contains($currency2));
    }

    #[Test]
    public function it_handles_null_filter_values()
    {
        $currency1 = Currency::factory()->create();
        $currency2 = Currency::factory()->create();

        $request = new Request([
            'code' => null,
            'name' => null,
            'status' => null,
        ]);
        $filter = new CurrencyFilter($request);

        $results = Currency::query()->filter($filter)->get();

        $this->assertTrue($results->contains($currency1));
        $this->assertTrue($results->contains($currency2));
    }

    #[Test]
    public function it_is_case_insensitive_for_like_filters()
    {
        $currency = Currency::factory()->create([
            'code' => 'USD',
            'name' => 'US Dollar',
        ]);

        // Test lowercase search
        $request = new Request(['code' => 'usd']);
        $filter = new CurrencyFilter($request);
        $results = Currency::query()->filter($filter)->get();
        $this->assertTrue($results->contains($currency));

        // Test mixed case search
        $request = new Request(['name' => 'us dollar']);
        $filter = new CurrencyFilter($request);
        $results = Currency::query()->filter($filter)->get();
        $this->assertTrue($results->contains($currency));
    }

    #[Test]
    public function it_has_correct_filter_configuration()
    {
        $filter = new CurrencyFilter(new Request());
        
        // Use reflection to access protected filters method
        $reflection = new \ReflectionClass($filter);
        $method = $reflection->getMethod('filters');
        $method->setAccessible(true);
        $filters = $method->invoke($filter);

        $expectedFilters = [
            'code' => 'like',
            'name' => 'like',
            'symbol' => 'like',
            'status' => 'exact',
            'decimal_digits' => 'exact',
            'sort_order' => 'exact',
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
        ];

        $this->assertEquals($expectedFilters, $filters);
    }
}
