<?php

namespace Modules\Notification\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

class NotificationPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedNotificationPermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed notification module permissions.
     */
    private function seedNotificationPermissions(): void
    {
        $permissions = [
            [
                'name' => 'notification.view',
                'display_name' => 'View Notifications',
                'description' => 'Permission to view notifications list and details',
                'module_name' => 'notification',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'notification.create',
                'display_name' => 'Create Notifications',
                'description' => 'Permission to create and send notifications',
                'module_name' => 'notification',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'notification.edit',
                'display_name' => 'Edit Notifications',
                'description' => 'Permission to update notification templates and settings',
                'module_name' => 'notification',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'notification.delete',
                'display_name' => 'Soft Delete Notifications',
                'description' => 'Permission to soft delete notifications and templates',
                'module_name' => 'notification',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'notification.destroy',
                'display_name' => 'Force Delete Notifications',
                'description' => 'Permission to permanently delete notifications and templates',
                'module_name' => 'notification',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
            [
                'name' => 'notification.restore',
                'display_name' => 'Restore Notifications',
                'description' => 'Permission to restore soft deleted notifications',
                'module_name' => 'notification',
                'sort_order' => 6,
                'guard_name' => 'api',
            ],
            [
                'name' => 'notification.send',
                'display_name' => 'Send Notifications',
                'description' => 'Permission to send notifications to users',
                'module_name' => 'notification',
                'sort_order' => 7,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign notification permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();
        
        if ($superAdmin) {
            $notificationPermissions = Permission::where('module_name', 'notification')->where('guard_name', 'api')->get();
            
            foreach ($notificationPermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}
