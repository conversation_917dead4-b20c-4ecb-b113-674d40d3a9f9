<?php

namespace Modules\Currency\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Modules\Currency\Models\Currency;
use Modules\Currency\Models\ExchangeRate;

class ExchangeRateService
{
    /**
     * Update exchange rates from external API.
     */
    public function updateRatesFromAPI(string $baseCurrency = 'USD', array $targetCurrencies = [], string $apiProvider = 'mock'): array
    {
        if (empty($targetCurrencies)) {
            $targetCurrencies = Currency::active()->pluck('code')->toArray();
        }

        $results = [
            'updated' => 0,
            'failed' => 0,
            'rates' => []
        ];

        foreach ($targetCurrencies as $targetCurrency) {
            if ($baseCurrency === $targetCurrency) continue;

            try {
                $rate = $this->fetchExchangeRate($baseCurrency, $targetCurrency, $apiProvider);
                
                if ($rate) {
                    $this->updateExchangeRate($baseCurrency, $targetCurrency, $rate);
                    $results['updated']++;
                    $results['rates'][] = [
                        'base' => $baseCurrency,
                        'target' => $targetCurrency,
                        'rate' => $rate,
                        'status' => 'success'
                    ];
                } else {
                    $results['failed']++;
                    $results['rates'][] = [
                        'base' => $baseCurrency,
                        'target' => $targetCurrency,
                        'rate' => null,
                        'status' => 'failed',
                        'error' => 'No rate returned from API'
                    ];
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['rates'][] = [
                    'base' => $baseCurrency,
                    'target' => $targetCurrency,
                    'rate' => null,
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
                
                Log::error("Exchange rate update failed", [
                    'base' => $baseCurrency,
                    'target' => $targetCurrency,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Fetch exchange rate from external API.
     */
    public function fetchExchangeRate(string $baseCurrency, string $targetCurrency, string $apiProvider = 'freecurrency'): ?float
    {
        switch ($apiProvider) {
            case 'freecurrency':
                return $this->fetchFromFreeCurrencyAPI($baseCurrency, $targetCurrency);

            case 'fixer':
                return $this->fetchFromFixer($baseCurrency, $targetCurrency);

            case 'exchangerate':
                return $this->fetchFromExchangeRateAPI($baseCurrency, $targetCurrency);

            case 'currencylayer':
                return $this->fetchFromCurrencyLayer($baseCurrency, $targetCurrency);

            case 'mock':
                return $this->fetchMockRate($baseCurrency, $targetCurrency);

            default:
                throw new \InvalidArgumentException("Unknown API provider: {$apiProvider}");
        }
    }

    /**
     * Update exchange rate in database.
     */
    public function updateExchangeRate(string $baseCurrency, string $targetCurrency, float $rate): ExchangeRate
    {
        // Update main rate
        $exchangeRate = ExchangeRate::updateOrCreate(
            [
                'base_currency' => $baseCurrency,
                'target_currency' => $targetCurrency
            ],
            [
                'rate' => $rate,
                'fetched_at' => now()
            ]
        );

        // Update reverse rate
        ExchangeRate::updateOrCreate(
            [
                'base_currency' => $targetCurrency,
                'target_currency' => $baseCurrency
            ],
            [
                'rate' => 1 / $rate,
                'fetched_at' => now()
            ]
        );

        return $exchangeRate;
    }

    /**
     * Get stale exchange rates.
     */
    public function getStaleRates(int $hoursThreshold = 24): \Illuminate\Database\Eloquent\Collection
    {
        return ExchangeRate::where('fetched_at', '<', now()->subHours($hoursThreshold))
            ->orWhereNull('fetched_at')
            ->with(['baseCurrency', 'targetCurrency'])
            ->get();
    }

    /**
     * Check if rates need update.
     */
    public function needsUpdate(string $baseCurrency, array $targetCurrencies, int $hoursThreshold = 1): bool
    {
        foreach ($targetCurrencies as $targetCurrency) {
            if ($baseCurrency === $targetCurrency) continue;

            $rate = ExchangeRate::where('base_currency', $baseCurrency)
                ->where('target_currency', $targetCurrency)
                ->first();

            if (!$rate || !$rate->fetched_at || $rate->fetched_at->lt(now()->subHours($hoursThreshold))) {
                return true;
            }
        }

        return false;
    }

    /**
     * Fetch rate from FreeCurrencyAPI.
     */
    private function fetchFromFreeCurrencyAPI(string $baseCurrency, string $targetCurrency): ?float
    {
        $apiKey = setting('currency.currency_freecurrency_api_key');

        if (!$apiKey) {
            Log::warning('FreeCurrencyAPI key not configured');
            return null;
        }

        $timeout = (int) setting('currency.currency_api_timeout', 30);

        // FreeCurrencyAPI uses latest endpoint with base_currency and currencies parameters
        $response = Http::timeout($timeout)
            ->get('https://api.freecurrencyapi.com/v1/latest', [
                'apikey' => $apiKey,
                'base_currency' => $baseCurrency,
                'currencies' => $targetCurrency
            ]);

        if ($response->successful()) {
            $data = $response->json();

            // FreeCurrencyAPI returns data in format: {"data": {"EUR": 0.85}}
            if (isset($data['data'][$targetCurrency])) {
                return (float) $data['data'][$targetCurrency];
            }
        }

        Log::error('FreeCurrencyAPI request failed', [
            'status' => $response->status(),
            'body' => $response->body()
        ]);

        return null;
    }

    /**
     * Fetch rate from Fixer.io API.
     */
    private function fetchFromFixer(string $baseCurrency, string $targetCurrency): ?float
    {
        $apiKey = setting('currency.currency_fixer_api_key');

        if (!$apiKey) {
            Log::warning('Fixer API key not configured');
            return null;
        }

        $timeout = (int) setting('currency.currency_api_timeout', 30);
        $response = Http::timeout($timeout)
            ->get('http://data.fixer.io/api/latest', [
                'access_key' => $apiKey,
                'base' => $baseCurrency,
                'symbols' => $targetCurrency
            ]);

        if ($response->successful()) {
            $data = $response->json();
            return $data['rates'][$targetCurrency] ?? null;
        }

        Log::error('Fixer API request failed', [
            'status' => $response->status(),
            'body' => $response->body()
        ]);

        return null;
    }

    /**
     * Fetch rate from ExchangeRate-API.
     */
    private function fetchFromExchangeRateAPI(string $baseCurrency, string $targetCurrency): ?float
    {
        $timeout = (int) setting('currency.currency_api_timeout', 30);

        $response = Http::timeout($timeout)
            ->get("https://api.exchangerate-api.com/v4/latest/{$baseCurrency}");

        if ($response->successful()) {
            $data = $response->json();
            return $data['rates'][$targetCurrency] ?? null;
        }

        Log::error('ExchangeRate-API request failed', [
            'status' => $response->status(),
            'body' => $response->body()
        ]);

        return null;
    }

    /**
     * Fetch rate from CurrencyLayer API.
     */
    private function fetchFromCurrencyLayer(string $baseCurrency, string $targetCurrency): ?float
    {
        $apiKey = setting('currency.currency_currencylayer_api_key');

        if (!$apiKey) {
            Log::warning('CurrencyLayer API key not configured');
            return null;
        }

        $timeout = (int) setting('currency.currency_api_timeout', 30);
        $response = Http::timeout($timeout)
            ->get('http://api.currencylayer.com/live', [
                'access_key' => $apiKey,
                'source' => $baseCurrency,
                'currencies' => $targetCurrency
            ]);

        if ($response->successful()) {
            $data = $response->json();
            $key = $baseCurrency . $targetCurrency;
            return $data['quotes'][$key] ?? null;
        }

        Log::error('CurrencyLayer API request failed', [
            'status' => $response->status(),
            'body' => $response->body()
        ]);

        return null;
    }

    /**
     * Generate mock exchange rate for testing.
     */
    private function fetchMockRate(string $baseCurrency, string $targetCurrency): float
    {
        // Mock rates for testing
        $mockRates = [
            'USD' => [
                'VND' => 23000 + rand(-500, 500),
                'EUR' => 0.85 + (rand(-50, 50) / 1000),
                'GBP' => 0.73 + (rand(-30, 30) / 1000),
                'JPY' => 110 + rand(-5, 5),
            ],
            'EUR' => [
                'USD' => 1.18 + (rand(-50, 50) / 1000),
                'VND' => 27000 + rand(-1000, 1000),
                'GBP' => 0.86 + (rand(-30, 30) / 1000),
                'JPY' => 129 + rand(-5, 5),
            ],
        ];

        return $mockRates[$baseCurrency][$targetCurrency] ?? 1.0;
    }

    /**
     * Batch fetch exchange rates from API.
     */
    public function batchFetchExchangeRates(string $baseCurrency, array $targetCurrencies, string $apiProvider = 'freecurrency'): array
    {
        $rates = [];

        foreach ($targetCurrencies as $targetCurrency) {
            $rate = $this->fetchExchangeRate($baseCurrency, $targetCurrency, $apiProvider);
            if ($rate !== null) {
                $rates[$targetCurrency] = $rate;
            }
        }

        return $rates;
    }

    /**
     * Get supported API providers.
     */
    public function getSupportedApiProviders(): array
    {
        return ['freecurrency', 'exchangerate', 'fixer', 'currencylayer', 'mock'];
    }

    /**
     * Validate API provider.
     */
    public function isValidApiProvider(string $provider): bool
    {
        return in_array($provider, $this->getSupportedApiProviders());
    }

    /**
     * Get API provider information.
     */
    public function getApiProviderInfo(string $provider): ?array
    {
        $providers = [
            'freecurrency' => [
                'name' => 'FreeCurrencyAPI',
                'requires_key' => true,
                'free_tier' => '5000 requests/month',
                'url' => 'https://freecurrencyapi.com'
            ],
            'exchangerate' => [
                'name' => 'ExchangeRate-API',
                'requires_key' => false,
                'free_tier' => '1500 requests/month',
                'url' => 'https://exchangerate-api.com'
            ],
            'fixer' => [
                'name' => 'Fixer.io',
                'requires_key' => true,
                'free_tier' => '1000 requests/month',
                'url' => 'https://fixer.io'
            ],
            'currencylayer' => [
                'name' => 'CurrencyLayer',
                'requires_key' => true,
                'free_tier' => '1000 requests/month',
                'url' => 'https://currencylayer.com'
            ],
            'mock' => [
                'name' => 'Mock Data',
                'requires_key' => false,
                'free_tier' => 'Unlimited',
                'url' => null
            ]
        ];

        return $providers[$provider] ?? null;
    }

    /**
     * Check if API key is configured.
     */
    public function isApiKeyConfigured(string $provider): bool
    {
        switch ($provider) {
            case 'freecurrency':
                return !empty(setting('currency.currency_freecurrency_api_key'));
            case 'fixer':
                return !empty(setting('currency.currency_fixer_api_key'));
            case 'currencylayer':
                return !empty(setting('currency.currency_currencylayer_api_key'));
            case 'exchangerate':
            case 'mock':
                return true;
            default:
                return false;
        }
    }

    /**
     * Get last update time for currency pair.
     */
    public function getLastUpdateTime(string $baseCurrency, string $targetCurrency): ?\Carbon\Carbon
    {
        $rate = \Modules\Currency\Models\ExchangeRate::where('base_currency', $baseCurrency)
            ->where('target_currency', $targetCurrency)
            ->first();

        return $rate?->fetched_at;
    }

    /**
     * Check if single rate needs update.
     */
    public function needsSingleUpdate(string $baseCurrency, string $targetCurrency): bool
    {
        $rate = \Modules\Currency\Models\ExchangeRate::where('base_currency', $baseCurrency)
            ->where('target_currency', $targetCurrency)
            ->first();

        return !$rate || $rate->isStale();
    }

    /**
     * Get cache key for rate.
     */
    public function getCacheKey(string $baseCurrency, string $targetCurrency): string
    {
        return "exchange_rates.{$baseCurrency}.{$targetCurrency}";
    }

    /**
     * Clear rate cache.
     */
    public function clearRateCache(string $baseCurrency, string $targetCurrency): bool
    {
        return \Illuminate\Support\Facades\Cache::forget($this->getCacheKey($baseCurrency, $targetCurrency));
    }

    /**
     * Get historical rate (mock implementation).
     */
    public function getHistoricalRate(string $baseCurrency, string $targetCurrency, string $date): ?float
    {
        // Validate date format
        try {
            \Carbon\Carbon::createFromFormat('Y-m-d', $date);
        } catch (\Exception $e) {
            return null;
        }

        // For now, return current rate (in real implementation, would fetch historical data)
        return $this->fetchExchangeRate($baseCurrency, $targetCurrency, 'freecurrency');
    }
}
