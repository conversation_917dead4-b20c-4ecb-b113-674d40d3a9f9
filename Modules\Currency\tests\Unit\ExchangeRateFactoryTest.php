<?php

namespace Modules\Currency\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Currency\Models\Currency;
use Modules\Currency\Models\ExchangeRate;
use PHPUnit\Framework\Attributes\Test;

class ExchangeRateFactoryTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Currency module
        $this->artisan('migrate', ['--path' => 'Modules/Currency/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);

        // Create some currencies for testing
        Currency::factory()->create(['code' => 'USD']);
        Currency::factory()->create(['code' => 'EUR']);
        Currency::factory()->create(['code' => 'GBP']);
        Currency::factory()->create(['code' => 'JPY']);
        Currency::factory()->create(['code' => 'VND']);
    }

    #[Test]
    public function it_can_create_an_exchange_rate_using_factory()
    {
        $exchangeRate = ExchangeRate::factory()->create();

        $this->assertInstanceOf(ExchangeRate::class, $exchangeRate);
        $this->assertDatabaseHas('exchange_rates', [
            'id' => $exchangeRate->id,
        ]);
    }

    #[Test]
    public function it_generates_valid_exchange_rate_attributes()
    {
        $exchangeRate = ExchangeRate::factory()->create();

        // Check required attributes are present
        $this->assertNotNull($exchangeRate->base_currency);
        $this->assertNotNull($exchangeRate->target_currency);
        $this->assertNotNull($exchangeRate->rate);
        $this->assertNotNull($exchangeRate->fetched_at);

        // Check attribute types and formats
        $this->assertIsString($exchangeRate->base_currency);
        $this->assertIsString($exchangeRate->target_currency);
        $this->assertIsFloat($exchangeRate->rate);
        $this->assertInstanceOf(\Carbon\Carbon::class, $exchangeRate->fetched_at);

        // Check currency codes are different
        $this->assertNotEquals($exchangeRate->base_currency, $exchangeRate->target_currency);

        // Check currency codes are 3 characters
        $this->assertEquals(3, strlen($exchangeRate->base_currency));
        $this->assertEquals(3, strlen($exchangeRate->target_currency));

        // Check rate is positive
        $this->assertGreaterThan(0, $exchangeRate->rate);
    }

    #[Test]
    public function it_generates_realistic_exchange_rates()
    {
        $exchangeRate = ExchangeRate::factory()->create();

        // Check rate is within reasonable bounds
        $this->assertGreaterThan(0.001, $exchangeRate->rate);
        $this->assertLessThan(100000, $exchangeRate->rate);

        // Check fetched_at is within last week
        $this->assertTrue($exchangeRate->fetched_at->gte(now()->subWeek()));
        $this->assertTrue($exchangeRate->fetched_at->lte(now()));
    }

    #[Test]
    public function it_can_create_fresh_exchange_rate_state()
    {
        $exchangeRate = ExchangeRate::factory()->fresh()->create();

        // Fresh rates should be updated within last hour
        $this->assertTrue($exchangeRate->fetched_at->gte(now()->subHour()));
        $this->assertTrue($exchangeRate->fetched_at->lte(now()));
    }

    #[Test]
    public function it_can_create_stale_exchange_rate_state()
    {
        $exchangeRate = ExchangeRate::factory()->stale()->create();

        // Stale rates should be older than 1 day
        $this->assertTrue($exchangeRate->fetched_at->lte(now()->subDay()));
        $this->assertTrue($exchangeRate->fetched_at->gte(now()->subWeek()));
    }

    #[Test]
    public function it_can_create_exchange_rate_for_specific_currency_pair()
    {
        $exchangeRate = ExchangeRate::factory()->forCurrencyPair('USD', 'EUR')->create();

        $this->assertEquals('USD', $exchangeRate->base_currency);
        $this->assertEquals('EUR', $exchangeRate->target_currency);
    }

    #[Test]
    public function it_can_create_exchange_rate_with_specific_rate()
    {
        $specificRate = 1.25;
        $exchangeRate = ExchangeRate::factory()->withRate($specificRate)->create();

        $this->assertEquals($specificRate, $exchangeRate->rate);
    }

    #[Test]
    public function it_generates_realistic_rates_for_known_currency_pairs()
    {
        // Test USD to VND (should be high value)
        $usdVndRate = ExchangeRate::factory()->forCurrencyPair('USD', 'VND')->create();
        $this->assertGreaterThan(20000, $usdVndRate->rate);
        $this->assertLessThan(25000, $usdVndRate->rate);

        // Test USD to EUR (should be less than 1)
        $usdEurRate = ExchangeRate::factory()->forCurrencyPair('USD', 'EUR')->create();
        $this->assertGreaterThan(0.7, $usdEurRate->rate);
        $this->assertLessThan(1.0, $usdEurRate->rate);

        // Test USD to JPY (should be around 100-120)
        $usdJpyRate = ExchangeRate::factory()->forCurrencyPair('USD', 'JPY')->create();
        $this->assertGreaterThan(90, $usdJpyRate->rate);
        $this->assertLessThan(130, $usdJpyRate->rate);
    }

    #[Test]
    public function it_can_create_multiple_exchange_rates()
    {
        $exchangeRates = ExchangeRate::factory()->count(5)->create();

        $this->assertCount(5, $exchangeRates);
        $exchangeRates->each(function ($exchangeRate) {
            $this->assertInstanceOf(ExchangeRate::class, $exchangeRate);
        });
    }

    #[Test]
    public function it_can_override_factory_attributes()
    {
        $exchangeRate = ExchangeRate::factory()->create([
            'base_currency' => 'GBP',
            'target_currency' => 'USD',
            'rate' => 1.30,
            'fetched_at' => now()->subHours(2),
        ]);

        $this->assertEquals('GBP', $exchangeRate->base_currency);
        $this->assertEquals('USD', $exchangeRate->target_currency);
        $this->assertEquals(1.30, $exchangeRate->rate);
        $this->assertTrue($exchangeRate->fetched_at->eq(now()->subHours(2)));
    }

    #[Test]
    public function it_can_make_exchange_rate_without_persisting()
    {
        $exchangeRate = ExchangeRate::factory()->make();

        $this->assertInstanceOf(ExchangeRate::class, $exchangeRate);
        $this->assertNull($exchangeRate->id);
        $this->assertDatabaseMissing('exchange_rates', [
            'base_currency' => $exchangeRate->base_currency,
            'target_currency' => $exchangeRate->target_currency,
        ]);
    }

    #[Test]
    public function it_generates_different_exchange_rates_on_multiple_calls()
    {
        $exchangeRate1 = ExchangeRate::factory()->create();
        $exchangeRate2 = ExchangeRate::factory()->create();

        // They should be different instances
        $this->assertNotEquals($exchangeRate1->id, $exchangeRate2->id);
        
        // They should have valid but potentially different currency pairs
        $this->assertNotNull($exchangeRate1->base_currency);
        $this->assertNotNull($exchangeRate2->base_currency);
    }

    #[Test]
    public function it_can_combine_states()
    {
        $exchangeRate = ExchangeRate::factory()
            ->forCurrencyPair('USD', 'EUR')
            ->fresh()
            ->withRate(0.85)
            ->create();

        $this->assertEquals('USD', $exchangeRate->base_currency);
        $this->assertEquals('EUR', $exchangeRate->target_currency);
        $this->assertEquals(0.85, $exchangeRate->rate);
        $this->assertTrue($exchangeRate->fetched_at->gte(now()->subHour()));
    }

    #[Test]
    public function it_can_create_exchange_rates_with_sequence()
    {
        $exchangeRates = ExchangeRate::factory()
            ->count(3)
            ->sequence(
                ['base_currency' => 'USD', 'target_currency' => 'EUR', 'rate' => 0.85],
                ['base_currency' => 'USD', 'target_currency' => 'GBP', 'rate' => 0.75],
                ['base_currency' => 'EUR', 'target_currency' => 'GBP', 'rate' => 0.88]
            )
            ->create();

        $this->assertEquals('USD', $exchangeRates[0]->base_currency);
        $this->assertEquals('EUR', $exchangeRates[0]->target_currency);
        $this->assertEquals(0.85, $exchangeRates[0]->rate);

        $this->assertEquals('USD', $exchangeRates[1]->base_currency);
        $this->assertEquals('GBP', $exchangeRates[1]->target_currency);
        $this->assertEquals(0.75, $exchangeRates[1]->rate);

        $this->assertEquals('EUR', $exchangeRates[2]->base_currency);
        $this->assertEquals('GBP', $exchangeRates[2]->target_currency);
        $this->assertEquals(0.88, $exchangeRates[2]->rate);
    }

    #[Test]
    public function it_calculates_inverse_rates_correctly()
    {
        // Test that if EUR to USD exists, USD to EUR is calculated as inverse
        $eurUsdRate = ExchangeRate::factory()
            ->forCurrencyPair('EUR', 'USD')
            ->withRate(1.20)
            ->create();

        // Create USD to EUR rate (should be calculated as inverse)
        $usdEurRate = ExchangeRate::factory()
            ->forCurrencyPair('USD', 'EUR')
            ->create();

        // The factory should generate a realistic rate, not necessarily the exact inverse
        // But it should be in the reasonable range
        $this->assertGreaterThan(0.7, $usdEurRate->rate);
        $this->assertLessThan(1.0, $usdEurRate->rate);
    }

    #[Test]
    public function it_handles_unknown_currency_pairs()
    {
        // Create currencies that might not have predefined rates
        Currency::factory()->create(['code' => 'XYZ']);
        Currency::factory()->create(['code' => 'ABC']);

        $exchangeRate = ExchangeRate::factory()
            ->forCurrencyPair('XYZ', 'ABC')
            ->create();

        $this->assertEquals('XYZ', $exchangeRate->base_currency);
        $this->assertEquals('ABC', $exchangeRate->target_currency);
        
        // Should generate a random rate within reasonable bounds
        $this->assertGreaterThan(0.1, $exchangeRate->rate);
        $this->assertLessThan(100, $exchangeRate->rate);
    }

    #[Test]
    public function it_creates_relationships_with_currency_models()
    {
        $exchangeRate = ExchangeRate::factory()->forCurrencyPair('USD', 'EUR')->create();

        // Test relationships
        $this->assertInstanceOf(Currency::class, $exchangeRate->baseCurrency);
        $this->assertInstanceOf(Currency::class, $exchangeRate->targetCurrency);
        $this->assertEquals('USD', $exchangeRate->baseCurrency->code);
        $this->assertEquals('EUR', $exchangeRate->targetCurrency->code);
    }

    #[Test]
    public function it_generates_fetched_at_within_reasonable_timeframe()
    {
        $exchangeRate = ExchangeRate::factory()->create();

        // Should be within the last week but not in the future
        $this->assertTrue($exchangeRate->fetched_at->lte(now()));
        $this->assertTrue($exchangeRate->fetched_at->gte(now()->subWeek()));
    }
}
