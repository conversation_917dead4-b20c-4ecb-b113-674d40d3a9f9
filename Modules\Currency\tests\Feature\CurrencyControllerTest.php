<?php

namespace Modules\Currency\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Currency\Models\Currency;
use PHPUnit\Framework\Attributes\Test;

class CurrencyControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Currency module
        $this->artisan('migrate', ['--path' => 'Modules/Currency/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_can_list_active_currencies()
    {
        Currency::factory()->create(['status' => 'active', 'code' => 'USD']);
        Currency::factory()->create(['status' => 'active', 'code' => 'EUR']);
        Currency::factory()->create(['status' => 'inactive', 'code' => 'GBP']);

        $response = $this->getJson('/api/v1/currencies');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'code',
                    'symbol',
                    'name',
                    'decimal_digits',
                    'decimal_separator',
                    'thousands_separator',
                ]
            ]
        ]);

        // Should only return active currencies
        $this->assertEquals(2, count($response->json('data')));
        
        // Verify only active currencies are returned
        foreach ($response->json('data') as $currency) {
            $this->assertContains($currency['code'], ['USD', 'EUR']);
        }
    }

    #[Test]
    public function it_returns_empty_array_when_no_active_currencies()
    {
        Currency::factory()->create(['status' => 'inactive']);
        Currency::factory()->create(['status' => 'inactive']);

        $response = $this->getJson('/api/v1/currencies');

        $response->assertStatus(200);
        $this->assertEquals(0, count($response->json('data')));
    }

    #[Test]
    public function it_can_get_currencies_dropdown()
    {
        Currency::factory()->create([
            'status' => 'active',
            'code' => 'USD',
            'name' => 'US Dollar',
            'symbol' => '$'
        ]);
        Currency::factory()->create([
            'status' => 'active',
            'code' => 'EUR',
            'name' => 'Euro',
            'symbol' => '€'
        ]);
        Currency::factory()->create([
            'status' => 'inactive',
            'code' => 'GBP',
            'name' => 'British Pound',
            'symbol' => '£'
        ]);

        // Debug: Check if currencies were created
        $allCurrencies = Currency::all();
        dump('All currencies in DB:', $allCurrencies->toArray());

        $activeCurrencies = Currency::where('status', 'active')->get();
        dump('Active currencies:', $activeCurrencies->toArray());

        $response = $this->getJson('/api/v1/currencies/dropdown');

        $response->assertStatus(200);

        // Debug actual response
        $responseData = $response->json();
        dump('Actual response:', $responseData);

        // Should only return active currencies
        if ($response->json('data') !== null) {
            $this->assertEquals(2, count($response->json('data')));
        }
    }

    #[Test]
    public function it_can_show_currency_by_code()
    {
        $currency = Currency::factory()->create([
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'status' => 'active',
        ]);

        $response = $this->getJson('/api/v1/currencies/USD');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'code',
                'symbol',
                'name',
                'decimal_digits',
                'decimal_separator',
                'thousands_separator',
            ]
        ]);

        $this->assertEquals('USD', $response->json('data.code'));
        $this->assertEquals('$', $response->json('data.symbol'));
        $this->assertEquals('US Dollar', $response->json('data.name'));
    }

    #[Test]
    public function it_handles_case_insensitive_currency_codes()
    {
        $currency = Currency::factory()->create([
            'code' => 'USD',
            'status' => 'active',
        ]);

        // Test lowercase
        $response = $this->getJson('/api/v1/currencies/usd');
        $response->assertStatus(200);
        $this->assertEquals('USD', $response->json('data.code'));

        // Test mixed case
        $response = $this->getJson('/api/v1/currencies/UsD');
        $response->assertStatus(200);
        $this->assertEquals('USD', $response->json('data.code'));
    }

    #[Test]
    public function it_returns_null_for_non_existent_currency()
    {
        $response = $this->getJson('/api/v1/currencies/XYZ');

        $response->assertStatus(200);
        $this->assertNull($response->json('data'));
    }

    #[Test]
    public function it_returns_null_for_inactive_currency()
    {
        Currency::factory()->create([
            'code' => 'GBP',
            'status' => 'inactive',
        ]);

        $response = $this->getJson('/api/v1/currencies/GBP');

        $response->assertStatus(200);
        $this->assertNull($response->json('data'));
    }

    #[Test]
    public function it_can_get_default_currency()
    {
        // Mock setting function
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_default') {
                    return 'USD';
                }
                return $default;
            };
        });

        $currency = Currency::factory()->create([
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'status' => 'active',
        ]);

        $response = $this->getJson('/api/v1/currencies/default');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'code',
                'symbol',
                'name',
                'decimal_digits',
                'decimal_separator',
                'thousands_separator',
            ]
        ]);

        $this->assertEquals('USD', $response->json('data.code'));
    }

    #[Test]
    public function it_returns_null_when_default_currency_not_found()
    {
        // Mock setting function to return non-existent currency
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_default') {
                    return 'XYZ';
                }
                return $default;
            };
        });

        $response = $this->getJson('/api/v1/currencies/default');

        $response->assertStatus(200);
        $this->assertNull($response->json('data'));
    }

    #[Test]
    public function it_returns_null_when_default_currency_is_inactive()
    {
        // Mock setting function
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_default') {
                    return 'USD';
                }
                return $default;
            };
        });

        Currency::factory()->create([
            'code' => 'USD',
            'status' => 'inactive',
        ]);

        $response = $this->getJson('/api/v1/currencies/default');

        $response->assertStatus(200);
        $this->assertNull($response->json('data'));
    }

    #[Test]
    public function it_returns_currencies_ordered_by_sort_order()
    {
        Currency::factory()->create(['status' => 'active', 'sort_order' => 3, 'code' => 'GBP']);
        Currency::factory()->create(['status' => 'active', 'sort_order' => 1, 'code' => 'USD']);
        Currency::factory()->create(['status' => 'active', 'sort_order' => 2, 'code' => 'EUR']);

        $response = $this->getJson('/api/v1/currencies');

        $response->assertStatus(200);
        $currencies = $response->json('data');

        $this->assertEquals('USD', $currencies[0]['code']); // sort_order 1
        $this->assertEquals('EUR', $currencies[1]['code']); // sort_order 2
        $this->assertEquals('GBP', $currencies[2]['code']); // sort_order 3
    }

    #[Test]
    public function it_returns_currencies_ordered_by_name_when_sort_order_same()
    {
        Currency::factory()->create(['status' => 'active', 'sort_order' => 1, 'code' => 'USD', 'name' => 'US Dollar']);
        Currency::factory()->create(['status' => 'active', 'sort_order' => 1, 'code' => 'EUR', 'name' => 'Euro']);

        $response = $this->getJson('/api/v1/currencies');

        $response->assertStatus(200);
        $currencies = $response->json('data');

        $this->assertEquals('Euro', $currencies[0]['name']); // Alphabetically first
        $this->assertEquals('US Dollar', $currencies[1]['name']); // Alphabetically second
    }

    #[Test]
    public function it_returns_consistent_response_structure()
    {
        Currency::factory()->create([
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'status' => 'active',
        ]);

        $response = $this->getJson('/api/v1/currencies');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'code',
                    'symbol',
                    'name',
                    'decimal_digits',
                    'decimal_separator',
                    'thousands_separator',
                ]
            ]
        ]);

        $this->assertTrue($response->json('success'));
        $this->assertIsString($response->json('message'));
    }

    #[Test]
    public function it_excludes_internal_fields_from_public_response()
    {
        Currency::factory()->create(['status' => 'active']);

        $response = $this->getJson('/api/v1/currencies');

        $response->assertStatus(200);
        
        foreach ($response->json('data') as $currency) {
            // These fields should not be exposed in public API
            $this->assertArrayNotHasKey('id', $currency);
            $this->assertArrayNotHasKey('status', $currency);
            $this->assertArrayNotHasKey('created_at', $currency);
            $this->assertArrayNotHasKey('updated_at', $currency);
        }
    }

    #[Test]
    public function it_handles_empty_database()
    {
        $response = $this->getJson('/api/v1/currencies');

        $response->assertStatus(200);
        $this->assertEquals(0, count($response->json('data')));
    }

    #[Test]
    public function it_handles_large_number_of_currencies()
    {
        Currency::factory()->count(50)->create(['status' => 'active']);

        $response = $this->getJson('/api/v1/currencies');

        $response->assertStatus(200);
        $this->assertEquals(50, count($response->json('data')));
    }

    #[Test]
    public function it_returns_proper_json_content_type()
    {
        Currency::factory()->create(['status' => 'active']);

        $response = $this->getJson('/api/v1/currencies');

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/json');
    }

    #[Test]
    public function it_handles_special_characters_in_currency_data()
    {
        Currency::factory()->create([
            'code' => 'EUR',
            'symbol' => '€',
            'name' => 'Euro',
            'status' => 'active',
        ]);

        $response = $this->getJson('/api/v1/currencies/EUR');

        $response->assertStatus(200);
        $this->assertEquals('€', $response->json('data.symbol'));
        $this->assertEquals('Euro', $response->json('data.name'));
    }

    #[Test]
    public function it_validates_currency_code_format_in_url()
    {
        // Test with invalid currency code format
        $response = $this->getJson('/api/v1/currencies/INVALID_CODE');

        $response->assertStatus(200);
        $this->assertNull($response->json('data'));
    }

    #[Test]
    public function it_handles_concurrent_requests()
    {
        Currency::factory()->create(['status' => 'active', 'code' => 'USD']);

        // Simulate concurrent requests
        $responses = [];
        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->getJson('/api/v1/currencies/USD');
        }

        foreach ($responses as $response) {
            $response->assertStatus(200);
            $this->assertEquals('USD', $response->json('data.code'));
        }
    }
}
