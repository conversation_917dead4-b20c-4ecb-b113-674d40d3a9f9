<?php

namespace Modules\Language\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static \Illuminate\Support\Collection getActiveLanguages()
 * @method static \Modules\Language\Models\Language|null getDefaultLanguage()
 * @method static \Modules\Language\Models\Language|null findByCode(string $code)
 *
 * @see \Modules\Language\Services\LanguageService
 */
class LanguageFacade extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return 'language.service'; // This is the key we'll bind in the service provider
    }
}
