<?php

namespace Modules\ModelAI\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Modules\ModelAI\Database\Factories\ModelToolFactory;

class ModelTool extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'model_tools';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'key',
        'name',
        'description',
        'type',
        'provider',
        'version',
        'api_endpoint',
        'configuration',
        'input_schema',
        'output_schema',
        'is_enabled',
        'is_public',
        'sort_order',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'configuration' => 'array',
        'input_schema' => 'array',
        'output_schema' => 'array',
        'is_enabled' => 'boolean',
        'is_public' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = ['deleted_at'];

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope a query to only include active tools.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive tools.
     */
    public function scopeInactive(Builder $query): Builder
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Scope a query to only include enabled tools.
     */
    public function scopeEnabled(Builder $query): Builder
    {
        return $query->where('is_enabled', true);
    }

    /**
     * Scope a query to only include disabled tools.
     */
    public function scopeDisabled(Builder $query): Builder
    {
        return $query->where('is_enabled', false);
    }

    /**
     * Scope a query to only include public tools.
     */
    public function scopePublic(Builder $query): Builder
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope a query to only include private tools.
     */
    public function scopePrivate(Builder $query): Builder
    {
        return $query->where('is_public', false);
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by type.
     */
    public function scopeType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to filter by provider.
     */
    public function scopeProvider(Builder $query, string $provider): Builder
    {
        return $query->where('provider', $provider);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the AI models that can use this tool.
     */
    public function modelAIs(): BelongsToMany
    {
        return $this->belongsToMany(ModelAI::class, 'model_ai_tools', 'model_tool_id', 'model_ai_id')
                    ->withPivot(['is_enabled', 'configuration', 'priority', 'max_usage_per_request', 'rate_limit_per_minute'])
                    ->withTimestamps();
    }

    /**
     * Get the categories that belong to this tool.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(ModelCategory::class, 'model_tool_categorizations', 'model_tool_id', 'model_category_id');
    }

    /**
     * Get the AI-tool configurations for this tool.
     */
    public function aiToolConfigs(): HasMany
    {
        return $this->hasMany(ModelAITool::class, 'model_tool_id');
    }

    /**
     * Check if tool is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if tool is enabled.
     */
    public function isEnabled(): bool
    {
        return $this->is_enabled;
    }

    /**
     * Check if tool is public.
     */
    public function isPublic(): bool
    {
        return $this->is_public;
    }

    /**
     * Check if tool is AI native.
     */
    public function isAINative(): bool
    {
        return $this->type === 'ai_native';
    }

    /**
     * Check if tool is custom.
     */
    public function isCustom(): bool
    {
        return $this->type === 'custom';
    }

    /**
     * Check if tool is model-based.
     */
    public function isModel(): bool
    {
        return $this->type === 'model';
    }

    /**
     * Get display name for the tool.
     */
    public function getDisplayName(): string
    {
        return $this->name;
    }

    /**
     * Get total model count that can use this tool.
     */
    public function getModelCount(): int
    {
        return $this->modelAIs()->count();
    }

    /**
     * Get active model count that can use this tool.
     */
    public function getActiveModelCount(): int
    {
        return $this->modelAIs()->where('status', 'active')->count();
    }

    /**
     * Check if tool can be used by a specific model.
     */
    public function canBeUsedByModel(int $modelId): bool
    {
        if ($this->is_public && $this->is_enabled && $this->isActive()) {
            return true;
        }

        return $this->modelAIs()
                    ->where('model_ai_id', $modelId)
                    ->wherePivot('is_enabled', true)
                    ->exists();
    }

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
        });
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): ModelToolFactory
    {
        return ModelToolFactory::new();
    }
}
