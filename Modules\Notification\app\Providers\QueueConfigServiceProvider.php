<?php

namespace Modules\Notification\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class QueueConfigServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only load queue config from database if not running in console during migration/seeding
        if ($this->shouldLoadQueueConfig()) {
            $this->loadQueueConfigFromDatabase();
        }
    }

    /**
     * Check if we should load queue config from database.
     */
    private function shouldLoadQueueConfig(): bool
    {
        // Don't load during migrations, seeding, or if database is not ready
        if ($this->app->runningInConsole()) {
            $command = $_SERVER['argv'][1] ?? '';

            // Skip for these commands
            $skipCommands = ['migrate', 'db:seed', 'config:cache', 'config:clear', 'optimize', 'module:migrate'];
            foreach ($skipCommands as $skipCommand) {
                if (str_contains($command, $skipCommand)) {
                    return false;
                }
            }
        }

        try {
            // Test if database is accessible and settings table exists
            DB::connection()->getPdo();
            return Schema::hasTable('settings');
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Load queue configuration from database settings.
     */
    private function loadQueueConfigFromDatabase(): void
    {
        try {
            // Get queue settings from database
            $queueConnection = setting('notifications.queue.connection', 'database');
            $queueName = setting('notifications.queue.name', 'notifications');
            $retryAfter = setting_int('notifications.queue.retry_after', 90);
            $tries = setting_int('notifications.queue.tries', 3);

            // Set the default queue connection
            Config::set('queue.default', $queueConnection);

            // Ensure database connection exists in queue config
            if ($queueConnection === 'database') {
                Config::set('queue.connections.database', [
                    'driver' => 'database',
                    'table' => 'jobs',
                    'queue' => $queueName,
                    'retry_after' => $retryAfter,
                    'after_commit' => false,
                ]);
            }

            // Set other queue-related configs from settings
            Config::set('queue.failed.driver', 'database');
            Config::set('queue.failed.database', config('database.default'));
            Config::set('queue.failed.table', 'failed_jobs');

        } catch (\Exception $e) {
            // Log error but don't break the application
            logger()->warning('Failed to load queue config from database: ' . $e->getMessage());
        }
    }
}
