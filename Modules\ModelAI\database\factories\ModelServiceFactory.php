<?php

namespace Modules\ModelAI\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\ModelAI\Models\ModelService;
use Modules\ModelAI\Models\ModelAI;

/**
 * @extends Factory<ModelService>
 */
class ModelServiceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = ModelService::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $billingType = $this->faker->randomElement(['per_request', 'per_token', 'hybrid']);
        
        return [
            'model_ai_id' => ModelAI::factory(),
            'input_types' => $this->faker->randomElements(['text', 'image', 'pdf', 'audio', 'video'], $this->faker->numberBetween(1, 3)),
            'output_types' => $this->faker->randomElements(['text', 'image', 'audio'], $this->faker->numberBetween(1, 2)),
            'supported_sources' => [
                ['type' => 'upload'],
                ['type' => 'url'],
                ['type' => 'oauth', 'provider' => 'google_drive'],
            ],
            'cost_per_request' => $billingType === 'per_token' ? 0 : $this->faker->numberBetween(1, 10),
            'cost_per1k_tokens' => $billingType === 'per_request' ? 0 : $this->faker->randomFloat(4, 0.001, 0.1),
            'billing_type' => $billingType,
            'cost_per1k_input' => $this->faker->randomFloat(6, 0.0001, 0.01),
            'cost_per1k_output' => $this->faker->randomFloat(6, 0.0001, 0.03),
            'max_tokens' => $this->faker->randomElement([1024, 2048, 4096, 8192, 16384]),
            'context_window' => $this->faker->randomElement([4096, 8192, 16384, 32768, 128000]),
            'rate_limit_rpm' => $this->faker->numberBetween(10, 1000),
            'timeout_seconds' => $this->faker->numberBetween(30, 300),
            'default_parameters' => [
                'temperature' => $this->faker->randomFloat(2, 0, 2),
                'top_p' => $this->faker->randomFloat(2, 0, 1),
                'max_tokens' => $this->faker->numberBetween(100, 2000),
            ],
            'allowed_parameters' => ['temperature', 'top_p', 'max_tokens', 'frequency_penalty', 'presence_penalty'],
            'priority' => $this->faker->numberBetween(0, 10),
            'notes' => $this->faker->optional()->sentence(20),
            'status' => $this->faker->randomElement(['active', 'inactive', 'draft']),
        ];
    }

    /**
     * Indicate that the service is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the service is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the service is draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
        ]);
    }

    /**
     * Configure for per-request billing.
     */
    public function perRequest(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'per_request',
            'cost_per_request' => $this->faker->numberBetween(1, 10),
            'cost_per1k_tokens' => 0,
        ]);
    }

    /**
     * Configure for per-token billing.
     */
    public function perToken(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'per_token',
            'cost_per_request' => 0,
            'cost_per1k_tokens' => $this->faker->randomFloat(4, 0.001, 0.1),
        ]);
    }

    /**
     * Configure for hybrid billing.
     */
    public function hybrid(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'hybrid',
            'cost_per_request' => $this->faker->numberBetween(1, 5),
            'cost_per1k_tokens' => $this->faker->randomFloat(4, 0.001, 0.05),
        ]);
    }

    /**
     * Configure with high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => $this->faker->numberBetween(8, 10),
        ]);
    }

    /**
     * Configure with text input/output only.
     */
    public function textOnly(): static
    {
        return $this->state(fn (array $attributes) => [
            'input_types' => ['text'],
            'output_types' => ['text'],
        ]);
    }

    /**
     * Configure with multimodal support.
     */
    public function multimodal(): static
    {
        return $this->state(fn (array $attributes) => [
            'input_types' => ['text', 'image', 'audio'],
            'output_types' => ['text', 'image'],
        ]);
    }
}
