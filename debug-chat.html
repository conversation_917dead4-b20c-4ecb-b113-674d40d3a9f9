<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Chat Widget</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f2f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
        }
        .debug-panel {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 600px;
            overflow-y: auto;
        }
        .debug-panel h3 {
            color: #ffffff;
            margin-top: 0;
        }
        .widget-container {
            width: 400px;
            height: 600px;
            border: 2px solid #e74c3c;
            border-radius: 8px;
            background: white;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #2563eb;
        }
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .test-messages {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-message {
            background: #f3f4f6;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .test-message:hover {
            background: #e5e7eb;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-entry.error {
            color: #ff6b6b;
        }
        .log-entry.warn {
            color: #ffd93d;
        }
        .log-entry.info {
            color: #74c0fc;
        }
    </style>
</head>
<body>
    <h1>🔍 Debug Chat Widget</h1>
    <p>Debug version với extensive logging để tìm và sửa lỗi chat.</p>

    <div class="test-messages">
        <h3>🧪 Test Messages (Click để gửi)</h3>
        <div class="test-message" onclick="sendTestMessage('Xin chào')">Xin chào</div>
        <div class="test-message" onclick="sendTestMessage('Thử coi')">Thử coi</div>
        <div class="test-message" onclick="sendTestMessage('Giúp tôi')">Giúp tôi</div>
        <div class="test-message" onclick="sendTestMessage('Sản phẩm')">Sản phẩm</div>
        <div class="test-message" onclick="sendTestMessage('Giá cả')">Giá cả</div>
        <div class="test-message" onclick="sendTestMessage('Hello')">Hello</div>
        <div class="test-message" onclick="sendTestMessage('Help me')">Help me</div>
    </div>

    <div class="controls">
        <button class="btn" onclick="loadWidget()">Load Widget</button>
        <button class="btn" onclick="clearWidget()">Clear Widget</button>
        <button class="btn" onclick="clearDebugLog()">Clear Debug Log</button>
        <button class="btn" onclick="testSendMessage()">Test Send Function</button>
    </div>

    <div class="container">
        <div class="debug-panel">
            <h3>🔍 Debug Console</h3>
            <div id="debug-log"></div>
        </div>
        
        <div id="widget-container" class="widget-container"></div>
    </div>

    <script src="dist/widget/procms-chatbot.umd.js"></script>
    <script>
        let widget = null;
        const debugLog = document.getElementById('debug-log');

        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            originalLog.apply(console, args);
            addDebugEntry('info', args.join(' '));
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addDebugEntry('error', args.join(' '));
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addDebugEntry('warn', args.join(' '));
        };

        function addDebugEntry(type, message) {
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            debugLog.appendChild(entry);
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        async function loadWidget() {
            try {
                addDebugEntry('info', '=== LOADING WIDGET ===');
                
                if (widget) {
                    addDebugEntry('info', 'Unmounting existing widget...');
                    widget.unmount();
                }

                addDebugEntry('info', 'Creating new widget...');
                widget = await ProcmsChatbot.create({
                    botUuid: 'debug-bot-uuid-123',
                    apiKey: 'pk_debug_api_key_12345678901234567890',
                    theme: 'light',
                    onReady: () => {
                        addDebugEntry('info', '✅ Widget ready callback fired');
                    },
                    onMessage: (message) => {
                        addDebugEntry('info', 'Message event: ' + JSON.stringify(message));
                    },
                    onError: (error) => {
                        addDebugEntry('error', 'Widget error: ' + error.message);
                    }
                }, '#widget-container');

                addDebugEntry('info', '✅ Widget loaded successfully');

            } catch (error) {
                addDebugEntry('error', 'Failed to load widget: ' + error.message);
                console.error('Widget load error:', error);
            }
        }

        function clearWidget() {
            if (widget) {
                addDebugEntry('info', 'Clearing widget...');
                widget.unmount();
                widget = null;
                addDebugEntry('info', 'Widget cleared');
            } else {
                addDebugEntry('warn', 'No widget to clear');
            }
        }

        function clearDebugLog() {
            debugLog.innerHTML = '';
            addDebugEntry('info', 'Debug log cleared');
        }

        function sendTestMessage(message) {
            if (!widget || !widget.isMounted()) {
                addDebugEntry('error', 'Cannot send message - widget not loaded');
                return;
            }

            addDebugEntry('info', `Attempting to send test message: "${message}"`);
            
            // Try to find the input field and send button
            const container = document.getElementById('widget-container');
            const inputField = container.querySelector('.procms-input');
            const sendButton = container.querySelector('.procms-send-btn');

            if (!inputField || !sendButton) {
                addDebugEntry('error', 'Cannot find input field or send button');
                return;
            }

            // Set the message and trigger send
            inputField.value = message;
            inputField.dispatchEvent(new Event('input')); // Trigger input event
            sendButton.click();
            
            addDebugEntry('info', 'Test message sent via UI simulation');
        }

        function testSendMessage() {
            if (!widget || !widget.isMounted()) {
                addDebugEntry('error', 'Widget not loaded for testing');
                return;
            }

            addDebugEntry('info', 'Testing sendMessage function directly...');
            
            // Try to access the chat widget's sendMessage method
            if (widget.chatWidget && widget.chatWidget.sendMessage) {
                // Simulate typing in the input field
                const container = document.getElementById('widget-container');
                const inputField = container.querySelector('.procms-input');
                
                if (inputField) {
                    inputField.value = 'Test message from debug function';
                    widget.chatWidget.sendMessage();
                    addDebugEntry('info', 'Direct sendMessage call completed');
                } else {
                    addDebugEntry('error', 'Cannot find input field for direct test');
                }
            } else {
                addDebugEntry('error', 'Cannot access sendMessage method');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            addDebugEntry('info', '🚀 Debug page loaded');
            addDebugEntry('info', 'ProcmsChatbot available: ' + (typeof ProcmsChatbot !== 'undefined'));
            
            // Auto-load widget after 2 seconds
            setTimeout(() => {
                addDebugEntry('info', 'Auto-loading widget...');
                loadWidget();
            }, 2000);
        });

        // Global error handler
        window.addEventListener('error', (event) => {
            addDebugEntry('error', `Global error: ${event.error.message} at ${event.filename}:${event.lineno}`);
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            addDebugEntry('error', `Unhandled promise rejection: ${event.reason}`);
        });
    </script>
</body>
</html>
