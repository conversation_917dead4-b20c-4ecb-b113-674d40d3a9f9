<?php

namespace Modules\Notification\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

class DynamicNotificationEvent implements ShouldQueue
{
    use Dispatchable, SerializesModels;

    public string $eventKey;
    public array $payload;

    /**
     * Create a new event instance.
     */
    public function __construct(string $eventKey, array $payload) {
        $this->eventKey = $eventKey;
        $this->payload = $payload;
    }

    /**
     * Get the channels the event should be broadcast on.
     */
    public function broadcastOn(): array
    {
        return [];
    }
}
