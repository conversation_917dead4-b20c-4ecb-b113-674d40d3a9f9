<?php

namespace Modules\Language\Services;

use Illuminate\Support\Facades\Cache;
use Modules\Language\Models\Language; // Assuming your model is here
use Illuminate\Support\Collection;

class LanguageService
{
    /**
     * Retrieve a list of active languages for dropdowns.
     *
     * @return Collection
     */
    public function getActiveLanguages(): Collection
    {
        return Cache::remember(
            key: 'proCMS.languages.active.list',
            ttl: now()->addMinutes(5),
            callback: function () {
                return Language::query()
                    ->active()
                    ->orderBy('name')
                    ->select(['code', 'name', 'native_name', 'direction', 'is_default'])
                    ->get();
             }
        );
    }

    /**
     * Get the default language.
     *
     * @return Language|null
     */
    public function getDefaultLanguage(): ?Language
    {
        return Language::where('is_default', 1)->active()->first();
    }

    /**
     * Find a language by its code.
     *
     * @param string $code
     * @return Language|null
     */
    public function findByCode(string $code): ?Language
    {
        return Language::where('code', $code)->first();
    }
}
