<?php

namespace Modules\ModelAI\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\ModelAI\Models\ModelAI;

/**
 * @extends Factory<ModelAI>
 */
class ModelAIFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = ModelAI::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $providers = [
            'openai' => [
                'models' => ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
                'endpoint' => 'https://api.openai.com/v1/chat/completions'
            ],
            'anthropic' => [
                'models' => ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
                'endpoint' => 'https://api.anthropic.com/v1/messages'
            ],
            'google' => [
                'models' => ['gemini-pro', 'gemini-pro-vision', 'palm-2'],
                'endpoint' => 'https://generativelanguage.googleapis.com/v1/models'
            ],
            'cohere' => [
                'models' => ['command', 'command-light', 'command-nightly'],
                'endpoint' => 'https://api.cohere.ai/v1/generate'
            ],
        ];

        $provider = $this->faker->randomElement(array_keys($providers));
        $modelKey = $this->faker->randomElement($providers[$provider]['models']);

        return [
            'key' => $modelKey . '-' . $this->faker->unique()->numberBetween(1000, 9999),
            'name' => ucfirst(str_replace('-', ' ', $modelKey)),
            'description' => $this->faker->sentence(15),
            'model_provider_id' => 1, // Default to first provider
            'version' => $this->faker->randomElement(['v1', 'v2', 'beta', 'stable']),
            'api_endpoint' => $providers[$provider]['endpoint'],
            'streaming' => $this->faker->boolean(80),
            'vision' => $this->faker->boolean(30),
            'function_calling' => $this->faker->boolean(60),
            'is_default' => false,
            'sort_order' => $this->faker->numberBetween(1, 100),
            'status' => $this->faker->randomElement(['active', 'inactive', 'draft']),
        ];
    }

    /**
     * Indicate that the model is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the model is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the model is draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
        ]);
    }

    /**
     * Indicate that the model is default.
     */
    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the model supports streaming.
     */
    public function withStreaming(): static
    {
        return $this->state(fn (array $attributes) => [
            'streaming' => true,
        ]);
    }

    /**
     * Indicate that the model supports vision.
     */
    public function withVision(): static
    {
        return $this->state(fn (array $attributes) => [
            'vision' => true,
        ]);
    }

    /**
     * Indicate that the model supports function calling.
     */
    public function withFunctionCalling(): static
    {
        return $this->state(fn (array $attributes) => [
            'function_calling' => true,
        ]);
    }

    /**
     * Configure for OpenAI provider.
     */
    public function openai(): static
    {
        return $this->state(fn (array $attributes) => [
            'model_provider_id' => 1, // Assuming OpenAI is first provider
            'api_endpoint' => 'https://api.openai.com/v1/chat/completions',
            'key' => 'gpt-' . $this->faker->randomElement(['4', '4-turbo', '3.5-turbo']),
        ]);
    }

    /**
     * Configure for Anthropic provider.
     */
    public function anthropic(): static
    {
        return $this->state(fn (array $attributes) => [
            'model_provider_id' => 2, // Assuming Anthropic is second provider
            'api_endpoint' => 'https://api.anthropic.com/v1/messages',
            'key' => 'claude-3-' . $this->faker->randomElement(['opus', 'sonnet', 'haiku']),
        ]);
    }

    /**
     * Configure for Google provider.
     */
    public function google(): static
    {
        return $this->state(fn (array $attributes) => [
            'model_provider_id' => 3, // Assuming Google is third provider
            'api_endpoint' => 'https://generativelanguage.googleapis.com/v1/models',
            'key' => $this->faker->randomElement(['gemini-pro', 'gemini-pro-vision', 'palm-2']),
        ]);
    }

    /**
     * Indicate that the model supports streaming.
     */
    public function streaming(): static
    {
        return $this->state(fn (array $attributes) => [
            'streaming' => true,
        ]);
    }

    /**
     * Indicate that the model supports vision.
     */
    public function vision(): static
    {
        return $this->state(fn (array $attributes) => [
            'vision' => true,
        ]);
    }

    /**
     * Indicate that the model supports function calling.
     */
    public function functionCalling(): static
    {
        return $this->state(fn (array $attributes) => [
            'function_calling' => true,
        ]);
    }
}
