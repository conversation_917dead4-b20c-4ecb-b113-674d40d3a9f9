<?php

namespace Modules\Notification\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Language\Models\Language;
use Modules\Notification\Models\NotificationType;

/**
 * @property mixed $id
 * @property mixed $notification_template_id
 */
class NotificationTemplateRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'notification_type_id' => [
                'required',
                'integer',
                'exists:notification_types,id',
            ],
            'channel' => [
                'required',
                'string',
                Rule::in(['database', 'email', 'sms', 'push', 'telegram']),
            ],
            'locale' => [
                'required',
                'string',
                'max:10',
                'exists:languages,code',
                function ($attribute, $value, $fail) {
                    $language = Language::where('code', $value)->first();
                    if ($language && $language->status !== 'active') {
                        $fail("The language '{$value}' is not active.");
                    }
                },
            ],
            'subject' => [
                'nullable',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    // Subject is required for email channel
                    if ($this->channel === 'email' && empty($value)) {
                        $fail('The subject field is required for email notifications.');
                    }
                    // Subject should be null for non-email channels
                    if ($this->channel !== 'email' && !empty($value)) {
                        $fail('The subject field is only applicable for email notifications.');
                    }
                },
            ],
            'title' => [
                'nullable',
                'string',
                'max:500',
            ],
            'content' => [
                'required',
                'string',
                'max:65535', // TEXT field limit
            ],
            'variables' => [
                'nullable',
                'array',
            ],
            'variables.*' => [
                'required',
                'string',
                'max:100',
                'alpha_dash',
            ],
            'settings' => [
                'nullable',
                'array',
            ],
            'settings.from_name' => [
                'nullable',
                'string',
                'max:255',
            ],
            'settings.reply_to' => [
                'nullable',
                'email',
                'max:255',
            ],
            'settings.priority' => [
                'nullable',
                'string',
                Rule::in(['low', 'normal', 'high']),
            ],
            'settings.sender_id' => [
                'nullable',
                'string',
                'max:100',
            ],
            'settings.icon' => [
                'nullable',
                'string',
                'max:255',
            ],
            'settings.sound' => [
                'nullable',
                'string',
                'max:100',
            ],
            'settings.badge' => [
                'nullable',
                'boolean',
            ],
            'settings.parse_mode' => [
                'nullable',
                'string',
                Rule::in(['HTML', 'Markdown']),
            ],
            'settings.disable_web_page_preview' => [
                'nullable',
                'boolean',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(['active', 'inactive']),
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'notification_type_id' => __('Notification Type'),
            'channel' => __('Channel'),
            'locale' => __('Language'),
            'subject' => __('Subject'),
            'title' => __('Title'),
            'content' => __('Content'),
            'variables' => __('Variables'),
            'variables.*' => __('Variable'),
            'settings' => __('Settings'),
            'settings.from_name' => __('From Name'),
            'settings.reply_to' => __('Reply To'),
            'settings.priority' => __('Priority'),
            'settings.sender_id' => __('Sender ID'),
            'settings.icon' => __('Icon'),
            'settings.sound' => __('Sound'),
            'settings.badge' => __('Badge'),
            'settings.parse_mode' => __('Parse Mode'),
            'settings.disable_web_page_preview' => __('Disable Web Page Preview'),
            'status' => __('Status'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'notification_type_id.exists' => 'The selected notification type does not exist.',
            'channel.in' => 'The selected channel is invalid.',
            'locale.exists' => 'The selected language does not exist.',
            'variables.*.alpha_dash' => 'Variables may only contain letters, numbers, dashes and underscores.',
            'content.max' => 'The content may not be greater than 65,535 characters.',
            'settings.priority.in' => 'Priority must be one of: low, normal, high.',
            'settings.parse_mode.in' => 'Parse mode must be either HTML or Markdown.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if template already exists for this type/channel/locale combination
            if ($this->isMethod('POST') || ($this->isMethod('PUT') && $this->hasChangedUniqueFields())) {
                $exists = \Modules\Notification\Models\NotificationTemplate::where([
                    'notification_type_id' => $this->notification_type_id,
                    'channel' => $this->channel,
                    'locale' => $this->locale,
                ])
                ->when($this->id, function ($query) {
                    return $query->where('id', '!=', $this->id);
                })
                ->exists();

                if ($exists) {
                    $validator->errors()->add(
                        'channel',
                        'A template for this notification type, channel, and language already exists.'
                    );
                }
            }

            // Validate that channel is supported by notification type
            if ($this->notification_type_id && $this->channel) {
                $notificationType = NotificationType::find($this->notification_type_id);
                if ($notificationType && !$notificationType->supportsChannel($this->channel)) {
                    $validator->errors()->add(
                        'channel',
                        'The selected channel is not supported by this notification type.'
                    );
                }
            }

            // Validate variables format
            if ($this->has('variables') && is_array($this->variables)) {
                $variables = $this->variables;
                if (count($variables) !== count(array_unique($variables))) {
                    $validator->errors()->add('variables', 'Duplicate variables are not allowed.');
                }
            }

            // Validate content contains referenced variables
            if ($this->content && $this->variables) {
                $content = $this->content;
                $variables = is_array($this->variables) ? $this->variables : [];
                
                foreach ($variables as $variable) {
                    if (!str_contains($content, '{' . $variable . '}')) {
                        $validator->errors()->add(
                            'content',
                            "The content should reference the variable: {$variable}"
                        );
                    }
                }
            }

            // Channel-specific validation
            $this->validateChannelSpecificSettings($validator);
        });
    }

    /**
     * Validate channel-specific settings.
     */
    protected function validateChannelSpecificSettings($validator): void
    {
        if (!$this->channel || !$this->settings) {
            return;
        }

        $settings = $this->settings;
        $channel = $this->channel;

        switch ($channel) {
            case 'email':
                $allowedKeys = ['from_name', 'reply_to', 'priority'];
                break;
            case 'sms':
                $allowedKeys = ['sender_id'];
                break;
            case 'push':
                $allowedKeys = ['icon', 'sound', 'badge'];
                break;
            case 'telegram':
                $allowedKeys = ['parse_mode', 'disable_web_page_preview'];
                break;
            default:
                $allowedKeys = [];
        }

        $unknownKeys = array_diff(array_keys($settings), $allowedKeys);
        if (!empty($unknownKeys)) {
            $validator->errors()->add(
                'settings',
                "Invalid settings for {$channel} channel: " . implode(', ', $unknownKeys)
            );
        }
    }

    /**
     * Check if unique fields have changed.
     */
    protected function hasChangedUniqueFields(): bool
    {
        if (!$this->route('notification_template')) {
            return true;
        }

        $template = $this->route('notification_template');
        return $template->notification_type_id != $this->notification_type_id ||
               $template->channel != $this->channel ||
               $template->locale != $this->locale;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure variables is an array
        if ($this->has('variables') && !is_array($this->variables)) {
            $variables = is_string($this->variables) ? explode(',', $this->variables) : [];
            $this->merge([
                'variables' => array_filter(array_map('trim', $variables))
            ]);
        }

        // Convert string booleans to actual booleans in settings
        if ($this->has('settings') && is_array($this->settings)) {
            $settings = $this->settings;
            
            foreach (['badge', 'disable_web_page_preview'] as $boolField) {
                if (isset($settings[$boolField])) {
                    $settings[$boolField] = filter_var(
                        $settings[$boolField], 
                        FILTER_VALIDATE_BOOLEAN, 
                        FILTER_NULL_ON_FAILURE
                    ) ?? false;
                }
            }
            
            $this->merge(['settings' => $settings]);
        }
    }
}
