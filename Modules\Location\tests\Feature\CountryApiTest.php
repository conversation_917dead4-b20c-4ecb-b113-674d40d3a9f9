<?php

namespace Modules\Location\Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Modules\Location\Models\Country;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use PHPUnit\Framework\Attributes\Test;

class CountryApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations for Location module
        $this->artisan('migrate', ['--path' => 'Modules/Location/database/migrations']);
        
        // Create and authenticate user
        $this->user = User::factory()->create();
        Sanctum::actingAs($this->user);
    }

    #[Test]
    public function it_requires_authentication_for_admin_endpoints()
    {
        Sanctum::actingAs(null);

        $response = $this->getJson('/api/v1/countries');

        $response->assertStatus(401);
    }

    #[Test]
    public function it_can_list_countries_with_pagination()
    {
        Country::factory()->count(15)->create();

        $response = $this->getJson('/api/v1/countries?limit=10');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'native_name',
                            'iso_code_2',
                            'iso_code_3',
                            'status'
                        ]
                    ],
                    'current_page',
                    'last_page',
                    'per_page',
                    'total'
                ]
            ]);

        $this->assertCount(10, $response->json('data.data'));
        $this->assertEquals(15, $response->json('data.total'));
    }

    #[Test]
    public function it_can_create_a_country()
    {
        $countryData = [
            'name' => 'Test Country',
            'native_name' => 'Test Native',
            'iso_code_2' => 'TC',
            'iso_code_3' => 'TST',
            'iso_numeric' => '999',
            'phone_code' => '+999',
            'region' => 'Test Region',
            'subregion' => 'Test Subregion',
            'latitude' => 45.0,
            'longitude' => 90.0,
            'emoji' => '🏳️',
            'emoji_unicode' => 'U+1F3F3',
            'status' => 'active'
        ];

        $response = $this->postJson('/api/v1/countries', $countryData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'iso_code_2',
                    'status'
                ]
            ]);

        $this->assertDatabaseHas('countries', [
            'name' => 'Test Country',
            'iso_code_2' => 'TC'
        ]);
    }

    #[Test]
    public function it_validates_required_fields_when_creating_country()
    {
        $response = $this->postJson('/api/v1/countries', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'iso_code_2', 'status']);
    }

    #[Test]
    public function it_validates_unique_iso_codes_when_creating_country()
    {
        Country::factory()->create(['iso_code_2' => 'US']);

        $response = $this->postJson('/api/v1/countries', [
            'name' => 'Test Country',
            'iso_code_2' => 'US',
            'status' => 'active'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['iso_code_2']);
    }

    #[Test]
    public function it_can_show_a_country()
    {
        $country = Country::factory()->create();

        $response = $this->getJson("/api/v1/countries/{$country->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'native_name',
                    'iso_code_2',
                    'iso_code_3',
                    'status'
                ]
            ]);

        $this->assertEquals($country->id, $response->json('data.id'));
    }

    #[Test]
    public function it_returns_404_for_non_existent_country()
    {
        $response = $this->getJson('/api/v1/countries/999');

        $response->assertStatus(404);
    }

    #[Test]
    public function it_can_update_a_country()
    {
        $country = Country::factory()->create();

        $updateData = [
            'name' => 'Updated Country Name',
            'native_name' => 'Updated Native Name',
            'status' => 'inactive'
        ];

        $response = $this->putJson("/api/v1/countries/{$country->id}", $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'status'
                ]
            ]);

        $this->assertDatabaseHas('countries', [
            'id' => $country->id,
            'name' => 'Updated Country Name',
            'status' => 'inactive'
        ]);
    }

    #[Test]
    public function it_validates_unique_iso_codes_when_updating_country()
    {
        $country1 = Country::factory()->create(['iso_code_2' => 'US']);
        $country2 = Country::factory()->create(['iso_code_2' => 'CA']);

        $response = $this->putJson("/api/v1/countries/{$country2->id}", [
            'name' => 'Updated Name',
            'iso_code_2' => 'US',
            'status' => 'active'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['iso_code_2']);
    }

    #[Test]
    public function it_can_soft_delete_a_country()
    {
        $country = Country::factory()->create();

        $response = $this->deleteJson("/api/v1/countries/{$country->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => $country->id
            ]);

        $this->assertSoftDeleted('countries', ['id' => $country->id]);
    }

    #[Test]
    public function it_can_restore_a_soft_deleted_country()
    {
        $country = Country::factory()->create();
        $country->delete();

        $response = $this->patchJson("/api/v1/countries/{$country->id}/restore");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'deleted_at'
                ]
            ]);

        $this->assertDatabaseHas('countries', [
            'id' => $country->id,
            'deleted_at' => null
        ]);
    }

    #[Test]
    public function it_can_force_delete_a_country()
    {
        $country = Country::factory()->create();
        $country->delete();

        $response = $this->deleteJson("/api/v1/countries/{$country->id}/force-delete");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => $country->id
            ]);

        $this->assertDatabaseMissing('countries', ['id' => $country->id]);
    }

    #[Test]
    public function it_can_list_trashed_countries()
    {
        $activeCountry = Country::factory()->create();
        $trashedCountry = Country::factory()->create();
        $trashedCountry->delete();

        $response = $this->getJson('/api/v1/countries/trashed/list');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'deleted_at'
                        ]
                    ]
                ]
            ]);

        $this->assertCount(1, $response->json('data.data'));
        $this->assertEquals($trashedCountry->id, $response->json('data.data.0.id'));
    }

    #[Test]
    public function it_can_bulk_delete_countries()
    {
        $countries = Country::factory()->count(3)->create();
        $ids = $countries->pluck('id')->toArray();

        $response = $this->postJson('/api/v1/countries/bulk-delete', [
            'ids' => $ids
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'deleted_count'
                ]
            ]);

        $this->assertEquals(3, $response->json('data.deleted_count'));
        
        foreach ($ids as $id) {
            $this->assertSoftDeleted('countries', ['id' => $id]);
        }
    }

    #[Test]
    public function it_can_bulk_restore_countries()
    {
        $countries = Country::factory()->count(3)->create();
        $countries->each->delete();
        $ids = $countries->pluck('id')->toArray();

        $response = $this->postJson('/api/v1/countries/bulk-restore', [
            'ids' => $ids
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'restored_count'
                ]
            ]);

        $this->assertEquals(3, $response->json('data.restored_count'));
        
        foreach ($ids as $id) {
            $this->assertDatabaseHas('countries', [
                'id' => $id,
                'deleted_at' => null
            ]);
        }
    }

    #[Test]
    public function it_can_bulk_force_delete_countries()
    {
        $countries = Country::factory()->count(3)->create();
        $countries->each->delete();
        $ids = $countries->pluck('id')->toArray();

        $response = $this->postJson('/api/v1/countries/bulk-force-delete', [
            'ids' => $ids
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'deleted_count'
                ]
            ]);

        $this->assertEquals(3, $response->json('data.deleted_count'));
        
        foreach ($ids as $id) {
            $this->assertDatabaseMissing('countries', ['id' => $id]);
        }
    }

    #[Test]
    public function it_validates_bulk_operation_ids()
    {
        $response = $this->postJson('/api/v1/countries/bulk-delete', [
            'ids' => []
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids']);
    }

    #[Test]
    public function it_validates_bulk_operation_existing_ids()
    {
        $response = $this->postJson('/api/v1/countries/bulk-delete', [
            'ids' => [999, 998]
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids.0', 'ids.1']);
    }

    #[Test]
    public function it_can_get_countries_dropdown()
    {
        Country::factory()->count(3)->active()->create();
        Country::factory()->inactive()->create();

        $response = $this->getJson('/api/v1/countries/dropdown/list');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'native_name',
                        'iso_code_2'
                    ]
                ]
            ]);

        $this->assertCount(3, $response->json('data'));
    }

    #[Test]
    public function it_can_filter_countries_by_name()
    {
        Country::factory()->create(['name' => 'United States']);
        Country::factory()->create(['name' => 'Canada']);

        $response = $this->getJson('/api/v1/countries?name=United');

        $response->assertStatus(200);
        
        $countries = $response->json('data.data');
        $this->assertCount(1, $countries);
        $this->assertEquals('United States', $countries[0]['name']);
    }

    #[Test]
    public function it_can_filter_countries_by_region()
    {
        Country::factory()->create(['region' => 'Asia']);
        Country::factory()->create(['region' => 'Europe']);

        $response = $this->getJson('/api/v1/countries?region=Asia');

        $response->assertStatus(200);
        
        $countries = $response->json('data.data');
        $this->assertCount(1, $countries);
        $this->assertEquals('Asia', $countries[0]['region']);
    }

    #[Test]
    public function it_can_filter_countries_by_status()
    {
        Country::factory()->active()->create();
        Country::factory()->inactive()->create();

        $response = $this->getJson('/api/v1/countries?status=active');

        $response->assertStatus(200);
        
        $countries = $response->json('data.data');
        $this->assertCount(1, $countries);
        $this->assertEquals('active', $countries[0]['status']);
    }

    #[Test]
    public function it_can_filter_countries_by_iso_code()
    {
        Country::factory()->create(['iso_code_2' => 'US']);
        Country::factory()->create(['iso_code_2' => 'CA']);

        $response = $this->getJson('/api/v1/countries?iso_code_2=US');

        $response->assertStatus(200);
        
        $countries = $response->json('data.data');
        $this->assertCount(1, $countries);
        $this->assertEquals('US', $countries[0]['iso_code_2']);
    }

    #[Test]
    public function it_can_sort_countries_by_name()
    {
        Country::factory()->create(['name' => 'Zebra Country']);
        Country::factory()->create(['name' => 'Alpha Country']);

        $response = $this->getJson('/api/v1/countries?sort=name&order=asc');

        $response->assertStatus(200);
        
        $countries = $response->json('data.data');
        $this->assertEquals('Alpha Country', $countries[0]['name']);
        $this->assertEquals('Zebra Country', $countries[1]['name']);
    }

    #[Test]
    public function it_returns_validation_errors_for_invalid_coordinates()
    {
        $response = $this->postJson('/api/v1/countries', [
            'name' => 'Test Country',
            'iso_code_2' => 'TC',
            'latitude' => 91, // Invalid latitude
            'longitude' => 181, // Invalid longitude
            'status' => 'active'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['latitude', 'longitude']);
    }

    #[Test]
    public function it_returns_validation_errors_for_invalid_phone_code()
    {
        $response = $this->postJson('/api/v1/countries', [
            'name' => 'Test Country',
            'iso_code_2' => 'TC',
            'phone_code' => 'invalid',
            'status' => 'active'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['phone_code']);
    }
}
