<?php

namespace Modules\Notification\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseFormRequest;

class BulkPreferenceUpdateRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'preferences' => [
                'required',
                'array',
                'min:1',
                'max:50', // Limit bulk updates
            ],
            'preferences.*.notification_type_id' => [
                'required',
                'integer',
                'exists:notification_types,id',
            ],
            'preferences.*.channel' => [
                'required',
                'string',
                Rule::in(['database', 'email', 'sms', 'push', 'telegram']),
            ],
            'preferences.*.enabled' => [
                'required',
                'boolean',
            ],
            'preferences.*.settings' => [
                'nullable',
                'array',
            ],
            'preferences.*.settings.frequency' => [
                'nullable',
                'string',
                Rule::in(['immediate', 'daily', 'weekly']),
            ],
            'preferences.*.settings.digest' => [
                'nullable',
                'boolean',
            ],
            'preferences.*.settings.sound_enabled' => [
                'nullable',
                'boolean',
            ],
            'preferences.*.settings.vibration_enabled' => [
                'nullable',
                'boolean',
            ],
            'preferences.*.settings.badge_enabled' => [
                'nullable',
                'boolean',
            ],
            'preferences.*.settings.show_preview' => [
                'nullable',
                'boolean',
            ],
            'preferences.*.settings.html_format' => [
                'nullable',
                'boolean',
            ],
            'preferences.*.settings.mark_as_read_after' => [
                'nullable',
                'integer',
                'min:1',
                'max:365',
            ],
            'preferences.*.settings.auto_delete_after' => [
                'nullable',
                'integer',
                'min:1',
                'max:3650',
            ],
            'preferences.*.settings.short_format' => [
                'nullable',
                'boolean',
            ],
            'preferences.*.settings.disable_notification' => [
                'nullable',
                'boolean',
            ],
            'preferences.*.settings.parse_mode' => [
                'nullable',
                'string',
                Rule::in(['HTML', 'Markdown']),
            ],
            'preferences.*.quiet_hours_start' => [
                'nullable',
                'date_format:H:i',
            ],
            'preferences.*.quiet_hours_end' => [
                'nullable',
                'date_format:H:i',
            ],
            'preferences.*.timezone' => [
                'nullable',
                'string',
                'max:50',
                'timezone',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'preferences' => __('Preferences'),
            'preferences.*.notification_type_id' => __('Notification Type'),
            'preferences.*.channel' => __('Channel'),
            'preferences.*.enabled' => __('Enabled'),
            'preferences.*.settings' => __('Settings'),
            'preferences.*.settings.frequency' => __('Frequency'),
            'preferences.*.settings.digest' => __('Digest Mode'),
            'preferences.*.settings.sound_enabled' => __('Sound Enabled'),
            'preferences.*.settings.vibration_enabled' => __('Vibration Enabled'),
            'preferences.*.settings.badge_enabled' => __('Badge Enabled'),
            'preferences.*.settings.show_preview' => __('Show Preview'),
            'preferences.*.settings.html_format' => __('HTML Format'),
            'preferences.*.settings.mark_as_read_after' => __('Mark as Read After (days)'),
            'preferences.*.settings.auto_delete_after' => __('Auto Delete After (days)'),
            'preferences.*.settings.short_format' => __('Short Format'),
            'preferences.*.settings.disable_notification' => __('Disable Notification'),
            'preferences.*.settings.parse_mode' => __('Parse Mode'),
            'preferences.*.quiet_hours_start' => __('Quiet Hours Start'),
            'preferences.*.quiet_hours_end' => __('Quiet Hours End'),
            'preferences.*.timezone' => __('Timezone'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'preferences.required' => 'At least one preference must be provided.',
            'preferences.array' => 'Preferences must be provided as an array.',
            'preferences.min' => 'At least one preference must be provided.',
            'preferences.max' => 'Cannot update more than 50 preferences at once.',
            'preferences.*.notification_type_id.exists' => 'One or more notification types do not exist.',
            'preferences.*.channel.in' => 'One or more channels are invalid.',
            'preferences.*.settings.frequency.in' => 'Frequency must be one of: immediate, daily, weekly.',
            'preferences.*.settings.parse_mode.in' => 'Parse mode must be either HTML or Markdown.',
            'preferences.*.quiet_hours_start.date_format' => 'Quiet hours start must be in HH:MM format.',
            'preferences.*.quiet_hours_end.date_format' => 'Quiet hours end must be in HH:MM format.',
            'preferences.*.timezone.timezone' => 'The timezone must be a valid timezone identifier.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $preferences = $this->preferences ?? [];
            
            // Validate unique combinations
            $this->validateUniquePreferences($validator, $preferences);
            
            // Validate channel support for each notification type
            $this->validateChannelSupport($validator, $preferences);
            
            // Validate quiet hours logic
            $this->validateQuietHours($validator, $preferences);
            
            // Validate channel-specific settings
            $this->validateChannelSpecificSettings($validator, $preferences);
        });
    }

    /**
     * Validate unique notification_type_id and channel combinations.
     */
    protected function validateUniquePreferences($validator, array $preferences): void
    {
        $combinations = [];
        
        foreach ($preferences as $index => $preference) {
            $key = ($preference['notification_type_id'] ?? '') . '_' . ($preference['channel'] ?? '');
            
            if (in_array($key, $combinations)) {
                $validator->errors()->add(
                    "preferences.{$index}.channel",
                    'Duplicate notification type and channel combination found.'
                );
            } else {
                $combinations[] = $key;
            }
        }
    }

    /**
     * Validate channel support for notification types.
     */
    protected function validateChannelSupport($validator, array $preferences): void
    {
        $notificationTypes = \Modules\Notification\Models\NotificationType::whereIn(
            'id',
            array_column($preferences, 'notification_type_id')
        )->get()->keyBy('id');

        foreach ($preferences as $index => $preference) {
            $typeId = $preference['notification_type_id'] ?? null;
            $channel = $preference['channel'] ?? null;
            
            if ($typeId && $channel && isset($notificationTypes[$typeId])) {
                $notificationType = $notificationTypes[$typeId];
                
                if (!$notificationType->supportsChannel($channel)) {
                    $validator->errors()->add(
                        "preferences.{$index}.channel",
                        "The channel '{$channel}' is not supported by notification type '{$notificationType->name}'."
                    );
                }
            }
        }
    }

    /**
     * Validate quiet hours logic.
     */
    protected function validateQuietHours($validator, array $preferences): void
    {
        foreach ($preferences as $index => $preference) {
            $start = $preference['quiet_hours_start'] ?? null;
            $end = $preference['quiet_hours_end'] ?? null;
            $timezone = $preference['timezone'] ?? null;
            
            // If quiet hours are set, validate they're not the same
            if ($start && $end && $start === $end) {
                $validator->errors()->add(
                    "preferences.{$index}.quiet_hours_end",
                    'Quiet hours start and end times cannot be the same.'
                );
            }
            
            // If quiet hours are set, timezone should be provided
            if (($start || $end) && !$timezone) {
                $validator->errors()->add(
                    "preferences.{$index}.timezone",
                    'Timezone is required when quiet hours are specified.'
                );
            }
        }
    }

    /**
     * Validate channel-specific settings.
     */
    protected function validateChannelSpecificSettings($validator, array $preferences): void
    {
        foreach ($preferences as $index => $preference) {
            $channel = $preference['channel'] ?? null;
            $settings = $preference['settings'] ?? [];
            
            if (!$channel || empty($settings)) {
                continue;
            }

            $allowedKeys = $this->getAllowedSettingsKeys($channel);
            $unknownKeys = array_diff(array_keys($settings), $allowedKeys);
            
            if (!empty($unknownKeys)) {
                $validator->errors()->add(
                    "preferences.{$index}.settings",
                    "Invalid settings for {$channel} channel: " . implode(', ', $unknownKeys)
                );
            }
        }
    }

    /**
     * Get allowed settings keys for a channel.
     */
    protected function getAllowedSettingsKeys(string $channel): array
    {
        $allowedKeys = [
            'email' => ['frequency', 'digest', 'html_format'],
            'database' => ['mark_as_read_after', 'auto_delete_after'],
            'sms' => ['frequency', 'short_format'],
            'push' => ['sound_enabled', 'vibration_enabled', 'badge_enabled', 'show_preview'],
            'telegram' => ['disable_notification', 'parse_mode'],
        ];

        return $allowedKeys[$channel] ?? [];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if (!$this->has('preferences') || !is_array($this->preferences)) {
            return;
        }

        $preferences = $this->preferences;
        
        foreach ($preferences as $index => $preference) {
            // Convert string booleans to actual booleans
            $booleanFields = ['enabled'];
            foreach ($booleanFields as $field) {
                if (isset($preference[$field])) {
                    $preferences[$index][$field] = filter_var(
                        $preference[$field], 
                        FILTER_VALIDATE_BOOLEAN, 
                        FILTER_NULL_ON_FAILURE
                    ) ?? false;
                }
            }

            // Convert string booleans in settings
            if (isset($preference['settings']) && is_array($preference['settings'])) {
                $settings = $preference['settings'];
                $settingsBooleanFields = [
                    'digest', 'sound_enabled', 'vibration_enabled', 'badge_enabled',
                    'show_preview', 'html_format', 'short_format', 'disable_notification'
                ];
                
                foreach ($settingsBooleanFields as $field) {
                    if (isset($settings[$field])) {
                        $settings[$field] = filter_var(
                            $settings[$field], 
                            FILTER_VALIDATE_BOOLEAN, 
                            FILTER_NULL_ON_FAILURE
                        ) ?? false;
                    }
                }
                
                $preferences[$index]['settings'] = $settings;
            }
        }
        
        $this->merge(['preferences' => $preferences]);
    }
}
