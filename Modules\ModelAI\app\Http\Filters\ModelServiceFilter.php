<?php

namespace Modules\ModelAI\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class ModelServiceFilter extends AbstractFilter
{
    protected function filters(): array
    {
        return [
            'model_ai_id' => 'exact',
            'billing_type' => 'exact',
            'status' => 'exact',
            'priority' => 'exact',
            'cost_per_request' => 'exact',
            'cost_per1k_tokens' => 'exact',
            'max_tokens' => 'exact',
            'context_window' => 'exact',
            'rate_limit_rpm' => 'exact',
            'timeout_seconds' => 'exact',
            'notes' => 'like',
            'is_trashed' => 'exact',
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
            'updated_from' => ['type' => 'from', 'column' => 'updated_at'],
            'updated_to' => ['type' => 'to', 'column' => 'updated_at'],
        ];
    }
}
