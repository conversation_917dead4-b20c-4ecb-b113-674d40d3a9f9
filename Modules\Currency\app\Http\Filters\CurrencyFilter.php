<?php

namespace Modules\Currency\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class CurrencyFilter extends AbstractFilter
{
    protected function filters(): array
    {
        return [
            'code' => 'like',
            'name' => 'like',
            'symbol' => 'like',
            'status' => 'exact',
            'decimal_digits' => 'exact',
            'sort_order' => 'exact',
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
        ];
    }
}
