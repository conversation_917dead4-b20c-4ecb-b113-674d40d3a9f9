<?php

namespace Modules\Currency\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Modules\Currency\Http\Requests\CurrencyRequest;
use Modules\Currency\Models\Currency;
use PHPUnit\Framework\Attributes\Test;

class CurrencyRequestTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Currency module
        $this->artisan('migrate', ['--path' => 'Modules/Currency/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_validates_required_fields()
    {
        $request = new CurrencyRequest();
        $rules = $request->rules();

        $validator = Validator::make([], $rules);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('code', $validator->errors()->toArray());
        $this->assertArrayHasKey('symbol', $validator->errors()->toArray());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
        $this->assertArrayHasKey('decimal_digits', $validator->errors()->toArray());
        $this->assertArrayHasKey('decimal_separator', $validator->errors()->toArray());
        $this->assertArrayHasKey('thousands_separator', $validator->errors()->toArray());
        $this->assertArrayHasKey('status', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_code_format()
    {
        $request = new CurrencyRequest();
        $rules = $request->rules();

        // Test valid code
        $validData = [
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ];
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        // Test invalid code - too short
        $invalidData = array_merge($validData, ['code' => 'US']);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('code', $validator->errors()->toArray());

        // Test invalid code - too long
        $invalidData = array_merge($validData, ['code' => 'USDD']);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('code', $validator->errors()->toArray());

        // Test invalid code - contains numbers
        $invalidData = array_merge($validData, ['code' => 'US1']);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('code', $validator->errors()->toArray());

        // Test invalid code - lowercase
        $invalidData = array_merge($validData, ['code' => 'usd']);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('code', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_code_uniqueness()
    {
        Currency::factory()->create(['code' => 'USD']);

        $request = new CurrencyRequest();
        $rules = $request->rules();

        $data = [
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ];

        $validator = Validator::make($data, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('code', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_symbol_format()
    {
        $request = new CurrencyRequest();
        $rules = $request->rules();

        $baseData = [
            'code' => 'USD',
            'name' => 'US Dollar',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ];

        // Test valid symbol
        $validData = array_merge($baseData, ['symbol' => '$']);
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        // Test symbol too long
        $invalidData = array_merge($baseData, ['symbol' => str_repeat('$', 11)]);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('symbol', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_name_format()
    {
        $request = new CurrencyRequest();
        $rules = $request->rules();

        $baseData = [
            'code' => 'USD',
            'symbol' => '$',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ];

        // Test valid name
        $validData = array_merge($baseData, ['name' => 'US Dollar']);
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        // Test name too short
        $invalidData = array_merge($baseData, ['name' => 'A']);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());

        // Test name too long
        $invalidData = array_merge($baseData, ['name' => str_repeat('A', 51)]);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_decimal_digits()
    {
        $request = new CurrencyRequest();
        $rules = $request->rules();

        $baseData = [
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ];

        // Test valid decimal digits
        $validData = array_merge($baseData, ['decimal_digits' => 2]);
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        // Test minimum decimal digits
        $validData = array_merge($baseData, ['decimal_digits' => 0]);
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        // Test maximum decimal digits
        $validData = array_merge($baseData, ['decimal_digits' => 8]);
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        // Test decimal digits too low
        $invalidData = array_merge($baseData, ['decimal_digits' => -1]);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('decimal_digits', $validator->errors()->toArray());

        // Test decimal digits too high
        $invalidData = array_merge($baseData, ['decimal_digits' => 9]);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('decimal_digits', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_decimal_separator()
    {
        $request = new CurrencyRequest();
        $rules = $request->rules();

        $baseData = [
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'decimal_digits' => 2,
            'thousands_separator' => ',',
            'status' => 'active',
        ];

        // Test valid decimal separators
        $validData = array_merge($baseData, ['decimal_separator' => '.']);
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        $validData = array_merge($baseData, ['decimal_separator' => ',']);
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        // Test invalid decimal separator
        $invalidData = array_merge($baseData, ['decimal_separator' => ';']);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('decimal_separator', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_thousands_separator()
    {
        $request = new CurrencyRequest();
        $rules = $request->rules();

        $baseData = [
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'status' => 'active',
        ];

        // Test valid thousands separators
        $validData = array_merge($baseData, ['thousands_separator' => ',']);
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        $validData = array_merge($baseData, ['thousands_separator' => '.']);
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        $validData = array_merge($baseData, ['thousands_separator' => ' ']);
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        // Test invalid thousands separator
        $invalidData = array_merge($baseData, ['thousands_separator' => ';']);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('thousands_separator', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_sort_order()
    {
        $request = new CurrencyRequest();
        $rules = $request->rules();

        $baseData = [
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ];

        // Test valid sort order
        $validData = array_merge($baseData, ['sort_order' => 1]);
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        $validData = array_merge($baseData, ['sort_order' => 999]);
        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());

        // Test sort order too low
        $invalidData = array_merge($baseData, ['sort_order' => 0]);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('sort_order', $validator->errors()->toArray());

        // Test sort order too high
        $invalidData = array_merge($baseData, ['sort_order' => 1000]);
        $validator = Validator::make($invalidData, $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('sort_order', $validator->errors()->toArray());
    }

    #[Test]
    public function it_allows_sort_order_to_be_optional()
    {
        $request = new CurrencyRequest();
        $rules = $request->rules();

        $validData = [
            'code' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
            // sort_order is not provided
        ];

        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_validates_complete_valid_data()
    {
        $request = new CurrencyRequest();
        $rules = $request->rules();

        $validData = [
            'code' => 'EUR',
            'symbol' => '€',
            'name' => 'Euro',
            'decimal_digits' => 2,
            'decimal_separator' => ',',
            'thousands_separator' => '.',
            'status' => 'active',
            'sort_order' => 2,
        ];

        $validator = Validator::make($validData, $rules);
        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_has_custom_attributes()
    {
        $request = new CurrencyRequest();
        
        // Check if the request has attributes method
        if (method_exists($request, 'attributes')) {
            $attributes = $request->attributes();
            $this->assertIsArray($attributes);
        } else {
            // If no custom attributes method, that's also valid
            $this->assertTrue(true);
        }
    }

    #[Test]
    public function it_has_custom_messages()
    {
        $request = new CurrencyRequest();
        
        // Check if the request has messages method
        if (method_exists($request, 'messages')) {
            $messages = $request->messages();
            $this->assertIsArray($messages);
        } else {
            // If no custom messages method, that's also valid
            $this->assertTrue(true);
        }
    }
}
