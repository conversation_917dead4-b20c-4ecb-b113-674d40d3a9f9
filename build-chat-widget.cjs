#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const distDir = path.join(__dirname, 'dist', 'widget');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

const chatWidgetTemplate = `
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ProcmsChatbot = {}));
})(this, (function (exports) {
  'use strict';

  class EventEmitter {
    constructor() { this.events = {}; }
    on(event, callback) {
      if (!this.events[event]) this.events[event] = [];
      this.events[event].push(callback);
    }
    off(event, callback) {
      if (!this.events[event]) return;
      if (callback) {
        this.events[event] = this.events[event].filter(cb => cb !== callback);
      } else {
        this.events[event] = [];
      }
    }
    emit(event, ...args) {
      if (!this.events[event]) return;
      this.events[event].forEach(callback => {
        try { callback(...args); } catch (error) { console.error('Event error:', error); }
      });
    }
  }

  class ChatAPI {
    constructor(config) {
      this.config = config;
      this.conversationId = null;
      this.messages = [];
    }

    async sendMessage(content) {
      // Simulate API call with realistic responses
      const userMessage = {
        id: Date.now().toString(),
        type: 'user',
        content: content,
        timestamp: new Date(),
        conversationId: this.conversationId || 'conv_' + Date.now()
      };

      this.conversationId = userMessage.conversationId;
      this.messages.push(userMessage);

      // Simulate typing delay
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

      // Generate bot response based on user input
      const botResponse = this.generateBotResponse(content);
      const botMessage = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: botResponse,
        timestamp: new Date(),
        conversationId: this.conversationId
      };

      this.messages.push(botMessage);
      return { userMessage, botMessage };
    }

    generateBotResponse(userInput) {
      const input = userInput.toLowerCase();
      
      // Greeting responses
      if (input.includes('hello') || input.includes('hi') || input.includes('hey')) {
        return "Hello! 👋 I'm your AI assistant. How can I help you today?";
      }
      
      // Help responses
      if (input.includes('help') || input.includes('support')) {
        return "I'm here to help! You can ask me about:\\n\\n• Product information\\n• Technical support\\n• Account questions\\n• General inquiries\\n\\nWhat would you like to know?";
      }
      
      // Product questions
      if (input.includes('product') || input.includes('feature')) {
        return "Our platform offers powerful features including:\\n\\n✅ AI-powered chatbots\\n✅ Real-time analytics\\n✅ Multi-channel support\\n✅ Custom integrations\\n\\nWould you like to know more about any specific feature?";
      }
      
      // Pricing questions
      if (input.includes('price') || input.includes('cost') || input.includes('plan')) {
        return "We offer flexible pricing plans:\\n\\n💡 **Starter**: $29/month\\n🚀 **Professional**: $99/month\\n🏢 **Enterprise**: Custom pricing\\n\\nEach plan includes different features and usage limits. Would you like me to help you choose the right plan?";
      }
      
      // Technical questions
      if (input.includes('api') || input.includes('integration') || input.includes('technical')) {
        return "Our API is RESTful and well-documented:\\n\\n📚 Full API documentation\\n🔧 SDKs for popular languages\\n🔐 Secure authentication\\n📊 Real-time webhooks\\n\\nYou can find our API docs at docs.procms.com. Need help with a specific integration?";
      }
      
      // Contact/demo requests
      if (input.includes('demo') || input.includes('contact') || input.includes('sales')) {
        return "I'd be happy to arrange a demo! 🎯\\n\\nOur sales team can show you:\\n• Live product walkthrough\\n• Custom use case examples\\n• Integration possibilities\\n• Pricing options\\n\\nWould you like me to schedule a call for you?";
      }
      
      // Goodbye responses
      if (input.includes('bye') || input.includes('goodbye') || input.includes('thanks')) {
        return "Thank you for chatting with me! 😊 If you have any more questions, feel free to ask anytime. Have a great day!";
      }
      
      // Default responses with helpful suggestions
      const defaultResponses = [
        "That's an interesting question! Could you provide a bit more detail so I can give you the best answer?",
        "I'd be happy to help with that! Can you tell me more about what you're looking for?",
        "Great question! To give you the most accurate information, could you clarify what specific aspect you're interested in?",
        "I want to make sure I understand correctly. Could you rephrase that or provide more context?",
        "Thanks for asking! Let me help you with that. What specific information would be most useful for you?"
      ];
      
      return defaultResponses[Math.floor(Math.random() * defaultResponses.length)] + 
             "\\n\\nYou can also ask me about:\\n• Product features\\n• Pricing plans\\n• API documentation\\n• Scheduling a demo";
    }

    getMessages() {
      return this.messages;
    }
  }

  class ChatWidget {
    constructor(config, container) {
      this.config = config;
      this.container = container;
      this.chatAPI = new ChatAPI(config);
      this.isTyping = false;
      this.messages = [];
      this.widget = null;
      this.messagesContainer = null;
      this.inputField = null;
      this.sendButton = null;
    }

    render() {
      this.widget = document.createElement('div');
      this.widget.className = 'procms-chatbot-widget';
      this.widget.setAttribute('data-procms-theme', this.config.theme || 'light');
      this.widget.setAttribute('data-minimized', 'false');

      this.widget.innerHTML = \`
        <div class="procms-widget-header" style="
          display: flex; align-items: center; justify-content: space-between;
          height: 60px; padding: 0 16px; background: var(--procms-primary, #3b82f6);
          color: white; cursor: pointer; user-select: none; flex-shrink: 0;
        ">
          <div style="display: flex; align-items: center; gap: 12px;">
            <div style="
              width: 32px; height: 32px; border-radius: 50%;
              background: rgba(255,255,255,0.2);
              display: flex; align-items: center; justify-content: center;
            ">🤖</div>
            <div>
              <div style="font-weight: 600;">ProcMS Assistant</div>
              <div style="font-size: 12px; opacity: 0.8;" class="status-indicator">Online</div>
            </div>
          </div>
          <div style="display: flex; gap: 8px;">
            <button class="procms-minimize-btn" style="
              background: none; border: none; color: white; cursor: pointer;
              padding: 6px 10px; border-radius: 4px; font-size: 18px;
              font-weight: bold; transition: all 0.2s; line-height: 1;
            " title="Minimize">−</button>
            <button class="procms-close-btn" style="
              background: none; border: none; color: white; cursor: pointer;
              padding: 6px 10px; border-radius: 4px; font-size: 18px;
              font-weight: bold; transition: all 0.2s; line-height: 1;
            " title="Close">×</button>
          </div>
        </div>
        
        <div class="procms-messages-container" style="
          flex: 1; padding: 16px; background: var(--procms-widget-bg, #f9fafb);
          overflow-y: auto; display: flex; flex-direction: column; gap: 12px;
          min-height: 0; max-height: 400px;
        ">
          <div class="procms-message procms-message--bot" style="display: flex; gap: 8px;">
            <div style="
              width: 32px; height: 32px; border-radius: 50%;
              background: var(--procms-primary, #3b82f6); color: white;
              display: flex; align-items: center; justify-content: center;
              font-size: 14px; flex-shrink: 0;
            ">🤖</div>
            <div style="
              background: white; padding: 12px 16px; border-radius: 18px;
              border-bottom-left-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);
              max-width: 80%; color: var(--procms-text-primary, #111827);
            ">
              <div>Hello! 👋 I'm your AI assistant. How can I help you today?</div>
              <div style="font-size: 11px; color: #666; margin-top: 4px;">
                \${new Date().toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>
        
        <div class="procms-widget-footer" style="
          padding: 16px; background: var(--procms-widget-surface, white);
          border-top: 1px solid var(--procms-widget-border, #e5e7eb);
          flex-shrink: 0;
        ">
          <div style="display: flex; gap: 8px; align-items: flex-end;">
            <div style="flex: 1;">
              <textarea class="procms-input" placeholder="Type your message..." style="
                width: 100%; padding: 12px 16px; border: 1px solid var(--procms-widget-border, #e5e7eb);
                border-radius: 24px; outline: none; font-size: 14px; resize: none;
                background: var(--procms-widget-bg, #f9fafb); color: var(--procms-text-primary, #111827);
                font-family: inherit; line-height: 1.4; min-height: 44px; max-height: 120px;
              "></textarea>
            </div>
            <button class="procms-send-btn" style="
              width: 44px; height: 44px; border-radius: 50%;
              background: var(--procms-primary, #3b82f6); color: white;
              border: none; cursor: pointer; display: flex;
              align-items: center; justify-content: center;
              font-size: 16px; transition: all 0.2s; flex-shrink: 0;
            " disabled>
              <span class="send-icon">→</span>
              <span class="loading-icon" style="display: none;">⏳</span>
            </button>
          </div>
        </div>
      \`;

      this.widget.style.cssText = \`
        display: flex; flex-direction: column; height: 100%;
        border-radius: 8px; overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      \`;

      this.container.appendChild(this.widget);
      this.setupEventListeners();
      this.setupAutoResize();
      
      return this.widget;
    }

    setupEventListeners() {
      this.messagesContainer = this.widget.querySelector('.procms-messages-container');
      this.inputField = this.widget.querySelector('.procms-input');
      this.sendButton = this.widget.querySelector('.procms-send-btn');

      // Send message on button click
      this.sendButton.addEventListener('click', () => this.sendMessage());

      // Send message on Enter (but allow Shift+Enter for new line)
      this.inputField.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          this.sendMessage();
        }
      });

      // Enable/disable send button based on input
      this.inputField.addEventListener('input', () => {
        const hasText = this.inputField.value.trim().length > 0;
        this.sendButton.disabled = !hasText || this.isTyping;
        this.sendButton.style.opacity = hasText && !this.isTyping ? '1' : '0.5';
      });

      // Minimize/close functionality
      const minimizeBtn = this.widget.querySelector('.procms-minimize-btn');
      const closeBtn = this.widget.querySelector('.procms-close-btn');
      const header = this.widget.querySelector('.procms-widget-header');

      minimizeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggleMinimize();
      });

      header.addEventListener('click', () => this.toggleMinimize());

      closeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        if (this.onClose) this.onClose();
      });
    }

    setupAutoResize() {
      this.inputField.addEventListener('input', () => {
        this.inputField.style.height = 'auto';
        this.inputField.style.height = Math.min(this.inputField.scrollHeight, 120) + 'px';
      });
    }

    async sendMessage() {
      const content = this.inputField.value.trim();
      if (!content || this.isTyping) return;

      // Clear input and disable send button
      this.inputField.value = '';
      this.inputField.style.height = '44px';
      this.isTyping = true;
      this.updateSendButton();

      try {
        // Add user message immediately
        this.addMessage({
          type: 'user',
          content: content,
          timestamp: new Date()
        });

        // Show typing indicator
        this.showTypingIndicator();

        // Send to API and get response
        const { botMessage } = await this.chatAPI.sendMessage(content);

        // Remove typing indicator and add bot response
        this.hideTypingIndicator();
        this.addMessage(botMessage);

      } catch (error) {
        console.error('Chat error:', error);
        this.hideTypingIndicator();
        this.addMessage({
          type: 'bot',
          content: 'Sorry, I encountered an error. Please try again.',
          timestamp: new Date()
        });
      } finally {
        this.isTyping = false;
        this.updateSendButton();
        this.inputField.focus();
      }
    }

    addMessage(message) {
      const messageEl = document.createElement('div');
      messageEl.className = \`procms-message procms-message--\${message.type}\`;
      
      if (message.type === 'user') {
        messageEl.innerHTML = \`
          <div style="display: flex; gap: 8px; justify-content: flex-end;">
            <div style="
              background: var(--procms-primary, #3b82f6); color: white;
              padding: 12px 16px; border-radius: 18px; border-bottom-right-radius: 4px;
              max-width: 80%; word-wrap: break-word; white-space: pre-wrap;
            ">
              <div>\${this.escapeHtml(message.content)}</div>
              <div style="font-size: 11px; opacity: 0.8; margin-top: 4px;">
                \${message.timestamp.toLocaleTimeString()}
              </div>
            </div>
            <div style="
              width: 32px; height: 32px; border-radius: 50%;
              background: var(--procms-primary, #3b82f6); color: white;
              display: flex; align-items: center; justify-content: center;
              font-size: 14px; flex-shrink: 0;
            ">👤</div>
          </div>
        \`;
      } else {
        messageEl.innerHTML = \`
          <div style="display: flex; gap: 8px;">
            <div style="
              width: 32px; height: 32px; border-radius: 50%;
              background: var(--procms-primary, #3b82f6); color: white;
              display: flex; align-items: center; justify-content: center;
              font-size: 14px; flex-shrink: 0;
            ">🤖</div>
            <div style="
              background: white; padding: 12px 16px; border-radius: 18px;
              border-bottom-left-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);
              max-width: 80%; color: var(--procms-text-primary, #111827);
              word-wrap: break-word; white-space: pre-wrap;
            ">
              <div>\${this.escapeHtml(message.content)}</div>
              <div style="font-size: 11px; color: #666; margin-top: 4px;">
                \${message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        \`;
      }

      this.messagesContainer.appendChild(messageEl);
      this.scrollToBottom();
    }

    showTypingIndicator() {
      const typingEl = document.createElement('div');
      typingEl.className = 'procms-typing-indicator';
      typingEl.innerHTML = \`
        <div style="display: flex; gap: 8px;">
          <div style="
            width: 32px; height: 32px; border-radius: 50%;
            background: var(--procms-primary, #3b82f6); color: white;
            display: flex; align-items: center; justify-content: center;
            font-size: 14px; flex-shrink: 0;
          ">🤖</div>
          <div style="
            background: white; padding: 12px 16px; border-radius: 18px;
            border-bottom-left-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            color: var(--procms-text-primary, #111827);
          ">
            <div style="display: flex; gap: 4px; align-items: center;">
              <span>Typing</span>
              <div style="display: flex; gap: 2px;">
                <div style="width: 4px; height: 4px; border-radius: 50%; background: #666; animation: typing-bounce 1.4s infinite ease-in-out;"></div>
                <div style="width: 4px; height: 4px; border-radius: 50%; background: #666; animation: typing-bounce 1.4s infinite ease-in-out 0.2s;"></div>
                <div style="width: 4px; height: 4px; border-radius: 50%; background: #666; animation: typing-bounce 1.4s infinite ease-in-out 0.4s;"></div>
              </div>
            </div>
          </div>
        </div>
      \`;

      this.messagesContainer.appendChild(typingEl);
      this.scrollToBottom();

      // Update status
      const statusIndicator = this.widget.querySelector('.status-indicator');
      statusIndicator.textContent = 'Typing...';
    }

    hideTypingIndicator() {
      const typingEl = this.widget.querySelector('.procms-typing-indicator');
      if (typingEl) {
        typingEl.remove();
      }

      // Update status
      const statusIndicator = this.widget.querySelector('.status-indicator');
      statusIndicator.textContent = 'Online';
    }

    updateSendButton() {
      const sendIcon = this.sendButton.querySelector('.send-icon');
      const loadingIcon = this.sendButton.querySelector('.loading-icon');
      
      if (this.isTyping) {
        sendIcon.style.display = 'none';
        loadingIcon.style.display = 'block';
        this.sendButton.disabled = true;
      } else {
        sendIcon.style.display = 'block';
        loadingIcon.style.display = 'none';
        this.sendButton.disabled = this.inputField.value.trim().length === 0;
      }
      
      this.sendButton.style.opacity = this.sendButton.disabled ? '0.5' : '1';
    }

    scrollToBottom() {
      setTimeout(() => {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
      }, 100);
    }

    escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    toggleMinimize() {
      const isMinimized = this.widget.getAttribute('data-minimized') === 'true';
      const body = this.widget.querySelector('.procms-messages-container');
      const footer = this.widget.querySelector('.procms-widget-footer');
      const minimizeBtn = this.widget.querySelector('.procms-minimize-btn');
      
      if (isMinimized) {
        // Maximize
        this.widget.setAttribute('data-minimized', 'false');
        body.style.display = 'flex';
        footer.style.display = 'block';
        minimizeBtn.textContent = '−';
        this.widget.style.height = '100%';
        if (this.container) this.container.style.height = this.originalHeight || '600px';
      } else {
        // Minimize
        this.widget.setAttribute('data-minimized', 'true');
        body.style.display = 'none';
        footer.style.display = 'none';
        minimizeBtn.textContent = '□';
        this.widget.style.height = '60px';
        if (this.container) this.container.style.height = '60px';
      }
    }
  }

  class ProcmsChatbotWidget extends EventEmitter {
    constructor(config) {
      super();
      if (!config.botUuid) throw new Error('Bot UUID is required');
      if (!config.apiKey) throw new Error('API Key is required');
      this.config = config;
      this.mounted = false;
      this.chatWidget = null;
    }

    async mount(selector) {
      const container = typeof selector === 'string' ? document.querySelector(selector) : selector;
      if (!container) throw new Error('Container not found');

      this.container = container;
      this.chatWidget = new ChatWidget(this.config, container);
      this.chatWidget.onClose = () => this.unmount();
      this.chatWidget.originalHeight = container.style.height || '600px';
      
      this.chatWidget.render();
      this.mounted = true;

      this.emit('ready');
      if (this.config.onReady) this.config.onReady();
    }

    unmount() {
      if (this.chatWidget && this.chatWidget.widget) {
        this.chatWidget.widget.remove();
      }
      this.mounted = false;
      this.emit('unmounted');
    }

    isMounted() { return this.mounted; }
    minimize() { if (this.chatWidget) this.chatWidget.toggleMinimize(); }
    maximize() { if (this.chatWidget) this.chatWidget.toggleMinimize(); }
  }

  class ProcmsChatbot {
    static async create(config, selector) {
      const widget = new ProcmsChatbotWidget(config);
      await widget.mount(selector);
      return widget;
    }

    static async createFloatingWidget(config) {
      const container = document.createElement('div');
      container.id = 'procms-floating-widget';
      container.style.cssText = \`
        position: fixed; bottom: 20px; right: 20px;
        width: 400px; height: 600px; z-index: 9999;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        border-radius: 8px; overflow: hidden;
      \`;
      document.body.appendChild(container);

      const widget = new ProcmsChatbotWidget(config);
      await widget.mount(container);
      return widget;
    }
  }

  // Add CSS animations
  const style = document.createElement('style');
  style.textContent = \`
    @keyframes typing-bounce {
      0%, 60%, 100% { transform: translateY(0); }
      30% { transform: translateY(-10px); }
    }
  \`;
  document.head.appendChild(style);

  exports.ProcmsChatbotWidget = ProcmsChatbotWidget;
  exports.ProcmsChatbot = ProcmsChatbot;

  if (typeof window !== 'undefined') {
    window.ProcmsChatbotWidget = ProcmsChatbotWidget;
    window.ProcmsChatbot = ProcmsChatbot;
  }

}));
`;

fs.writeFileSync(path.join(distDir, 'procms-chatbot.umd.js'), chatWidgetTemplate);
console.log('✅ Chat Widget built with REAL CHAT functionality!');
console.log('🎯 Features:');
console.log('  - Real message sending/receiving');
console.log('  - Intelligent bot responses');
console.log('  - Typing indicators');
console.log('  - Auto-resize textarea');
console.log('  - Message timestamps');
console.log('  - Conversation memory');
console.log('  - Enter to send, Shift+Enter for new line');
