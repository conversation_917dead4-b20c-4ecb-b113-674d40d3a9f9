<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProcMS Chatbot Widget - Integration Example</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
        }
        .demo-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }
        .demo-description {
            color: #666;
            margin-bottom: 20px;
        }
        .widget-container {
            width: 400px;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .floating-trigger {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            font-size: 24px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
        }
        .code-block code {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 14px;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ProcMS Chatbot Widget - Integration Examples</h1>
        <p>This page demonstrates different ways to integrate the ProcMS Chatbot Widget into your website.</p>

        <!-- Example 1: Embedded Widget -->
        <div class="demo-section">
            <div class="demo-title">1. Embedded Widget (JavaScript)</div>
            <div class="demo-description">
                Widget embedded directly in a container with full JavaScript functionality.
            </div>
            
            <div class="code-block">
                <code>
// JavaScript Integration
const widget = new ProcmsChatbotWidget({
    botUuid: 'your-bot-uuid-here',
    apiKey: 'pk_live_your_api_key_here',
    theme: 'light',
    showHeader: true,
    showAvatar: true
});

await widget.mount('#chat-container');
                </code>
            </div>

            <div id="chat-container-1" class="widget-container"></div>
            <button class="btn" onclick="loadEmbeddedWidget()">Load Embedded Widget</button>
            <button class="btn" onclick="unloadWidget(1)">Unload Widget</button>
            <div id="status-1" class="status" style="display: none;"></div>
        </div>

        <!-- Example 2: Iframe Integration -->
        <div class="demo-section">
            <div class="demo-title">2. Iframe Integration</div>
            <div class="demo-description">
                Widget loaded in an iframe for maximum compatibility and isolation.
            </div>
            
            <div class="code-block">
                <code>
// Iframe Integration
&lt;iframe 
    src="https://widget.procms.com?bot_uuid=your-bot-uuid&api_key=your-api-key"
    width="400" 
    height="600" 
    frameborder="0"
    style="border-radius: 8px;"&gt;
&lt;/iframe&gt;

// Or generate programmatically
const iframeCode = ProcmsChatbot.generateIframeCode({
    botUuid: 'your-bot-uuid-here',
    apiKey: 'pk_live_your_api_key_here'
});
                </code>
            </div>

            <div id="iframe-container" class="widget-container"></div>
            <button class="btn" onclick="loadIframeWidget()">Load Iframe Widget</button>
            <button class="btn" onclick="unloadWidget(2)">Unload Widget</button>
            <div id="status-2" class="status" style="display: none;"></div>
        </div>

        <!-- Example 3: Auto-Detection -->
        <div class="demo-section">
            <div class="demo-title">3. Smart Auto-Detection</div>
            <div class="demo-description">
                Widget automatically detects the best integration method based on the environment.
            </div>
            
            <div class="code-block">
                <code>
// Auto-detection
const widget = new ProcmsChatbotWidget({
    botUuid: 'your-bot-uuid-here',
    apiKey: 'pk_live_your_api_key_here',
    mode: 'auto' // Will auto-detect best method
});

// Get detection report
const report = ProcmsChatbot.getDetectionReport(config);
console.log('Recommended mode:', report.recommendedMode);
console.log('Detection reasons:', report.reasons);
                </code>
            </div>

            <div id="chat-container-3" class="widget-container"></div>
            <button class="btn" onclick="loadAutoWidget()">Load Auto-Detected Widget</button>
            <button class="btn" onclick="showDetectionReport()">Show Detection Report</button>
            <button class="btn" onclick="unloadWidget(3)">Unload Widget</button>
            <div id="status-3" class="status" style="display: none;"></div>
        </div>

        <!-- Example 4: Floating Widget -->
        <div class="demo-section">
            <div class="demo-title">4. Floating Chat Button</div>
            <div class="demo-description">
                Widget appears as a floating chat button, commonly used for customer support.
            </div>
            
            <div class="code-block">
                <code>
// Floating Widget
const floatingWidget = await ProcmsChatbot.createFloatingWidget({
    botUuid: 'your-bot-uuid-here',
    apiKey: 'pk_live_your_api_key_here',
    position: 'bottom-right',
    autoOpen: false
});
                </code>
            </div>

            <button class="btn" onclick="loadFloatingWidget()">Create Floating Widget</button>
            <button class="btn" onclick="removeFloatingWidget()">Remove Floating Widget</button>
            <div id="status-4" class="status" style="display: none;"></div>
        </div>

        <!-- Example 5: Auto-Initialize -->
        <div class="demo-section">
            <div class="demo-title">5. Auto-Initialize with Global Config</div>
            <div class="demo-description">
                Widget automatically initializes when the page loads using global configuration.
            </div>
            
            <div class="code-block">
                <code>
// Set global config before loading widget script
&lt;script&gt;
window.PROCMS_CHATBOT_CONFIG = {
    botUuid: 'your-bot-uuid-here',
    apiKey: 'pk_live_your_api_key_here',
    position: 'bottom-right'
};
&lt;/script&gt;

&lt;!-- Widget will auto-initialize on page load --&gt;
&lt;script src="https://cdn.procms.com/widget/procms-chatbot.umd.js"&gt;&lt;/script&gt;
                </code>
            </div>

            <div class="status info">
                Auto-initialization example - check the browser console for initialization logs.
            </div>
        </div>
    </div>

    <!-- Load the widget library -->
    <script src="../dist/widget/procms-chatbot.umd.js"></script>
    
    <script>
        // Demo configuration (replace with your actual bot UUID and API key)
        const DEMO_CONFIG = {
            botUuid: 'demo-bot-uuid-123',
            apiKey: 'pk_test_demo_api_key_12345678901234567890',
            theme: 'light',
            showHeader: true,
            showAvatar: true,
            width: 400,
            height: 600
        };

        let widgets = {};
        let floatingWidget = null;

        // Helper functions
        function showStatus(id, message, type = 'info') {
            const statusEl = document.getElementById(`status-${id}`);
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
        }

        function hideStatus(id) {
            const statusEl = document.getElementById(`status-${id}`);
            statusEl.style.display = 'none';
        }

        // Example 1: Embedded Widget
        async function loadEmbeddedWidget() {
            try {
                showStatus(1, 'Loading embedded widget...', 'info');

                // Check if widget classes are available
                if (typeof ProcmsChatbotWidget === 'undefined') {
                    throw new Error('ProcmsChatbotWidget not found. Make sure the widget library is loaded.');
                }

                const widget = new ProcmsChatbotWidget({
                    ...DEMO_CONFIG,
                    mode: 'widget'
                });

                await widget.mount('#chat-container-1');
                widgets[1] = widget;

                showStatus(1, 'Embedded widget loaded successfully!', 'success');
            } catch (error) {
                showStatus(1, `Error: ${error.message}`, 'error');
                console.error('Widget loading error:', error);
            }
        }

        // Example 2: Iframe Widget
        function loadIframeWidget() {
            try {
                showStatus(2, 'Loading iframe widget...', 'info');
                
                const iframeCode = ProcmsChatbot.generateIframeCode({
                    ...DEMO_CONFIG,
                    iframeUrl: 'https://widget.procms.com'
                });
                
                document.getElementById('iframe-container').innerHTML = iframeCode;
                
                showStatus(2, 'Iframe widget loaded successfully!', 'success');
            } catch (error) {
                showStatus(2, `Error: ${error.message}`, 'error');
            }
        }

        // Example 3: Auto-Detection
        async function loadAutoWidget() {
            try {
                showStatus(3, 'Loading auto-detected widget...', 'info');
                
                const widget = new ProcmsChatbotWidget({
                    ...DEMO_CONFIG,
                    mode: 'auto'
                });

                await widget.mount('#chat-container-3');
                widgets[3] = widget;
                
                const mode = widget.getMode();
                showStatus(3, `Auto-detected widget loaded successfully! Mode: ${mode}`, 'success');
            } catch (error) {
                showStatus(3, `Error: ${error.message}`, 'error');
            }
        }

        // Show detection report
        function showDetectionReport() {
            const report = ProcmsChatbot.getDetectionReport(DEMO_CONFIG);
            alert(`Detection Report:
Recommended Mode: ${report.recommendedMode}
Reasons: ${report.reasons.join(', ')}
Conflicts: ${JSON.stringify(report.conflicts, null, 2)}`);
        }

        // Example 4: Floating Widget
        async function loadFloatingWidget() {
            try {
                showStatus(4, 'Creating floating widget...', 'info');
                
                floatingWidget = await ProcmsChatbot.createFloatingWidget({
                    ...DEMO_CONFIG,
                    position: 'bottom-right'
                });
                
                showStatus(4, 'Floating widget created! Check bottom-right corner.', 'success');
            } catch (error) {
                showStatus(4, `Error: ${error.message}`, 'error');
            }
        }

        function removeFloatingWidget() {
            if (floatingWidget) {
                floatingWidget.unmount();
                const container = document.getElementById('procms-floating-widget');
                if (container) container.remove();
                floatingWidget = null;
                showStatus(4, 'Floating widget removed.', 'info');
            }
        }

        // Unload widget
        function unloadWidget(id) {
            if (widgets[id]) {
                widgets[id].unmount();
                delete widgets[id];
                showStatus(id, 'Widget unloaded.', 'info');
            } else if (id === 2) {
                document.getElementById('iframe-container').innerHTML = '';
                showStatus(2, 'Iframe widget removed.', 'info');
            }
        }

        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
        });

        // Log widget library info
        console.log('ProcMS Chatbot Widget Library loaded');
        console.log('Available classes:', typeof ProcmsChatbotWidget !== 'undefined' ? { ProcmsChatbotWidget, ProcmsChatbot } : 'Not loaded yet');

        // Check if library loaded correctly
        if (typeof ProcmsChatbotWidget === 'undefined') {
            console.warn('ProcmsChatbotWidget not found. Check if the library loaded correctly.');
        } else {
            console.log('✅ Widget library loaded successfully!');
        }
    </script>
</body>
</html>
