<?php

namespace Modules\Currency\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;
use Modules\Currency\Models\Currency;
use Modules\Currency\Models\ExchangeRate;

class CurrencyService
{
    /**
     * Cache tag for currencies
     */
    private const CACHE_TAG = 'currencies';

    /**
     * Get active currencies for dropdown.
     */
    public function getActiveCurrencies(): Collection
    {
        if (!enabledCache()) {
            return Currency::getActiveCurrencies();
        }

        $cacheTtl = (int) setting('currency.currency_list_cache_ttl', 3600); // seconds
        return Cache::tags([self::CACHE_TAG])->remember(
            key: 'currencies.active.list',
            ttl: now()->addSeconds($cacheTtl),
            callback: fn() => Currency::getActiveCurrencies()
        );
    }

    
    /**
     * Get the default currency from settings or first active currency.
     */
    public function getDefaultCurrency(): ?Currency
    {
        $defaultCode = setting('currency.currency_default', 'USD');

        if (!enabledCache()) {
            return Currency::where('code', $defaultCode)->active()->first()
                ?? Currency::active()->ordered()->first();
        }

        return Cache::tags([self::CACHE_TAG])->remember(
            key: 'currencies.default',
            ttl: cacheTTL(),
            callback: fn() => Currency::where('code', $defaultCode)->active()->first()
                ?? Currency::active()->ordered()->first()
        );
    }

    /**
     * Find currency by code.
     */
    public function findByCode(string $code): ?Currency
    {
        if (!enabledCache()) {
            return Currency::findByCode($code);
        }

        return Cache::tags([self::CACHE_TAG])->remember(
            key: "currencies.code.{$code}",
            ttl: cacheTTL(),
            callback: fn() => Currency::findByCode($code)
        );
    }

    /**
     * Convert amount between currencies.
     */
    public function convertAmount(float $amount, string $fromCurrency, string $toCurrency): ?float
    {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        $rate = $this->getExchangeRate($fromCurrency, $toCurrency);
        
        if ($rate === null) {
            return null;
        }

        return $amount * $rate;
    }

    /**
     * Get exchange rate between two currencies.
     */
    public function getExchangeRate(string $fromCurrency, string $toCurrency): ?float
    {
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        $cacheTtl = (int) setting('currency.currency_cache_ttl', 300); // seconds
        return Cache::remember(
            key: "exchange_rates.{$fromCurrency}.{$toCurrency}",
            ttl: now()->addSeconds($cacheTtl),
            callback: fn() => ExchangeRate::getRate($fromCurrency, $toCurrency)
        );
    }

    /**
     * Format amount with currency.
     */
    public function formatAmount(float $amount, string $currencyCode): string
    {
        $currency = $this->findByCode($currencyCode);
        
        if (!$currency) {
            return number_format($amount, 2) . ' ' . $currencyCode;
        }

        return $currency->formatAmount($amount);
    }

    /**
     * Get all supported currency codes.
     */
    public function getSupportedCurrencyCodes(): array
    {
        if (!enabledCache()) {
            return Currency::active()->pluck('code')->toArray();
        }

        return Cache::remember(
            key: 'currencies.codes',
            ttl: cacheTTL(),
            callback: fn() => Currency::active()->pluck('code')->toArray()
        );
    }

    /**
     * Update exchange rate.
     */
    public function updateExchangeRate(string $baseCurrency, string $targetCurrency, float $rate): ExchangeRate
    {
        $exchangeRate = ExchangeRate::updateOrCreate(
            [
                'base_currency' => $baseCurrency,
                'target_currency' => $targetCurrency,
            ],
            [
                'rate' => $rate,
                'fetched_at' => now(),
            ]
        );
        return $exchangeRate;
    }

    /**
     * Get currencies for dropdown with formatted display.
     */
    public function getCurrenciesForDropdown(): Collection
    {
        return $this->getActiveCurrencies()->map(function ($currency) {
            return [
                'value' => $currency->code,
                'label' => $currency->display_name,
                'symbol' => $currency->symbol,
            ];
        });
    }

    /**
     * Clear currency cache.
     */
    public function clearCache(): bool
    {
        return Cache::tags([self::CACHE_TAG])->flush();
    }

    /**
     * Clear exchange rate cache for specific currency pair.
     */
    public function clearExchangeRateCache(?string $fromCurrency = null, ?string $toCurrency = null): bool
    {
        if ($fromCurrency && $toCurrency) {
            return Cache::forget("exchange_rates.{$fromCurrency}.{$toCurrency}");
        }

        return Cache::flush();
    }

    /**
     * Find currency by symbol.
     */
    public function findBySymbol(string $symbol): ?Currency
    {
        if (!enabledCache()) {
            return Currency::where('symbol', $symbol)->active()->first();
        }

        return Cache::tags([self::CACHE_TAG])->remember(
            key: "currencies.symbol.{$symbol}",
            ttl: cacheTTL(),
            callback: fn() => Currency::where('symbol', $symbol)->active()->first()
        );
    }

    /**
     * Check if currency is supported.
     */
    public function isCurrencySupported(string $code): bool
    {
        return Currency::where('code', strtoupper($code))->active()->exists();
    }

    /**
     * Get currency display name.
     */
    public function getCurrencyDisplayName(string $code): string
    {
        $currency = $this->findByCode($code);

        if (!$currency) {
            return $code;
        }

        return $currency->name . ' (' . $currency->symbol . ')';
    }

    /**
     * Validate currency pair.
     */
    public function isValidCurrencyPair(string $fromCurrency, string $toCurrency): bool
    {
        if ($fromCurrency === $toCurrency) {
            return false;
        }

        return $this->isCurrencySupported($fromCurrency) && $this->isCurrencySupported($toCurrency);
    }

    /**
     * Get available exchange rates for a base currency.
     */
    public function getAvailableExchangeRates(string $baseCurrency): array
    {
        $rates = ExchangeRate::where('base_currency', $baseCurrency)->get();

        return $rates->pluck('rate', 'target_currency')->toArray();
    }

    /**
     * Batch convert amounts.
     */
    public function batchConvertAmounts(array $amounts, string $fromCurrency, string $toCurrency): array
    {
        $rate = $this->getExchangeRate($fromCurrency, $toCurrency);

        if ($rate === null) {
            return array_fill(0, count($amounts), null);
        }

        return array_map(fn($amount) => $amount * $rate, $amounts);
    }
}
