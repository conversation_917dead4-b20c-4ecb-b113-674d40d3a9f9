<?php

namespace Modules\Organization\Facades;

use Illuminate\Support\Facades\Facade;
use Illuminate\Support\Collection;
use Modules\Organization\Models\Organization;
use Modules\Organization\Models\OrganizationInvitation;
use Modules\User\Models\User;

/**
 * Organization Facade
 *
 * Provides convenient access to public organization functionality through a static interface.
 * Only exposes public/user-level methods. Administrative methods should be accessed
 * through direct service injection or Auth controllers.
 *
 * @method static OrganizationInvitation|null getInvitationByToken(string $token) Get invitation by token
 * @method static array validateInvitation(string $token) Validate invitation token and expiration
 * @method static array acceptInvitation(string $token, User $user) Accept invitation and create member
 * @method static array acceptInvitationAsGuest(string $token, User $user) Accept invitation as guest
 * @method static Collection getPublicOrganizations() Get public organizations for listing
 * @method static Organization|null getByUuid(string $uuid) Get organization by UUID (public only)
 * @method static Collection searchPublicOrganizations(string $query, int $limit = 10) Search public organizations
 * @method static array getOrganizationTypes() Get organization types for dropdown
 * @method static array getInvitationRoles() Get invitation roles for dropdown
 *
 * @see \Modules\Organization\Services\OrganizationService
 */
class OrganizationFacade extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'organization.service';
    }
}
