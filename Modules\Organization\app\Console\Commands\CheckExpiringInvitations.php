<?php

namespace Modules\Organization\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Modules\Organization\Models\OrganizationInvitation;
use Modules\Organization\Events\OrganizationInvitationExpiring;

class CheckExpiringInvitations extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'organization:check-expiring-invitations 
                            {--hours=24 : Hours before expiration to send reminder}
                            {--dry-run : Show what would be done without sending emails}';

    /**
     * The console command description.
     */
    protected $description = 'Check for expiring organization invitations and send reminder emails';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $hours = (int) $this->option('hours');
        $dryRun = $this->option('dry-run');
        
        $this->info("Checking for invitations expiring within {$hours} hours...");
        
        // Find invitations that will expire within specified hours
        $expiringInvitations = OrganizationInvitation::where('expires_at', '>', Carbon::now())
            ->where('expires_at', '<=', Carbon::now()->addHours($hours))
            ->with(['organization', 'inviter'])
            ->get();

        if ($expiringInvitations->isEmpty()) {
            $this->info('No expiring invitations found.');
            return self::SUCCESS;
        }

        $this->info("Found {$expiringInvitations->count()} expiring invitations:");

        $remindersSent = 0;
        
        foreach ($expiringInvitations as $invitation) {
            $hoursRemaining = Carbon::now()->diffInHours($invitation->expires_at);
            
            $this->line("- {$invitation->email} for {$invitation->organization->name} (expires in {$hoursRemaining}h)");
            
            if (!$dryRun) {
                try {
                    // Fire event to send reminder
                    event(new OrganizationInvitationExpiring($invitation));
                    $remindersSent++;
                    $this->info("  ✓ Reminder sent");
                } catch (\Exception $e) {
                    $this->error("  ✗ Failed to send reminder: " . $e->getMessage());
                }
            } else {
                $this->info("  → Would send reminder (dry-run mode)");
            }
        }

        if ($dryRun) {
            $this->info("\nDry-run completed. {$expiringInvitations->count()} reminders would be sent.");
        } else {
            $this->info("\nCompleted. {$remindersSent} reminder emails sent.");
        }

        // Also clean up expired invitations
        $this->cleanupExpiredInvitations($dryRun);

        return self::SUCCESS;
    }

    /**
     * Clean up expired invitations.
     */
    private function cleanupExpiredInvitations(bool $dryRun): void
    {
        $expiredInvitations = OrganizationInvitation::where('expires_at', '<=', Carbon::now())->get();

        if ($expiredInvitations->isEmpty()) {
            $this->info('No expired invitations to clean up.');
            return;
        }

        $this->info("\nFound {$expiredInvitations->count()} expired invitations to clean up:");

        foreach ($expiredInvitations as $invitation) {
            $this->line("- {$invitation->email} for {$invitation->organization->name} (expired " . 
                       $invitation->expires_at->diffForHumans() . ")");
            
            if (!$dryRun) {
                $invitation->delete();
                $this->info("  ✓ Deleted");
            } else {
                $this->info("  → Would delete (dry-run mode)");
            }
        }

        if ($dryRun) {
            $this->info("Dry-run: {$expiredInvitations->count()} expired invitations would be deleted.");
        } else {
            $this->info("Cleaned up {$expiredInvitations->count()} expired invitations.");
        }
    }
}
