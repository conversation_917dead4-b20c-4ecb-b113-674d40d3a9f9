<?php

namespace Modules\ModelAI\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

/**
 * @property mixed $id
 * @property mixed $model_ai_id
 */
class ModelAIRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'key' => [
                'required',
                'string',
                'max:255',
                Rule::unique('model_ai')->ignore($this->id)->whereNull('deleted_at'),
            ],
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'model_provider_id' => [
                'required',
                'integer',
                'exists:model_providers,id',
            ],
            'version' => [
                'nullable',
                'string',
                'max:50',
            ],
            'api_endpoint' => [
                'sometimes',
                'required',
                'string',
                'max:500',
            ],
            'streaming' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'vision' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'function_calling' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'is_default' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'sort_order' => [
                'sometimes',
                'required',
                'integer',
                'min:1',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(['active', 'inactive', 'draft']),
            ],
            'categories' => [
                'nullable',
                'array',
            ],
            'categories.*' => [
                'integer',
                'exists:model_categories,id',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'key' => __('Model Key'),
            'name' => __('Model Name'),
            'description' => __('Description'),
            'model_provider_id' => __('Provider'),
            'version' => __('Version'),
            'api_endpoint' => __('API Endpoint'),
            'streaming' => __('Streaming Support'),
            'vision' => __('Vision Support'),
            'function_calling' => __('Function Calling Support'),
            'is_default' => __('Is Default'),
            'sort_order' => __('Sort Order'),
            'status' => __('Status'),
            'categories' => __('Categories'),
            'categories.*' => __('Category'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'key.alpha_dash' => __('The model key may only contain letters, numbers, dashes and underscores.'),
            'api_endpoint.url' => __('The API endpoint must be a valid URL.'),
            'model_provider_id.exists' => __('The selected provider is invalid.'),
        ];
    }

}
