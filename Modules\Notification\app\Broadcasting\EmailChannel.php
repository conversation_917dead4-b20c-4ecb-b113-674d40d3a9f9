<?php

namespace Modules\Notification\Broadcasting;

use Exception;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;
use Modules\Setting\Facades\SettingFacade;
use Modules\User\Models\User;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;

class EmailChannel
{
    /**
     * Gửi email thông báo
     * @throws Exception
     */
    public function send(array $notifiable, array $content, array $data): void
    {
        try {
            $this->configureMailFromSettings();

            $email = $this->resolveEmailAddress($notifiable);

            Mail::send([], [], function ($message) use ($content, $email) {
                $message->to($email)
                    ->subject($content['subject'])
                    ->html($content['content']);

                $fromAddress = SettingFacade::getSetting('email', 'email.from.address') ?: '<EMAIL>';
                $fromName = SettingFacade::getSetting('email', 'email.from.name') ?: 'Laravel ProCMS';
                $message->from($fromAddress, $fromName);

                $replyTo = SettingFacade::getSetting('email', 'email.reply.to');
                if ($replyTo) {
                    $message->replyTo($replyTo);
                }
            });

        } catch (TransportExceptionInterface $e) {
            $this->handleTransportException($e, $notifiable, $content, $data);
            throw new Exception("Email sending failed: " . $e->getMessage(), $e->getCode(), $e);
        } catch (\Throwable $e) {
            $this->handleGenericException($e, $notifiable, $content, $data);
            throw new Exception("Email processing failed: " . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Cấu hình Mail driver từ settings
     */
    protected function configureMailFromSettings(): void
    {
        Config::set('mail.mailers.smtp', [
            'transport' => 'smtp',
            'host' => setting('email.smtp.host', 'localhost'),
            'port' => setting_int('email.smtp.port', 587),
            'encryption' => setting('email.smtp.encryption', 'tls'),
            'username' => setting('email.smtp.username', ''),
            'password' => setting('email.smtp.password', ''),
            'timeout' => setting_int('email.timeout', 30),
        ]);

        Config::set('mail.default', 'smtp');
    }

    /**
     * Xác định địa chỉ email người nhận
     */
    protected function resolveEmailAddress(array $notifiable): string
    {
        // Ưu tiên 1: Trường email trực tiếp
        if (isset($notifiable['email']) && filter_var($notifiable['email'], FILTER_VALIDATE_EMAIL)) {
            return $notifiable['email'];
        }

        throw new \InvalidArgumentException("No valid email address found for recipient");
    }

    /**
     * Xử lý lỗi gửi email
     */
    protected function handleTransportException(
        TransportExceptionInterface $e,
        array                       $notifiable,
        array                       $content,
        array                       $data
    ): void
    {
        $errorContext = [
            'error' => $e->getMessage(),
            'error_code' => $e->getCode(),
            'recipient' => $this->tryGetRecipientInfo($notifiable),
            'notification_type' => $data['notification_type'] ?? 'unknown',
        ];

        logger()->critical("Email transport failed", $errorContext);
        $this->logFailedNotification($notifiable, $content, $data, 'email', $e->getMessage(), $errorContext);
    }

    /**
     * Xử lý các ngoại lệ chung
     */
    protected function handleGenericException(
        \Throwable $e,
                   $notifiable,
        array      $content,
        array      $data
    ): void
    {
        $errorContext = [
            'error' => $e->getMessage(),
            'error_code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'recipient' => $this->tryGetRecipientInfo($notifiable),
            'notification_type' => $data['notification_type'] ?? 'unknown'
        ];

        logger()->error("Email channel error", $errorContext);
        $this->logFailedNotification($notifiable, $content, $data, 'email', $e->getMessage(), $errorContext);
    }

    /**
     * Lấy thông tin người nhận an toàn
     */
    protected function tryGetRecipientInfo(array $notifiable): array
    {
        try {
            return [
                'id' => $notifiable['id'] ?? null,
                'type' => User::class,
                'email' => $this->resolveEmailAddress($notifiable)
            ];
        } catch (\Throwable $e) {
            return ['error' => 'Failed to get recipient info: ' . $e->getMessage()];
        }
    }

    /**
     * Ghi log thông báo thất bại
     */
    protected function logFailedNotification(
        array  $notifiable,
        array  $content,
        array  $data,
        string $channel,
        string $error,
        array  $context
    ): void
    {
        logger()->error("Failed to log failed notification", [
            'error' => $error,
            'notification_data' => $data,
            'context' => $context,
            'notifiable' => $notifiable
        ]);
    }
}
