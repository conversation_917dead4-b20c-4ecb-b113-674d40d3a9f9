<?php

namespace Modules\Currency\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Setting\Models\Setting;
use Modules\Setting\Models\SettingGroup;

class CurrencySettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Currency setting group
        $currencyGroup = SettingGroup::updateOrCreate(
            ['key' => 'currency'],
            [
                'label' => 'Currency Settings',
                'description' => 'Currency and exchange rate configuration settings',
                'icon' => 'fas fa-coins',
                'sort_order' => 50,
            ]
        );

        // Currency settings
        $settings = [
            // Default Currency Settings
            [
                'key' => 'currency_default',
                'value' => 'USD',
                'type' => 'string',
                'input_type' => 'select',
                'label' => 'Default Currency',
                'description' => 'Default currency for the application',
                'options' => [
                    'USD' => 'US Dollar (USD)',
                    'EUR' => 'Euro (EUR)',
                    'GBP' => 'British Pound (GBP)',
                    'VND' => 'Vietnamese Dong (VND)',
                    'JPY' => 'Japanese Yen (JPY)',
                ],
                'validation_rules' => 'required|string|size:3',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'currency_decimal_digits',
                'value' => '2',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'Default Decimal Digits',
                'description' => 'Default number of decimal places for currency display',
                'validation_rules' => 'required|integer|min:0|max:8',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'currency_decimal_separator',
                'value' => '.',
                'type' => 'string',
                'input_type' => 'select',
                'label' => 'Decimal Separator',
                'description' => 'Character used to separate decimal places',
                'options' => [
                    '.' => 'Dot (.)',
                    ',' => 'Comma (,)',
                ],
                'validation_rules' => 'required|string|in:.,',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'currency_thousands_separator',
                'value' => ',',
                'type' => 'string',
                'input_type' => 'select',
                'label' => 'Thousands Separator',
                'description' => 'Character used to separate thousands',
                'options' => [
                    ',' => 'Comma (,)',
                    '.' => 'Dot (.)',
                    ' ' => 'Space ( )',
                    '' => 'None',
                ],
                'validation_rules' => 'nullable|string|in:,.,, ',
                'is_public' => true,
                'sort_order' => 4,
            ],

            // Exchange Rate API Settings
            [
                'key' => 'currency_api_default',
                'value' => 'freecurrency',
                'type' => 'string',
                'input_type' => 'select',
                'label' => 'Default Exchange Rate API',
                'description' => 'Default API provider for fetching exchange rates',
                'options' => [
                    'freecurrency' => 'FreeCurrencyAPI (Free)',
                    'exchangerate' => 'ExchangeRate-API (Freemium)',
                    'fixer' => 'Fixer.io (Paid)',
                    'currencylayer' => 'CurrencyLayer (Freemium)',
                    'mock' => 'Mock Data (Testing)',
                ],
                'validation_rules' => 'required|string|in:freecurrency,exchangerate,fixer,currencylayer,mock',
                'is_public' => false,
                'sort_order' => 10,
            ],
            [
                'key' => 'currency_fixer_api_key',
                'value' => '',
                'type' => 'string',
                'input_type' => 'password',
                'label' => 'Fixer.io API Key',
                'description' => 'API key for Fixer.io service (required for Fixer API)',
                'validation_rules' => 'nullable|string|max:255',
                'is_public' => false,
                'sort_order' => 11,
            ],
            [
                'key' => 'currency_currencylayer_api_key',
                'value' => '',
                'type' => 'string',
                'input_type' => 'password',
                'label' => 'CurrencyLayer API Key',
                'description' => 'API key for CurrencyLayer service (required for CurrencyLayer API)',
                'validation_rules' => 'nullable|string|max:255',
                'is_public' => false,
                'sort_order' => 12,
            ],
            [
                'key' => 'currency_freecurrency_api_key',
                'value' => 'fca_live_0gSS8pGGYNXqGMnKQNOsMoMxZHCf4uEliILF7gI9',
                'type' => 'string',
                'input_type' => 'password',
                'label' => 'FreeCurrencyAPI Key',
                'description' => 'API key for FreeCurrencyAPI service (free tier: 5000 requests/month)',
                'validation_rules' => 'nullable|string|max:255',
                'is_public' => false,
                'sort_order' => 13,
            ],
            [
                'key' => 'currency_api_timeout',
                'value' => '30',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'API Request Timeout',
                'description' => 'Timeout in seconds for external API requests',
                'validation_rules' => 'required|integer|min:5|max:120',
                'is_public' => false,
                'sort_order' => 14,
            ],

            // Auto Update Settings
            [
                'key' => 'currency_auto_update_enabled',
                'value' => 'false',
                'type' => 'boolean',
                'input_type' => 'switch',
                'label' => 'Enable Auto Update',
                'description' => 'Automatically update exchange rates on schedule',
                'validation_rules' => 'required|boolean',
                'is_public' => false,
                'sort_order' => 20,
            ],
            [
                'key' => 'currency_update_schedule',
                'value' => 'daily',
                'type' => 'string',
                'input_type' => 'select',
                'label' => 'Update Schedule',
                'description' => 'How often to automatically update exchange rates',
                'options' => [
                    'hourly' => 'Every Hour',
                    'daily' => 'Daily',
                    'weekly' => 'Weekly',
                ],
                'validation_rules' => 'required|string|in:hourly,daily,weekly',
                'is_public' => false,
                'sort_order' => 21,
            ],
            [
                'key' => 'currency_base_currency',
                'value' => 'USD',
                'type' => 'string',
                'input_type' => 'select',
                'label' => 'Base Currency for Updates',
                'description' => 'Base currency to use when fetching exchange rates',
                'options' => [
                    'USD' => 'US Dollar (USD)',
                    'EUR' => 'Euro (EUR)',
                    'GBP' => 'British Pound (GBP)',
                ],
                'validation_rules' => 'required|string|size:3',
                'is_public' => false,
                'sort_order' => 22,
            ],
            [
                'key' => 'currency_target_currencies',
                'value' => 'VND,EUR,GBP,JPY',
                'type' => 'string',
                'input_type' => 'text',
                'label' => 'Target Currencies',
                'description' => 'Comma-separated list of currencies to update (e.g., VND,EUR,GBP)',
                'validation_rules' => 'required|string|max:500',
                'is_public' => false,
                'sort_order' => 23,
            ],

            // Cache Settings
            [
                'key' => 'currency_cache_ttl',
                'value' => '300',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'Exchange Rate Cache TTL',
                'description' => 'Cache time-to-live in seconds for exchange rates (300 = 5 minutes)',
                'validation_rules' => 'required|integer|min:60|max:86400',
                'is_public' => false,
                'sort_order' => 30,
            ],
            [
                'key' => 'currency_list_cache_ttl',
                'value' => '3600',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'Currency List Cache TTL',
                'description' => 'Cache time-to-live in seconds for currency lists (3600 = 1 hour)',
                'validation_rules' => 'required|integer|min:300|max:86400',
                'is_public' => false,
                'sort_order' => 31,
            ],

            // Rate Freshness Settings
            [
                'key' => 'currency_fresh_threshold',
                'value' => '3600',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'Fresh Rate Threshold',
                'description' => 'Time in seconds when exchange rates are considered fresh (3600 = 1 hour)',
                'validation_rules' => 'required|integer|min:300|max:86400',
                'is_public' => false,
                'sort_order' => 40,
            ],
            [
                'key' => 'currency_stale_threshold',
                'value' => '86400',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'Stale Rate Threshold',
                'description' => 'Time in seconds when exchange rates are considered stale (86400 = 24 hours)',
                'validation_rules' => 'required|integer|min:3600|max:604800',
                'is_public' => false,
                'sort_order' => 41,
            ],

            // Validation Settings
            [
                'key' => 'currency_rate_min',
                'value' => '0.00000001',
                'type' => 'float',
                'input_type' => 'number',
                'label' => 'Minimum Exchange Rate',
                'description' => 'Minimum allowed value for exchange rates',
                'validation_rules' => 'required|numeric|min:0.00000001',
                'is_public' => false,
                'sort_order' => 50,
            ],
            [
                'key' => 'currency_rate_max',
                'value' => '999999999.99999999',
                'type' => 'float',
                'input_type' => 'number',
                'label' => 'Maximum Exchange Rate',
                'description' => 'Maximum allowed value for exchange rates',
                'validation_rules' => 'required|numeric|max:999999999.99999999',
                'is_public' => false,
                'sort_order' => 51,
            ],
            [
                'key' => 'currency_bulk_update_limit',
                'value' => '100',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'Bulk Update Limit',
                'description' => 'Maximum number of exchange rates that can be updated in one bulk operation',
                'validation_rules' => 'required|integer|min:1|max:1000',
                'is_public' => false,
                'sort_order' => 52,
            ],
        ];

        // Create or update each setting
        foreach ($settings as $settingData) {
            $settingData['group_id'] = $currencyGroup->id;
            
            Setting::updateOrCreate(
                ['key' => $settingData['key']],
                $settingData
            );
        }

        $this->command->info('Currency settings seeded successfully.');
    }
}
