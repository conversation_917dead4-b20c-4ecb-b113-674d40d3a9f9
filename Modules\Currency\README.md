# Currency Module

The Currency Module provides comprehensive multi-currency support for Laravel ProCMS, enabling currency management, exchange rate handling, amount conversion, and formatting with intelligent caching and API-first design.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [Database Schema](#database-schema)
- [Installation](#installation)
- [API Documentation](#api-documentation)
- [Usage Examples](#usage-examples)
- [Filtering](#filtering)
- [Multi-Currency Support](#multi-currency-support)
- [Settings Configuration](#settings-configuration)
- [Caching](#caching)
- [Testing](#testing)
- [Dependencies](#dependencies)
- [Contributing](#contributing)

## Overview

The Currency Module is a core component of Laravel ProCMS that handles multi-currency operations including currency management, exchange rate tracking, amount conversion, and formatting. It provides both public APIs for frontend consumption and administrative APIs for currency management.

**Architecture**: This module follows **Page Module patterns** and Laravel ProCMS architectural standards, ensuring consistency across the platform with modern testing practices and clean code organization.

### Key Components

- **Currency Model**: Core currency entity with formatting and validation
- **ExchangeRate Model**: Exchange rate tracking with automatic updates
- **CurrencyService**: Business logic with intelligent caching strategies
- **CurrencyController**: Public API for currency operations
- **AuthCurrencyController**: Administrative CRUD operations
- **CurrencyFacade**: Convenient access to currency functionality

## Features

### Core Features
- ✅ **Multi-Currency Support**: Support for unlimited currencies with ISO 4217 codes
- ✅ **Exchange Rate Management**: Real-time exchange rate tracking and conversion
- ✅ **Amount Formatting**: Locale-specific amount formatting with symbols
- ✅ **Default Currency**: Configurable default currency system
- ✅ **Currency Conversion**: Real-time amount conversion between currencies
- ✅ **Status Management**: Active/inactive currency status control

### Advanced Features
- ✅ **Intelligent Caching**: Redis-based caching with automatic invalidation
- ✅ **API-First Design**: Complete REST API with comprehensive endpoints
- ✅ **Bulk Operations**: Mass operations for efficient management
- ✅ **Advanced Filtering**: Search and filter across currency attributes
- ✅ **Soft Deletes**: Safe deletion with recovery capabilities
- ✅ **Comprehensive Testing**: 100% test coverage following Page Module patterns

### Exchange Rate Features
- ✅ **Multi-API Support**: FreeCurrencyAPI, ExchangeRate-API, Fixer.io, CurrencyLayer
- ✅ **Real-time Updates**: Automatic exchange rate fetching from external APIs
- ✅ **Rate Freshness Tracking**: Smart detection of stale exchange rates
- ✅ **Batch Rate Updates**: Efficient bulk exchange rate operations
- ✅ **Rate Validation**: Comprehensive validation with min/max limits
- ✅ **Reverse Rate Calculation**: Automatic bidirectional rate generation

### Management Features
- ✅ **CRUD Operations**: Complete Create, Read, Update, Delete functionality
- ✅ **Validation**: Comprehensive validation rules and error handling
- ✅ **Statistics**: Currency usage and exchange rate statistics
- ✅ **Sort Ordering**: Customizable currency display ordering

## Architecture

The Currency Module follows Laravel ProCMS architectural patterns:

```
Modules/Currency/
├── app/
│   ├── Facades/
│   │   └── CurrencyFacade.php          # Service facade
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── CurrencyController.php  # Public API
│   │   │   └── Auth/
│   │   │       └── CurrencyController.php # Admin CRUD
│   │   ├── Filters/
│   │   │   └── CurrencyFilter.php      # Advanced filtering
│   │   └── Requests/
│   │       ├── CurrencyRequest.php     # Currency validation
│   │       └── BulkCurrencyRequest.php # Bulk operation validation
│   ├── Models/
│   │   ├── Currency.php                # Main currency model
│   │   └── ExchangeRate.php            # Exchange rate model
│   ├── Providers/
│   │   ├── CurrencyServiceProvider.php # Service registration
│   │   └── RouteServiceProvider.php    # Route registration
│   ├── Services/
│   │   ├── CurrencyService.php         # Business logic
│   │   └── ExchangeRateService.php     # Exchange rate management
│   └── Console/
│       └── Commands/
│           └── UpdateExchangeRatesCommand.php # Exchange rate update command

├── database/
│   ├── factories/
│   │   ├── CurrencyFactory.php         # Currency factory
│   │   └── ExchangeRateFactory.php     # Exchange rate factory
│   ├── migrations/                     # Database migrations
│   └── seeders/
│       ├── CurrencySeeder.php          # Currency data seeder
│       ├── ExchangeRateSeeder.php      # Exchange rate seeder
│       ├── CurrencySettingsSeeder.php  # Settings seeder
│       └── CurrencyDatabaseSeeder.php  # Main seeder
├── routes/
│   ├── api.php                         # API routes
│   └── web.php                         # Web routes (API-only)
└── tests/
    ├── Feature/                        # Integration tests
    └── Unit/                           # Unit tests
```

## Database Schema

### Currencies Table
```sql
CREATE TABLE currencies (
    id BIGINT UNSIGNED PRIMARY KEY,
    code VARCHAR(3) UNIQUE NOT NULL,           -- ISO 4217 code (USD, EUR, VND)
    symbol VARCHAR(10) NOT NULL,               -- Currency symbol ($, €, ₫)
    name VARCHAR(50) NOT NULL,                 -- Currency name (US Dollar, Euro)
    decimal_digits INT DEFAULT 0,              -- Number of decimal places
    decimal_separator VARCHAR(5) DEFAULT '.',  -- Decimal separator (. or ,)
    thousands_separator VARCHAR(5) DEFAULT ',', -- Thousands separator (, or .)
    status VARCHAR(255) DEFAULT 'active',      -- Currency status
    sort_order INT DEFAULT 1,                  -- Display order
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

);
```

### Exchange Rates Table
```sql
CREATE TABLE exchange_rates (
    id BIGINT UNSIGNED PRIMARY KEY,
    base_currency VARCHAR(3) NOT NULL,         -- Base currency code
    target_currency VARCHAR(3) NOT NULL,       -- Target currency code
    rate DECIMAL(18,8) NOT NULL,               -- Exchange rate
    fetched_at TIMESTAMP NULL,                 -- Last update time
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    UNIQUE KEY unique_pair (base_currency, target_currency)
);
```

### Example Data Structure
```json
{
    "code": "USD",
    "symbol": "$",
    "name": "US Dollar",
    "decimal_digits": 2,
    "decimal_separator": ".",
    "thousands_separator": ",",
    "status": "active",
    "sort_order": 1
}
```

## Installation

The Currency Module is included in Laravel ProCMS by default. To manually install or reinstall:

### 1. Run Migrations
```bash
php artisan migrate --path=Modules/Currency/database/migrations
```

### 2. Seed Sample Data and Settings
```bash
# Seed currencies, exchange rates, and settings
php artisan db:seed --class=Modules\\Currency\\Database\\Seeders\\CurrencyDatabaseSeeder

# Or seed individually
php artisan db:seed --class=Modules\\Currency\\Database\\Seeders\\CurrencySeeder
php artisan db:seed --class=Modules\\Currency\\Database\\Seeders\\ExchangeRateSeeder
php artisan db:seed --class=Modules\\Currency\\Database\\Seeders\\CurrencySettingsSeeder
```

### 3. Register Service Provider
The module is auto-registered via Laravel's package discovery. Manual registration in `config/app.php`:

```php
'providers' => [
    // Other providers...
    Modules\Currency\Providers\CurrencyServiceProvider::class,
],
```

### 4. Register Facade (Optional)
```php
'aliases' => [
    // Other aliases...
    'CurrencyFacade' => Modules\Currency\Facades\CurrencyFacade::class,
],
```

## API Documentation

### Public API Endpoints

#### Get Active Currencies
```http
GET /api/v1/currencies
```

**Description**: Retrieve all active currencies.

**Response Structure**:
```json
{
    "success": true,
    "message": "Currencies retrieved successfully.",
    "data": [
        {
            "id": 1,
            "code": "USD",
            "symbol": "$",
            "name": "US Dollar",
            "is_default": true
        },
        {
            "id": 2,
            "code": "EUR",
            "symbol": "€",
            "name": "Euro",
            "is_default": false
        }
    ]
}
```

#### Get Currency Dropdown Data
```http
GET /api/v1/currencies/dropdown
```

**Description**: Get formatted currency data for dropdown components.

**Response Structure**:
```json
{
    "success": true,
    "message": "Currency dropdown data retrieved successfully.",
    "data": [
        {
            "value": "USD",
            "label": "US Dollar ($)",
            "symbol": "$",
            "is_default": true
        }
    ]
}
```

#### Get Exchange Rate
```http
GET /api/v1/currencies/exchange-rate/{from}/{to}
```

**Description**: Get current exchange rate between two currencies.

**Parameters**:
- `from` (string, required): Base currency code (e.g., 'USD')
- `to` (string, required): Target currency code (e.g., 'EUR')

**Example Request**:
```bash
curl -X GET "https://api.example.com/api/v1/currencies/exchange-rate/USD/EUR" \
     -H "Accept: application/json"
```

**Response Structure**:
```json
{
    "success": true,
    "message": "Exchange rate retrieved successfully.",
    "data": {
        "base_currency": "USD",
        "target_currency": "EUR",
        "rate": 0.85,
        "fetched_at": "2024-01-15T10:30:00Z",
        "is_fresh": true
    }
}
```

#### Get Default Currency
```http
GET /api/v1/currencies/default
```

**Description**: Retrieve the default currency.

**Response Structure**:
```json
{
    "success": true,
    "message": "Default currency retrieved successfully.",
    "data": {
        "id": 1,
        "code": "USD",
        "symbol": "$",
        "name": "US Dollar",
        "decimal_digits": 2,
        "decimal_separator": ".",
        "thousands_separator": ",",
        "is_default": true,
        "status": "active"
    }
}
```

#### Get Currency by Code
```http
GET /api/v1/currencies/{code}
```

**Description**: Retrieve a specific currency by its code.

**Parameters**:
- `code` (string, required): The currency code (e.g., 'USD', 'EUR')

**Example Request**:
```bash
curl -X GET "https://api.example.com/api/v1/currencies/USD" \
     -H "Accept: application/json"
```

#### Convert Amount
```http
POST /api/v1/currencies/convert
```

**Description**: Convert amount between two currencies.

**Request Body**:
```json
{
    "amount": 100,
    "from": "USD",
    "to": "EUR"
}
```

**Validation Rules**:
- `amount`: required, numeric, min:0
- `from`: required, string, size:3, exists in currencies table
- `to`: required, string, size:3, exists in currencies table

**Response Structure**:
```json
{
    "success": true,
    "message": "Amount converted successfully.",
    "data": {
        "original_amount": 100,
        "converted_amount": 85,
        "from_currency": "USD",
        "to_currency": "EUR",
        "exchange_rate": 0.85,
        "formatted_original": "$ 100.00",
        "formatted_converted": "€ 85,00"
    }
}
```

### Administrative API Endpoints

#### List Currencies (Admin)
```http
GET /api/v1/auth/currencies
```

**Description**: Retrieve paginated list of currencies with filtering options.

**Query Parameters**:
- `limit` (integer, optional): Number of items per page (default: 10)
- `page` (integer, optional): Page number (default: 1)
- `code` (string, optional): Filter by currency code
- `name` (string, optional): Filter by currency name
- `status` (string, optional): Filter by status (active/inactive)
- `sort_by` (string, optional): Sort field (code, name, status, sort_order, created_at)
- `sort_direction` (string, optional): Sort direction (asc/desc)

#### Create Currency
```http
POST /api/v1/auth/currencies
```

**Request Body**:
```json
{
    "code": "JPY",
    "symbol": "¥",
    "name": "Japanese Yen",
    "decimal_digits": 0,
    "decimal_separator": ".",
    "thousands_separator": ",",
    "status": "active",
    "sort_order": 5
}
```

**Validation Rules**:
- `code`: required, string, size:3, alpha, uppercase, unique
- `symbol`: required, string, max:10
- `name`: required, string, max:50, min:2
- `decimal_digits`: required, integer, min:0, max:8
- `decimal_separator`: required, string, max:5, in:.,
- `thousands_separator`: required, string, max:5, in:,., ,
- `status`: required, string, in:active,inactive

#### Update Currency
```http
PUT /api/v1/auth/currencies/{id}
```

**Description**: Update an existing currency.

#### Delete Currency
```http
DELETE /api/v1/auth/currencies/{id}
```

**Description**: Soft delete a currency.

#### Set Default Currency
```http
PATCH /api/v1/auth/currencies/{id}/set-default
```

**Description**: Set a currency as the default currency.

#### Toggle Currency Status
```http
PATCH /api/v1/auth/currencies/{id}/toggle-status
```

**Description**: Toggle currency status between active and inactive.

#### Bulk Delete Currencies
```http
DELETE /api/v1/auth/currencies/bulk-delete
```

**Request Body**:
```json
{
    "ids": [1, 2, 3]
}
```

### Exchange Rate Management API

#### Update Exchange Rate
```http
POST /api/v1/auth/currencies/exchange-rates/update
```

**Description**: Manually update exchange rate between two currencies.

**Request Body**:
```json
{
    "base_currency": "USD",
    "target_currency": "EUR",
    "rate": 0.85
}
```

**Validation Rules**:
- `base_currency`: required, string, size:3, exists in currencies table
- `target_currency`: required, string, size:3, exists in currencies table, different from base
- `rate`: required, numeric, min:0.00000001, max:999999999.99999999

#### Bulk Update Exchange Rates
```http
POST /api/v1/auth/currencies/exchange-rates/bulk-update
```

**Description**: Update multiple exchange rates in a single request.

**Request Body**:
```json
{
    "rates": [
        {
            "base_currency": "USD",
            "target_currency": "EUR",
            "rate": 0.85
        },
        {
            "base_currency": "USD",
            "target_currency": "VND",
            "rate": 23000
        }
    ]
}
```

#### Update Rates from API
```http
POST /api/v1/auth/currencies/exchange-rates/update-from-api
```

**Description**: Fetch and update exchange rates from external API.

**Request Body**:
```json
{
    "api": "freecurrency",
    "base_currency": "USD",
    "target_currencies": ["EUR", "VND", "GBP"],
    "force": false
}
```

**Query Parameters**:
- `api` (string, optional): API provider (freecurrency, exchangerate, fixer, currencylayer, mock)
- `base_currency` (string, optional): Base currency code (default: USD)
- `target_currencies` (array, optional): Target currencies (default: all active)
- `force` (boolean, optional): Force update even if rates are fresh (default: false)

**Response Structure**:
```json
{
    "success": true,
    "message": "Exchange rates updated successfully.",
    "data": {
        "updated_count": 3,
        "skipped_count": 0,
        "failed_count": 0,
        "rates": [
            {
                "base_currency": "USD",
                "target_currency": "EUR",
                "rate": 0.85,
                "fetched_at": "2024-01-15T10:30:00Z"
            }
        ]
    }
}
```

## Usage Examples

### Using the Currency Service

```php
use Modules\Currency\Services\CurrencyService;

// Resolve service from container
$currencyService = app(CurrencyService::class);

// Get active currencies
$currencies = $currencyService->getActiveCurrencies();

// Get default currency
$defaultCurrency = $currencyService->getDefaultCurrency();

// Find currency by code
$usdCurrency = $currencyService->findByCode('USD');

// Convert amount
$convertedAmount = $currencyService->convertAmount(100, 'USD', 'EUR');

// Format amount
$formattedAmount = $currencyService->formatAmount(1234.56, 'USD');
// Output: "$ 1,234.56"
```

### Using the Exchange Rate Service

```php
use Modules\Currency\Services\ExchangeRateService;

// Resolve service from container
$exchangeRateService = app(ExchangeRateService::class);

// Get exchange rate
$rate = $exchangeRateService->getExchangeRate('USD', 'EUR');

// Fetch rate from external API
$rate = $exchangeRateService->fetchExchangeRate('USD', 'EUR', 'freecurrency');

// Update rate manually
$exchangeRateService->updateExchangeRate('USD', 'EUR', 0.85);

// Bulk update rates from API
$results = $exchangeRateService->updateRatesFromAPI([
    'api' => 'freecurrency',
    'base_currency' => 'USD',
    'target_currencies' => ['EUR', 'VND', 'GBP']
]);

// Check if rate needs update
$needsUpdate = $exchangeRateService->needsUpdate('USD', 'EUR');

// Get rate freshness info
$info = $exchangeRateService->getRateFreshnessInfo('USD', 'EUR');
```

### Using the Currency Facade

```php
use Modules\Currency\Facades\CurrencyFacade;

// Get active currencies
$currencies = CurrencyFacade::getActiveCurrencies();

// Convert amount
$converted = CurrencyFacade::convertAmount(100, 'USD', 'EUR');

// Format amount
$formatted = CurrencyFacade::formatAmount(1000, 'VND');
// Output: "₫ 1,000"

// Get dropdown data
$dropdown = CurrencyFacade::getCurrenciesForDropdown();
```

### Working with Currency Models

```php
use Modules\Currency\Models\Currency;

// Create a new currency
$currency = Currency::create([
    'code' => 'GBP',
    'symbol' => '£',
    'name' => 'British Pound',
    'decimal_digits' => 2,
    'status' => 'active'
]);

// Get active currencies
$activeCurrencies = Currency::active()->ordered()->get();

// Get default currency
$defaultCurrency = Currency::default()->first();

// Format amount using model
$formatted = $currency->formatAmount(1234.56);
// Output: "£ 1,234.56"

// Check currency status
if ($currency->isActive()) {
    // Currency is active
}

if ($currency->isDefault()) {
    // Currency is default
}
```

### Exchange Rate Operations

```php
use Modules\Currency\Models\ExchangeRate;

// Get exchange rate
$rate = ExchangeRate::getRate('USD', 'EUR');

// Create/update exchange rate
$exchangeRate = ExchangeRate::updateOrCreate(
    ['base_currency' => 'USD', 'target_currency' => 'EUR'],
    ['rate' => 0.85, 'fetched_at' => now()]
);

// Convert amount using exchange rate
$convertedAmount = $exchangeRate->convertAmount(100);

// Check if rate is fresh
if ($exchangeRate->isFresh()) {
    // Rate is fresh (within threshold)
}

// Check if rate is stale
if ($exchangeRate->isStale()) {
    // Rate needs updating
}
```

### API Integration Examples

```php
use Modules\Currency\Services\ExchangeRateService;

$exchangeRateService = app(ExchangeRateService::class);

// Check API key configuration
if ($exchangeRateService->isApiKeyConfigured('freecurrency')) {
    // API key is configured
}

// Get supported API providers
$providers = ['freecurrency', 'exchangerate', 'fixer', 'currencylayer', 'mock'];

// Fetch rates from different APIs
$freeCurrencyRate = $exchangeRateService->fetchExchangeRate('USD', 'EUR', 'freecurrency');
$exchangeRateApiRate = $exchangeRateService->fetchExchangeRate('USD', 'EUR', 'exchangerate');
$fixerRate = $exchangeRateService->fetchExchangeRate('USD', 'EUR', 'fixer');

// Batch update with error handling
try {
    $results = $exchangeRateService->updateRatesFromAPI([
        'api' => 'freecurrency',
        'base_currency' => 'USD',
        'target_currencies' => ['EUR', 'VND', 'GBP', 'JPY'],
        'force' => false
    ]);

    echo "Updated: {$results['updated_count']} rates";
    echo "Skipped: {$results['skipped_count']} rates";
    echo "Failed: {$results['failed_count']} rates";
} catch (\Exception $e) {
    echo "Error updating rates: " . $e->getMessage();
}
```

## Filtering

The Currency Module provides comprehensive filtering capabilities:

### Available Filters

- `code`: Filter by currency code (partial match)
- `name`: Filter by currency name (partial match)
- `symbol`: Filter by currency symbol (partial match)
- `status`: Filter by status (active/inactive)
- `is_default`: Filter by default currency flag
- `decimal_digits`: Filter by decimal digits
- `sort_order_min/max`: Filter by sort order range
- `created_from/to`: Filter by creation date range
- `search`: Global search across code, name, and symbol

### Filter Usage Examples

```php
// Filter by status
GET /api/v1/auth/currencies?status=active

// Filter by currency code
GET /api/v1/auth/currencies?code=USD

// Global search
GET /api/v1/auth/currencies?search=dollar

// Combined filters
GET /api/v1/auth/currencies?status=active&decimal_digits=2&sort_by=name
```

## Multi-Currency Support

### Currency Configuration

Each currency supports comprehensive configuration through database settings managed by the Setting module:

```php
// Currency settings are managed through the Setting module
$defaultCurrency = setting('currency.currency_default', 'USD');
$apiProvider = setting('currency.currency_api_default', 'exchangerate');
$cacheTimeout = setting('currency.currency_cache_ttl', 300);

// Create currencies with full configuration
Currency::create([
    'code' => 'EUR',
    'symbol' => '€',
    'name' => 'Euro',
    'decimal_digits' => 2,
    'decimal_separator' => ',',    // European format
    'thousands_separator' => '.',  // European format
    'status' => 'active',
    'sort_order' => 2
]);
```

### Formatting Examples

```php
// US Dollar: $ 1,234.56
$usd = Currency::where('code', 'USD')->first();
echo $usd->formatAmount(1234.56);

// Euro: € 1.234,56
$eur = Currency::where('code', 'EUR')->first();
echo $eur->formatAmount(1234.56);

// Vietnamese Dong: ₫ 1,234
$vnd = Currency::where('code', 'VND')->first();
echo $vnd->formatAmount(1234.56);
```

### Exchange Rate Management

```php
// Update exchange rates
CurrencyFacade::updateExchangeRate('USD', 'EUR', 0.85);
CurrencyFacade::updateExchangeRate('USD', 'VND', 23000);

// Get current rates
$usdToEur = CurrencyFacade::getExchangeRate('USD', 'EUR');
$usdToVnd = CurrencyFacade::getExchangeRate('USD', 'VND');
```

## Exchange Rate Management

### Automatic Updates via Command Line

The Currency Module provides an Artisan command to automatically update exchange rates from external APIs:

```bash
# Update rates using FreeCurrencyAPI (default, free)
php artisan currency:update-rates --api=freecurrency

# Update rates using ExchangeRate-API (free)
php artisan currency:update-rates --api=exchangerate

# Update rates using Fixer.io (paid, requires API key)
php artisan currency:update-rates --api=fixer

# Update rates using CurrencyLayer (freemium, requires API key)
php artisan currency:update-rates --api=currencylayer

# Update with specific currencies
php artisan currency:update-rates --api=freecurrency --base=USD --targets=VND,EUR,GBP

# Force update even if rates are fresh
php artisan currency:update-rates --api=freecurrency --force
```

### Available API Providers

#### 1. FreeCurrencyAPI (Default - Free)
- **5000 requests/month** free tier
- **No registration required** for basic usage
- **Real-time rates** from multiple sources
- **168+ currencies** supported

#### 2. ExchangeRate-API (Free)
- **1500 requests/month** free tier
- **No API key required**
- **Daily updates**
- **168+ currencies** supported

#### 3. Fixer.io (Paid)
- **1000 requests/month** free tier
- **API key required**
- **Real-time rates**
- **170+ currencies** supported

#### 4. CurrencyLayer (Freemium)
- **1000 requests/month** free tier
- **API key required**
- **Hourly updates**
- **168+ currencies** supported

### Schedule Automatic Updates

Add to your `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule)
{
    // Update exchange rates daily at 9:00 AM using FreeCurrencyAPI
    $schedule->command('currency:update-rates --api=freecurrency')
        ->dailyAt('09:00')
        ->withoutOverlapping();

    // Or update hourly using a paid service
    $schedule->command('currency:update-rates --api=fixer')
        ->hourly()
        ->withoutOverlapping();
}
```

## Settings Configuration

The Currency Module uses database settings managed by the Setting Module instead of traditional config files. This provides dynamic configuration that can be changed through the admin interface without requiring file edits or server restarts.

### Available Settings

All currency configuration is stored in the `currency` settings group:

#### Default Currency Settings
- `currency_default`: Default currency code (USD)
- `currency_decimal_digits`: Default decimal places (2)
- `currency_decimal_separator`: Decimal separator (.)
- `currency_thousands_separator`: Thousands separator (,)

#### Exchange Rate API Settings
- `currency_api_default`: Default API provider (freecurrency)
- `currency_freecurrency_api_key`: FreeCurrencyAPI key (free tier: 5000 requests/month)
- `currency_fixer_api_key`: Fixer.io API key
- `currency_currencylayer_api_key`: CurrencyLayer API key
- `currency_api_timeout`: API request timeout (30 seconds)

#### Auto Update Settings
- `currency_auto_update_enabled`: Enable automatic updates (false)
- `currency_update_schedule`: Update frequency (daily)
- `currency_base_currency`: Base currency for updates (USD)
- `currency_target_currencies`: Target currencies (VND,EUR,GBP,JPY)

#### Cache Settings
- `currency_cache_ttl`: Exchange rate cache TTL (300 seconds)
- `currency_list_cache_ttl`: Currency list cache TTL (3600 seconds)

#### Rate Freshness Settings
- `currency_fresh_threshold`: Fresh rate threshold (3600 seconds)
- `currency_stale_threshold`: Stale rate threshold (86400 seconds)

#### Validation Settings
- `currency_rate_min`: Minimum exchange rate (0.00000001)
- `currency_rate_max`: Maximum exchange rate (999999999.99999999)
- `currency_bulk_update_limit`: Bulk update limit (100)

### Using Settings in Code

```php
// Get settings using the helper function
$defaultCurrency = setting('currency.currency_default', 'USD');
$apiProvider = setting('currency.currency_api_default', 'exchangerate');
$cacheTimeout = setting('currency.currency_cache_ttl', 300);

// Settings are automatically used by services
$currencyService = app(CurrencyService::class);
$defaultCurrency = $currencyService->getDefaultCurrency(); // Uses setting

// Exchange rate service uses API settings
$exchangeRateService = app(ExchangeRateService::class);
$rates = $exchangeRateService->updateRatesFromAPI(); // Uses API settings
```

### Managing Settings

Settings can be managed through:

1. **Admin API**: Update settings via REST API
2. **Setting Module Interface**: Web-based admin interface
3. **Database**: Direct database manipulation
4. **Seeders**: Programmatic setup during deployment

```php
// Update settings via Setting Facade
SettingFacade::updateSetting('currency', 'currency_default', 'EUR');

// Bulk update settings
SettingFacade::updateSettings([
    'currency.currency_api_default' => 'fixer',
    'currency.currency_fixer_api_key' => 'your-api-key'
]);
```

## Caching

The Currency Module implements intelligent caching strategies:

### Cache Keys and TTL

- `proCMS.currencies.active.list` - Active currencies (60 minutes)
- `proCMS.currencies.default` - Default currency (60 minutes)
- `proCMS.currencies.codes` - Currency codes (60 minutes)
- `proCMS.currencies.code.{code}` - Individual currency (60 minutes)
- `proCMS.exchange_rates.{from}.{to}` - Exchange rates (5 minutes)
- `proCMS.exchange_rates.freshness.{from}.{to}` - Rate freshness info (5 minutes)

### Cache Management

```php
// Clear all currency cache
CurrencyFacade::clearCache();

// Clear exchange rate cache
$currencyService->clearExchangeRateCache();

// Manual cache operations
Cache::forget('proCMS.currencies.active.list');
```

## Testing

The Currency Module includes comprehensive test coverage following Page Module patterns and Laravel ProCMS testing standards:

### Test Architecture

The module follows **Page Module testing patterns** with:
- ✅ **PHPUnit Attributes**: Modern `#[Test]` syntax instead of `@test` annotations
- ✅ **Direct TestCase Extension**: Uses `Tests\TestCase` directly like Page Module
- ✅ **RefreshDatabase Trait**: Clean database state for each test
- ✅ **Self-contained Tests**: Each test creates its own data without complex setUp
- ✅ **100% Coverage Requirement**: Comprehensive coverage across all components

### Test Structure

```
tests/
├── Feature/
│   ├── CurrencyApiTest.php                 # Public API endpoints
│   ├── AuthCurrencyControllerTest.php      # Admin API endpoints
│   └── UpdateExchangeRatesCommandTest.php  # Console command tests
└── Unit/
    ├── Models/
    │   ├── CurrencyTest.php                # Currency model tests
    │   └── ExchangeRateTest.php            # Exchange rate model tests
    ├── Services/
    │   ├── CurrencyServiceTest.php         # Currency service tests
    │   └── ExchangeRateServiceTest.php     # Exchange rate service tests
    ├── Requests/
    │   ├── CurrencyRequestTest.php         # Form validation tests
    │   └── BulkCurrencyRequestTest.php     # Bulk validation tests
    └── Factories/
        ├── CurrencyFactoryTest.php         # Currency factory tests
        └── ExchangeRateFactoryTest.php     # Exchange rate factory tests
```

### Test Quality Standards

#### **Modern PHPUnit Patterns**
```php
#[Test]
public function it_can_create_currency()
{
    $currency = Currency::factory()->create([
        'code' => 'USD',
        'symbol' => '$',
        'name' => 'US Dollar',
        'status' => 'active'
    ]);

    $this->assertInstanceOf(Currency::class, $currency);
    $this->assertEquals('USD', $currency->code);
}
```

#### **API Testing Patterns**
```php
#[Test]
public function it_can_get_active_currencies()
{
    Currency::factory()->create(['code' => 'USD', 'status' => 'active']);
    Currency::factory()->create(['code' => 'EUR', 'status' => 'active']);

    $response = $this->getJson('/api/v1/currencies');

    $response->assertStatus(200);
    $response->assertJsonStructure([
        'success',
        'message',
        'data' => [
            '*' => ['code', 'symbol', 'name']
        ]
    ]);
}
```

### Running Tests

```bash
# Run all Currency module tests (following Page Module patterns)
php artisan test --testsuite=Currency

# Run with coverage and enforce 100% requirement
php artisan test --testsuite=Currency --coverage --min=100

# Run specific test types
php artisan test Modules/Currency/tests/Unit/
php artisan test Modules/Currency/tests/Feature/

# Run specific test files
php artisan test Modules/Currency/tests/Unit/Models/CurrencyTest.php
php artisan test Modules/Currency/tests/Feature/CurrencyApiTest.php

# Run with parallel processing
php artisan test --testsuite=Currency --parallel

# Run with verbose output for debugging
php artisan test --testsuite=Currency --verbose
```

### Test Coverage

The module maintains **100% test coverage** across:

- ✅ **Models**: All methods, relationships, scopes, and edge cases
- ✅ **Services**: Business logic, caching, validation, and error handling
- ✅ **Controllers**: All endpoints, validation, responses, and error scenarios
- ✅ **Commands**: All options, success/failure paths, and edge cases
- ✅ **Requests**: Validation rules, error messages, and authorization
- ✅ **Factories**: Data generation and state variations

### Test Data Management

Following Page Module patterns:
- **Factory-based**: All test data generated via factories
- **Isolated**: Each test creates its own data
- **Realistic**: Test data reflects real-world scenarios
- **Clean**: Database refreshed between tests

## Dependencies

### Core Dependencies
- **Laravel Framework**: ^10.0 || ^11.0
- **PHP**: ^8.1
- **MySQL/PostgreSQL**: Database support

### Module Dependencies
- **Core Module**: For base request classes and traits
- **Setting Module**: For configuration management

### External API Dependencies
- **FreeCurrencyAPI**: Free tier (5000 requests/month)
- **ExchangeRate-API**: Free tier (1500 requests/month)
- **Fixer.io**: Paid service (1000 requests/month free)
- **CurrencyLayer**: Freemium (1000 requests/month free)

### Development Dependencies
- **PHPUnit**: Testing framework
- **Laravel Testing**: Feature and unit testing
- **Faker**: Test data generation
- **HTTP Client**: For API testing and mocking

## Troubleshooting

### Common Issues

#### Exchange Rate API Issues

**Problem**: API requests failing with authentication errors
```
Solution: Check API key configuration in settings
```

```php
// Check if API key is configured
$exchangeRateService = app(ExchangeRateService::class);
if (!$exchangeRateService->isApiKeyConfigured('freecurrency')) {
    // Configure API key in settings
    SettingFacade::updateSetting('currency', 'currency_freecurrency_api_key', 'your-api-key');
}
```

**Problem**: Exchange rates not updating automatically
```
Solution: Check scheduled task configuration
```

```php
// In app/Console/Kernel.php
protected function schedule(Schedule $schedule)
{
    $schedule->command('currency:update-rates --api=freecurrency')
        ->dailyAt('09:00')
        ->withoutOverlapping();
}
```

**Problem**: Stale exchange rates being used
```
Solution: Check freshness thresholds in settings
```

```php
// Check rate freshness
$info = $exchangeRateService->getRateFreshnessInfo('USD', 'EUR');
if ($info['is_stale']) {
    // Force update
    $exchangeRateService->updateRatesFromAPI(['force' => true]);
}
```

#### Cache Issues

**Problem**: Currency data not updating after changes
```
Solution: Clear currency cache
```

```php
// Clear all currency cache
CurrencyFacade::clearCache();

// Or clear specific cache keys
Cache::forget('proCMS.currencies.active.list');
Cache::forget('proCMS.currencies.default');
```

#### Performance Issues

**Problem**: Slow currency conversion operations
```
Solution: Optimize caching and database queries
```

```php
// Use cached exchange rates
$rate = Cache::remember("exchange_rate.USD.EUR", 300, function () {
    return ExchangeRate::getRate('USD', 'EUR');
});

// Preload currencies for bulk operations
$currencies = Currency::active()->get()->keyBy('code');
```

### Best Practices

#### API Usage
- **Use FreeCurrencyAPI** for development and small applications (free tier)
- **Implement rate limiting** to avoid exceeding API quotas
- **Cache exchange rates** appropriately based on your update frequency needs
- **Handle API failures gracefully** with fallback mechanisms

#### Performance Optimization
- **Enable Redis caching** for production environments
- **Use bulk operations** for updating multiple exchange rates
- **Implement proper indexing** on exchange_rates table
- **Monitor API usage** to avoid quota limits

#### Security
- **Store API keys securely** in database settings, not config files
- **Validate all currency codes** before API calls
- **Implement rate limiting** on public API endpoints
- **Use HTTPS** for all external API communications

## Contributing

We welcome contributions to the Currency Module! Please follow Laravel ProCMS standards and Page Module patterns:

### Development Setup
1. Clone the repository
2. Install dependencies: `composer install`
3. Run migrations: `php artisan migrate`
4. Seed data: `php artisan db:seed --class=Modules\\Currency\\Database\\Seeders\\CurrencyDatabaseSeeder`
5. Run tests: `php artisan test --testsuite=Currency`

### Coding Standards
- Follow **PSR-12** coding standards
- Use **Page Module patterns** for consistency
- Use **PHPUnit attributes** (`#[Test]`) instead of annotations
- Extend `Tests\TestCase` directly (not custom test cases)
- Use `RefreshDatabase` trait for test isolation
- Write **self-contained tests** without complex setUp methods
- Add comprehensive docblocks
- Maintain **100% test coverage** requirement

### Testing Requirements
- **100% Coverage**: All new code must be fully tested
- **Page Module Patterns**: Follow established testing conventions
- **Modern PHPUnit**: Use attributes and current best practices
- **Realistic Data**: Use factories for test data generation
- **Edge Cases**: Test both success and failure scenarios

### Pull Request Process
1. **Create feature branch**: `git checkout -b feature/new-feature`
2. **Follow Page Module patterns**: Ensure consistency with existing modules
3. **Write comprehensive tests**: 100% coverage with realistic scenarios
4. **Run full test suite**: `php artisan test --testsuite=Currency --coverage --min=100`
5. **Update documentation**: Update README and API docs if needed
6. **Submit pull request**: With clear description and test evidence

### Code Review Checklist
- ✅ Follows Page Module architectural patterns
- ✅ Uses modern PHPUnit attributes and practices
- ✅ Maintains 100% test coverage
- ✅ Includes both unit and feature tests
- ✅ Tests cover edge cases and error scenarios
- ✅ Documentation is updated and accurate
- ✅ No breaking changes without proper versioning

---

For more information about Laravel ProCMS architecture and patterns, see the main project documentation.
