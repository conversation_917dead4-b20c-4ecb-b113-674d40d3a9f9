<?php

namespace Modules\Language\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Modules\Core\Traits\ResponseTrait;
use Modules\Language\Http\Filters\LanguageFilter;
use Modules\Language\Models\Language;
use Modules\Language\Http\Requests\LanguageRequest;
use Modules\Language\Http\Requests\BulkLanguageRequest;
use Modules\Language\Http\Requests\BulkLanguageDestroyRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LanguageController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|language.view')->only(['index', 'show', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|language.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|language.edit')->only(['update', 'setDefault']);
        $this->middleware('role_or_permission:super-admin|language.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|language.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $languages = Language::query()
            ->filter(new LanguageFilter($request))
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($languages, __('Languages retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(LanguageRequest $request): JsonResponse
    {
        $language = Language::create($request->all());

        return $this->successResponse($language, __('Language created successfully.'), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Language $language): JsonResponse
    {
        return $this->successResponse($language, __('Language retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     * @param LanguageRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(LanguageRequest $request, int $id): JsonResponse
    {
        $language = Language::find($id);
        if (!$language) {
            return $this->errorResponse(null, __('Language not found.'), 404);
        }
        $language->update($request->all());

        return $this->successResponse($language->fresh(), __('Language updated successfully.'));
    }

    /**
     * Soft delete the specified resource from storage.
     */
    public function delete(int $id): JsonResponse
    {
        $language = Language::findOrFail($id);

        // Prevent deletion of default language
        if ($language->is_default) {
            return $this->errorResponse($language->id, __('Cannot delete the default language.'), 422);
        }

        $language->delete();

        return $this->successResponse($language->id, __('Language deleted successfully.'));
    }

    /**
     * Bulk soft delete multiple resources.
     */
    public function bulkDelete(BulkLanguageRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');

        // Check if any of the selected languages is the default language
        $defaultLanguage = Language::query()->whereIn('id', $ids)->where('is_default', 1)->first();
        if ($defaultLanguage) {
            return $this->errorResponse($defaultLanguage, __('Cannot delete the default language.'), 422);
        }

        $deletedCount = Language::query()->whereIn('id', $ids)->delete();

        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count languages deleted successfully.', ['count' => $deletedCount])
        );
    }

    /**
     * Restore the specified soft-deleted resource.
     */
    public function restore(int $id): JsonResponse
    {
        $language = Language::onlyTrashed()->findOrFail($id);
        $language->restore();

        return $this->successResponse($language->fresh(), __('Language restored successfully.'));
    }

    /**
     * Bulk restore multiple soft-deleted resources.
     */
    public function bulkRestore(BulkLanguageRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');
        $restoredCount = Language::onlyTrashed()->whereIn('id', $ids)->restore();

        return $this->successResponse(
            ['restored_count' => $restoredCount],
            __(':count languages restored successfully.', ['count' => $restoredCount])
        );
    }

    /**
     * Permanently delete the specified resource.
     */
    public function destroy(int $id): JsonResponse
    {
        $language = Language::onlyTrashed()->findOrFail($id);
        $language->forceDelete();

        return $this->successResponse([$id], __('Language permanently deleted.'));
    }

    /**
     * Bulk permanently delete multiple resources.
     */
    public function bulkDestroy(BulkLanguageDestroyRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');

        $deletedCount = Language::onlyTrashed()->whereIn('id', $ids)->forceDelete();
        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count languages permanently deleted.', ['count' => $deletedCount])
        );
    }

    public function dropdown(): JsonResponse
    {
        $languages = Language::query()->active()->get();
        return $this->successResponse($languages->makeHidden(['id', 'updated_at', 'created_at', 'status']), __('Languages retrieved successfully.'));
    }
}
