<?php

namespace Modules\ModelAI\Observers;

use Illuminate\Support\Facades\Cache;
use Modules\ModelAI\Models\ModelAITool;

class ModelAIToolObserver
{
    /**
     * Cache tags for model AI tools
     */
    private const CACHE_TAGS = ['model-tools', 'model-ai'];

    /**
     * Handle the ModelAITool "created" event.
     */
    public function created(ModelAITool $modelAITool): void
    {
        $this->clearCache($modelAITool);
    }

    /**
     * Handle the ModelAITool "updated" event.
     */
    public function updated(ModelAITool $modelAITool): void
    {
        $this->clearCache($modelAITool);
    }

    /**
     * Handle the ModelAITool "deleted" event.
     */
    public function deleted(ModelAITool $modelAITool): void
    {
        $this->clearCache($modelAITool);
    }

    /**
     * Clear model AI tool related cache.
     */
    private function clearCache(ModelAITool $modelAITool): void
    {
        try {
            if (function_exists('enabledCache') && enabledCache()) {
                Cache::tags(self::CACHE_TAGS)->flush();
                
                // Clear specific model cache
                if ($modelAITool->model_ai_id) {
                    Cache::forget("modelai.model.{$modelAITool->model_ai_id}.available_tools");
                }
            }
        } catch (\Exception $e) {
            // Silently handle cache errors
        }
    }
}
