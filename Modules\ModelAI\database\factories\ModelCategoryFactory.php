<?php

namespace Modules\ModelAI\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\ModelAI\Models\ModelCategory;

/**
 * @extends Factory<ModelCategory>
 */
class ModelCategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = ModelCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $baseCategories = [
            'text-generation',
            'image-creation',
            'code-generation',
            'data-analysis',
            'translation',
            'summarization',
            'question-answering',
            'content-writing',
        ];

        // Generate unique key by combining base category with random suffix
        $baseKey = $this->faker->randomElement($baseCategories);
        $key = $baseKey . '-' . $this->faker->unique()->numberBetween(1000, 9999);

        // Generate name based on key
        $name = ucwords(str_replace('-', ' ', $baseKey));

        return [
            'key' => $key,
            'name' => $name,
            'description' => $this->faker->sentence(10),
            'icon' => $this->faker->randomElement(['🤖', '🎨', '💻', '📊', '🌐', '📝', '❓', '✍️']),
            'color' => $this->faker->hexColor(),
            'sort_order' => $this->faker->numberBetween(1, 100),
            'status' => $this->faker->randomElement(['active', 'inactive', 'draft']),
        ];
    }

    /**
     * Indicate that the category is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the category is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the category is draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
        ]);
    }
}
