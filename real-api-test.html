<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real API Widget Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .hero {
            text-align: center;
            padding: 40px 0;
        }
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .hero p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        .config-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .config-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .form-group label {
            font-weight: 600;
            font-size: 14px;
        }
        .form-group input, .form-group select {
            padding: 12px 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }
        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            margin-top: 20px;
        }
        .widget-container {
            width: 400px;
            height: 600px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            background: white;
            overflow: hidden;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .btn.primary {
            background: #3b82f6;
            border-color: #3b82f6;
        }
        .btn.primary:hover {
            background: #2563eb;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }
        .status.success { 
            background: rgba(34, 197, 94, 0.2); 
            border: 1px solid rgba(34, 197, 94, 0.4);
        }
        .status.info { 
            background: rgba(59, 130, 246, 0.2); 
            border: 1px solid rgba(59, 130, 246, 0.4);
        }
        .status.error { 
            background: rgba(239, 68, 68, 0.2); 
            border: 1px solid rgba(239, 68, 68, 0.4);
        }
        .api-info {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .api-info h4 {
            margin-top: 0;
            color: #fbbf24;
        }
        .api-info code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        .floating-demo {
            text-align: center;
            padding: 40px 20px;
        }
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            .config-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1>🔗 Real API Widget Test</h1>
            <p>Test widget với real backend API integration</p>
        </div>

        <!-- Configuration Section -->
        <div class="config-section">
            <h3>⚙️ Widget Configuration</h3>
            <div class="config-grid">
                <div class="form-group">
                    <label for="botUuid">Bot UUID:</label>
                    <input type="text" id="botUuid" placeholder="Enter bot UUID" value="">
                </div>
                <div class="form-group">
                    <label for="apiKey">API Key:</label>
                    <input type="text" id="apiKey" placeholder="pk_live_..." value="">
                </div>
                <div class="form-group">
                    <label for="apiBaseUrl">API Base URL:</label>
                    <input type="text" id="apiBaseUrl" placeholder="https://your-domain.com/api/v1/widget" value="">
                </div>
                <div class="form-group">
                    <label for="userId">User ID (Optional):</label>
                    <input type="text" id="userId" placeholder="user_123" value="">
                </div>
            </div>
            
            <div class="controls">
                <button class="btn primary" onclick="loadWidget()">Load Widget</button>
                <button class="btn" onclick="testConnection()">Test Connection</button>
                <button class="btn" onclick="clearWidget()">Clear Widget</button>
                <button class="btn" onclick="createFloating()">Create Floating</button>
            </div>
            
            <div id="status" class="status" style="display: none;"></div>
        </div>

        <!-- API Information -->
        <div class="api-info">
            <h4>📋 API Endpoints Used:</h4>
            <ul>
                <li><code>GET /widget/health</code> - Health check</li>
                <li><code>GET /widget/bot/{uuid}/validate</code> - Validate access</li>
                <li><code>GET /widget/bot/{uuid}/config</code> - Get bot config</li>
                <li><code>POST /widget/conversations</code> - Create conversation</li>
                <li><code>GET /widget/conversations/{uuid}/messages</code> - Get messages</li>
                <li><code>POST /widget/conversations/{uuid}/messages</code> - Send message</li>
            </ul>
            
            <h4>🔐 Authentication:</h4>
            <p>Uses <code>Authorization: Bearer {apiKey}</code> header for API authentication.</p>
        </div>

        <!-- Demo Section -->
        <div class="demo-grid">
            <div>
                <h3>📱 Embedded Widget Demo</h3>
                <p>Widget được embed trực tiếp vào page với real API calls.</p>
                
                <div id="widget-container" class="widget-container"></div>
            </div>
            
            <div class="floating-demo">
                <h3>🎈 Floating Widget</h3>
                <p>Click "Create Floating" để tạo floating widget ở góc màn hình.</p>
                
                <div style="
                    background: rgba(255, 255, 255, 0.1);
                    border: 2px dashed rgba(255, 255, 255, 0.3);
                    border-radius: 12px;
                    padding: 60px 20px;
                    margin: 20px 0;
                ">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">💬</div>
                    <div>Floating widget sẽ xuất hiện ở góc bottom-right</div>
                </div>
                
                <div id="floating-status" class="status" style="display: none;"></div>
            </div>
        </div>

        <!-- Sample Configuration -->
        <div class="config-section">
            <h3>📝 Sample Configuration</h3>
            <p>Để test với bot thật, bạn cần:</p>
            <ol>
                <li>Tạo bot trong admin panel</li>
                <li>Copy Bot UUID từ bot settings</li>
                <li>Copy API Key từ bot settings</li>
                <li>Đảm bảo bot status = "active"</li>
                <li>Nhập thông tin vào form trên và click "Load Widget"</li>
            </ol>
            
            <div class="controls">
                <button class="btn" onclick="fillSampleConfig()">Fill Sample Config</button>
                <button class="btn" onclick="openAdminPanel()">Open Admin Panel</button>
            </div>
        </div>
    </div>

    <!-- Load Widget Library -->
    <script src="dist/widget/procms-chatbot.umd.js"></script>
    <script>
        let widget = null;
        let floatingWidget = null;

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            console.log(`[Real API Test] ${message}`);
        }

        function showFloatingStatus(message, type = 'info') {
            const statusEl = document.getElementById('floating-status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
        }

        function getConfig() {
            return {
                botUuid: document.getElementById('botUuid').value.trim(),
                apiKey: document.getElementById('apiKey').value.trim(),
                apiBaseUrl: document.getElementById('apiBaseUrl').value.trim() || undefined,
                userId: document.getElementById('userId').value.trim() || undefined,
                theme: 'light'
            };
        }

        async function testConnection() {
            const config = getConfig();
            
            if (!config.botUuid || !config.apiKey) {
                showStatus('Please enter Bot UUID and API Key', 'error');
                return;
            }

            showStatus('Testing connection...', 'info');

            try {
                const baseUrl = config.apiBaseUrl || window.location.origin + '/api/v1/widget';
                
                // Test health endpoint
                const healthResponse = await fetch(`${baseUrl}/health`);
                if (!healthResponse.ok) {
                    throw new Error('Health check failed');
                }

                // Test bot validation
                const validateResponse = await fetch(`${baseUrl}/bot/${config.botUuid}/validate`, {
                    headers: {
                        'Authorization': `Bearer ${config.apiKey}`,
                        'Accept': 'application/json'
                    }
                });

                if (!validateResponse.ok) {
                    const errorData = await validateResponse.json();
                    throw new Error(errorData.message || 'Bot validation failed');
                }

                const validateData = await validateResponse.json();
                showStatus(`✅ Connection successful! Bot: ${validateData.data?.name || 'Unknown'}`, 'success');

            } catch (error) {
                showStatus(`❌ Connection failed: ${error.message}`, 'error');
                console.error('Connection test failed:', error);
            }
        }

        async function loadWidget() {
            const config = getConfig();
            
            if (!config.botUuid || !config.apiKey) {
                showStatus('Please enter Bot UUID and API Key', 'error');
                return;
            }

            try {
                if (widget) {
                    widget.unmount();
                }

                showStatus('Loading widget with real API...', 'info');

                widget = await ProcmsChatbot.create({
                    ...config,
                    onReady: () => {
                        showStatus('✅ Widget loaded successfully! You can now chat.', 'success');
                    },
                    onMessage: (message) => {
                        console.log('Message event:', message);
                    },
                    onError: (error) => {
                        showStatus(`Widget error: ${error.message}`, 'error');
                    }
                }, '#widget-container');

            } catch (error) {
                showStatus(`❌ Failed to load widget: ${error.message}`, 'error');
                console.error('Widget load error:', error);
            }
        }

        function clearWidget() {
            if (widget) {
                widget.unmount();
                widget = null;
                showStatus('Widget cleared', 'info');
            }
        }

        async function createFloating() {
            const config = getConfig();
            
            if (!config.botUuid || !config.apiKey) {
                showFloatingStatus('Please enter Bot UUID and API Key', 'error');
                return;
            }

            try {
                if (floatingWidget) {
                    floatingWidget.unmount();
                    const container = document.getElementById('procms-floating-widget');
                    if (container) container.remove();
                }

                showFloatingStatus('Creating floating widget...', 'info');

                floatingWidget = await ProcmsChatbot.createFloatingWidget({
                    ...config,
                    theme: 'dark',
                    onReady: () => {
                        showFloatingStatus('✅ Floating widget created! Check bottom-right corner.', 'success');
                    },
                    onError: (error) => {
                        showFloatingStatus(`Error: ${error.message}`, 'error');
                    }
                });

            } catch (error) {
                showFloatingStatus(`❌ Failed to create floating widget: ${error.message}`, 'error');
                console.error('Floating widget error:', error);
            }
        }

        function fillSampleConfig() {
            // Fill with sample values for testing
            document.getElementById('botUuid').value = 'sample-bot-uuid-123';
            document.getElementById('apiKey').value = 'pk_live_sample_api_key_123';
            document.getElementById('apiBaseUrl').value = window.location.origin + '/api/v1/widget';
            document.getElementById('userId').value = 'test_user_123';
            
            showStatus('Sample configuration filled. Update with real values to test.', 'info');
        }

        function openAdminPanel() {
            window.open('/admin/chatbot/bots', '_blank');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Real API Widget Test loaded');
            
            // Auto-fill API base URL
            document.getElementById('apiBaseUrl').value = window.location.origin + '/api/v1/widget';
            
            // Check if widget library is loaded
            if (typeof ProcmsChatbotWidget !== 'undefined') {
                console.log('✅ Real API widget library loaded successfully');
                showStatus('Widget library loaded. Enter bot configuration to test.', 'info');
            } else {
                console.error('❌ Widget library failed to load');
                showStatus('❌ Widget library failed to load', 'error');
            }
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (widget) widget.unmount();
            if (floatingWidget) floatingWidget.unmount();
        });
    </script>
</body>
</html>
