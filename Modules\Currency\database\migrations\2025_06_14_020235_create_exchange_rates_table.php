<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exchange_rates', function (Blueprint $table) {
            $table->id();
            $table->string('base_currency', 3)->comment('Base currency code (ISO 4217)');
            $table->string('target_currency', 3)->comment('Target currency code (ISO 4217)');
            $table->decimal('rate', 18, 8)->comment('Exchange rate from base to target currency');
            $table->timestamp('fetched_at')->nullable()->comment('When the rate was last fetched from external API');
            $table->timestamps();

            // Indexes
            $table->unique(['base_currency', 'target_currency'], 'unique_currency_pair');
            $table->index(['base_currency'], 'idx_base_currency');
            $table->index(['target_currency'], 'idx_target_currency');
            $table->index(['fetched_at'], 'idx_fetched_at');

            // Foreign key constraints
            $table->foreign('base_currency')->references('code')->on('currencies')->onDelete('cascade');
            $table->foreign('target_currency')->references('code')->on('currencies')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exchange_rates');
    }
};
