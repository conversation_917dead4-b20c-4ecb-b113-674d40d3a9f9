<?php

namespace Modules\Location\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Location\Database\Factories\CountryFactory;


class Country extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'native_name',
        'iso_code_2',
        'iso_code_3',
        'iso_numeric',
        'phone_code',
        'region',
        'subregion',
        'latitude',
        'longitude',
        'emoji',
        'emoji_unicode',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = ['deleted_at'];



    /**
     * Scope a query to only include active countries.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by region.
     */
    public function scopeRegion(Builder $query, string $region): Builder
    {
        return $query->where('region', $region);
    }

    /**
     * Get all geographic divisions for this country.
     */
    public function geoDivisions(): HasMany
    {
        return $this->hasMany(GeoDivision::class);
    }

    /**
     * Get all states/provinces for this country.
     */
    public function states(): HasMany
    {
        return $this->hasMany(GeoDivision::class)
            ->where('type', 'state')
            ->where('level', 1)
            ->whereNull('parent_id');
    }

    /**
     * Get all cities for this country.
     */
    public function cities(): HasMany
    {
        return $this->hasMany(GeoDivision::class)
            ->where('type', 'city');
    }

    /**
     * Get all districts for this country.
     */
    public function districts(): HasMany
    {
        return $this->hasMany(GeoDivision::class)
            ->where('type', 'district');
    }



    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'iso_code_2';
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): CountryFactory
    {
        return CountryFactory::new();
    }
}
