<?php

namespace Modules\Currency\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Currency\Models\Currency;
use Modules\Currency\Models\ExchangeRate;

class ExchangeRateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = ExchangeRate::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        // Get random currencies for base and target
        $currencies = Currency::pluck('code')->toArray();
        $baseCurrency = $this->faker->randomElement($currencies);
        
        do {
            $targetCurrency = $this->faker->randomElement($currencies);
        } while ($baseCurrency === $targetCurrency);

        return [
            'base_currency' => $baseCurrency,
            'target_currency' => $targetCurrency,
            'rate' => $this->generateRealisticRate($baseCurrency, $targetCurrency),
            'fetched_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ];
    }

    /**
     * Generate realistic exchange rates based on currency pairs.
     */
    private function generateRealisticRate(string $baseCurrency, string $targetCurrency): float
    {
        // Realistic base rates (approximate)
        $baseRates = [
            'USD' => [
                'VND' => $this->faker->randomFloat(2, 22000, 24000),
                'EUR' => $this->faker->randomFloat(4, 0.80, 0.90),
                'GBP' => $this->faker->randomFloat(4, 0.70, 0.80),
                'JPY' => $this->faker->randomFloat(2, 100, 120),
                'CNY' => $this->faker->randomFloat(2, 6.0, 7.0),
                'KRW' => $this->faker->randomFloat(0, 1100, 1300),
                'THB' => $this->faker->randomFloat(2, 30, 36),
                'SGD' => $this->faker->randomFloat(4, 1.30, 1.40),
                'MYR' => $this->faker->randomFloat(2, 4.0, 4.5),
                'IDR' => $this->faker->randomFloat(0, 14000, 15000),
            ],
            'EUR' => [
                'USD' => $this->faker->randomFloat(4, 1.10, 1.25),
                'VND' => $this->faker->randomFloat(2, 25000, 28000),
                'GBP' => $this->faker->randomFloat(4, 0.85, 0.90),
                'JPY' => $this->faker->randomFloat(2, 120, 140),
            ],
            'GBP' => [
                'USD' => $this->faker->randomFloat(4, 1.20, 1.40),
                'EUR' => $this->faker->randomFloat(4, 1.10, 1.20),
                'VND' => $this->faker->randomFloat(2, 28000, 32000),
            ],
        ];

        // If we have a predefined rate, use it with some variation
        if (isset($baseRates[$baseCurrency][$targetCurrency])) {
            return $baseRates[$baseCurrency][$targetCurrency];
        }

        // If reverse rate exists, calculate inverse
        if (isset($baseRates[$targetCurrency][$baseCurrency])) {
            return 1 / $baseRates[$targetCurrency][$baseCurrency];
        }

        // Default random rate for unknown pairs
        return $this->faker->randomFloat(4, 0.1, 100);
    }

    /**
     * Create fresh exchange rate (updated within last hour).
     */
    public function fresh(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'fetched_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            ];
        });
    }

    /**
     * Create stale exchange rate (older than 24 hours).
     */
    public function stale(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'fetched_at' => $this->faker->dateTimeBetween('-1 week', '-1 day'),
            ];
        });
    }

    /**
     * Create exchange rate with specific currency pair.
     */
    public function forCurrencyPair(string $baseCurrency, string $targetCurrency): static
    {
        return $this->state(function (array $attributes) use ($baseCurrency, $targetCurrency) {
            return [
                'base_currency' => $baseCurrency,
                'target_currency' => $targetCurrency,
                'rate' => $this->generateRealisticRate($baseCurrency, $targetCurrency),
            ];
        });
    }

    /**
     * Create exchange rate with specific rate.
     */
    public function withRate(float $rate): static
    {
        return $this->state(function (array $attributes) use ($rate) {
            return [
                'rate' => $rate,
            ];
        });
    }
}
