<?php

namespace Modules\Currency\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Currency\Facades\CurrencyFacade;
use Modules\Currency\Models\Currency;
use Modules\Currency\Models\ExchangeRate;
use Modules\Currency\Services\CurrencyService;
use PHPUnit\Framework\Attributes\Test;

class CurrencyFacadeTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Currency module
        $this->artisan('migrate', ['--path' => 'Modules/Currency/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_resolves_to_currency_service()
    {
        $service = CurrencyFacade::getFacadeRoot();

        $this->assertInstanceOf(CurrencyService::class, $service);
    }

    #[Test]
    public function it_can_get_active_currencies_through_facade()
    {
        Currency::factory()->create(['status' => 'active']);
        Currency::factory()->create(['status' => 'active']);
        Currency::factory()->create(['status' => 'inactive']);

        $currencies = CurrencyFacade::getActiveCurrencies();

        $this->assertCount(2, $currencies);
        $currencies->each(function ($currency) {
            $this->assertEquals('active', $currency->status);
        });
    }

    #[Test]
    public function it_can_get_default_currency_through_facade()
    {
        // Mock setting function
        $this->app->bind('setting', function () {
            return function ($key, $default = null) {
                if ($key === 'currency.currency_default') {
                    return 'USD';
                }
                return $default;
            };
        });

        $usdCurrency = Currency::factory()->create(['code' => 'USD', 'status' => 'active']);
        Currency::factory()->create(['code' => 'EUR', 'status' => 'active']);

        $defaultCurrency = CurrencyFacade::getDefaultCurrency();

        $this->assertNotNull($defaultCurrency);
        $this->assertEquals($usdCurrency->id, $defaultCurrency->id);
        $this->assertEquals('USD', $defaultCurrency->code);
    }

    #[Test]
    public function it_can_find_currency_by_code_through_facade()
    {
        $currency = Currency::factory()->create(['code' => 'USD', 'status' => 'active']);

        $foundCurrency = CurrencyFacade::findByCode('USD');

        $this->assertNotNull($foundCurrency);
        $this->assertEquals($currency->id, $foundCurrency->id);
    }

    #[Test]
    public function it_can_convert_amount_through_facade()
    {
        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'rate' => 0.85,
        ]);

        $convertedAmount = CurrencyFacade::convertAmount(100, 'USD', 'EUR');

        $this->assertEquals(85.0, $convertedAmount);
    }

    #[Test]
    public function it_can_get_exchange_rate_through_facade()
    {
        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'rate' => 0.85,
        ]);

        $rate = CurrencyFacade::getExchangeRate('USD', 'EUR');

        $this->assertEquals(0.85, $rate);
    }

    #[Test]
    public function it_can_format_amount_through_facade()
    {
        $currency = Currency::factory()->create([
            'code' => 'USD',
            'symbol' => '$',
            'decimal_digits' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
        ]);

        $formatted = CurrencyFacade::formatAmount(1234.56, 'USD');

        $this->assertEquals('$ 1,234.56', $formatted);
    }

    #[Test]
    public function it_can_get_supported_currency_codes_through_facade()
    {
        Currency::factory()->create(['code' => 'USD', 'status' => 'active']);
        Currency::factory()->create(['code' => 'EUR', 'status' => 'active']);
        Currency::factory()->create(['code' => 'GBP', 'status' => 'inactive']);

        $codes = CurrencyFacade::getSupportedCurrencyCodes();

        $this->assertCount(2, $codes);
        $this->assertContains('USD', $codes);
        $this->assertContains('EUR', $codes);
        $this->assertNotContains('GBP', $codes);
    }

    #[Test]
    public function it_can_get_currencies_for_dropdown_through_facade()
    {
        Currency::factory()->create(['code' => 'USD', 'name' => 'US Dollar', 'status' => 'active']);
        Currency::factory()->create(['code' => 'EUR', 'name' => 'Euro', 'status' => 'active']);
        Currency::factory()->create(['code' => 'GBP', 'name' => 'British Pound', 'status' => 'inactive']);

        $dropdown = CurrencyFacade::getCurrenciesForDropdown();

        $this->assertCount(2, $dropdown);
        $dropdown->each(function ($currency) {
            $this->assertArrayHasKey('code', $currency);
            $this->assertArrayHasKey('name', $currency);
        });
    }

    #[Test]
    public function it_can_update_exchange_rate_through_facade()
    {
        Currency::factory()->create(['code' => 'USD']);
        Currency::factory()->create(['code' => 'EUR']);

        $exchangeRate = CurrencyFacade::updateExchangeRate('USD', 'EUR', 0.85);

        $this->assertInstanceOf(ExchangeRate::class, $exchangeRate);
        $this->assertEquals('USD', $exchangeRate->base_currency);
        $this->assertEquals('EUR', $exchangeRate->target_currency);
        $this->assertEquals(0.85, $exchangeRate->rate);
        $this->assertNotNull($exchangeRate->fetched_at);
    }

    #[Test]
    public function it_can_clear_cache_through_facade()
    {
        // This test verifies the method exists and can be called
        // The actual cache clearing functionality is tested in CurrencyServiceTest
        $result = CurrencyFacade::clearCache();

        // Method should execute without errors
        $this->assertTrue(true);
    }

    #[Test]
    public function it_can_clear_exchange_rate_cache_through_facade()
    {
        // This test verifies the method exists and can be called
        // The actual cache clearing functionality is tested in CurrencyServiceTest
        $result = CurrencyFacade::clearExchangeRateCache();

        // Method should execute without errors
        $this->assertTrue(true);
    }

    #[Test]
    public function it_can_find_currency_by_symbol_through_facade()
    {
        $currency = Currency::factory()->create(['symbol' => '$', 'status' => 'active']);

        $foundCurrency = CurrencyFacade::findBySymbol('$');

        $this->assertNotNull($foundCurrency);
        $this->assertEquals($currency->id, $foundCurrency->id);
    }

    #[Test]
    public function it_can_check_if_currency_is_supported_through_facade()
    {
        Currency::factory()->create(['code' => 'USD', 'status' => 'active']);
        Currency::factory()->create(['code' => 'EUR', 'status' => 'inactive']);

        $this->assertTrue(CurrencyFacade::isCurrencySupported('USD'));
        $this->assertFalse(CurrencyFacade::isCurrencySupported('EUR')); // inactive
        $this->assertFalse(CurrencyFacade::isCurrencySupported('XYZ')); // not exists
    }

    #[Test]
    public function it_can_get_currency_display_name_through_facade()
    {
        $currency = Currency::factory()->create([
            'code' => 'USD',
            'name' => 'US Dollar',
            'symbol' => '$',
            'status' => 'active',
        ]);

        $displayName = CurrencyFacade::getCurrencyDisplayName('USD');

        $this->assertEquals('US Dollar ($)', $displayName);
    }

    #[Test]
    public function it_handles_null_returns_gracefully()
    {
        // Test methods that can return null
        $this->assertNull(CurrencyFacade::findByCode('NONEXISTENT'));
        $this->assertNull(CurrencyFacade::findBySymbol('¤'));
        $this->assertNull(CurrencyFacade::getDefaultCurrency());
        $this->assertNull(CurrencyFacade::convertAmount(100, 'USD', 'NONEXISTENT'));
        $this->assertNull(CurrencyFacade::getExchangeRate('USD', 'NONEXISTENT'));
    }

    #[Test]
    public function it_handles_same_currency_conversions()
    {
        $amount = 100.0;
        $convertedAmount = CurrencyFacade::convertAmount($amount, 'USD', 'USD');
        $exchangeRate = CurrencyFacade::getExchangeRate('USD', 'USD');

        $this->assertEquals($amount, $convertedAmount);
        $this->assertEquals(1.0, $exchangeRate);
    }

    #[Test]
    public function it_provides_fallback_formatting()
    {
        $formatted = CurrencyFacade::formatAmount(1234.56, 'NONEXISTENT');

        $this->assertEquals('1,234.56 NONEXISTENT', $formatted);
    }

    #[Test]
    public function it_maintains_method_signatures()
    {
        // Test that all documented facade methods exist and have correct signatures
        $reflection = new \ReflectionClass(CurrencyFacade::class);
        
        // Check that facade extends Facade
        $this->assertTrue($reflection->isSubclassOf(\Illuminate\Support\Facades\Facade::class));
    }

    #[Test]
    public function it_delegates_all_calls_to_service()
    {
        // Create a mock service to verify delegation
        $mockService = $this->createMock(CurrencyService::class);
        
        // Bind the mock to the container
        $this->app->instance(CurrencyService::class, $mockService);

        // Test that facade methods delegate to service
        $mockService->expects($this->once())
            ->method('getActiveCurrencies')
            ->willReturn(collect());

        CurrencyFacade::getActiveCurrencies();
    }

    #[Test]
    public function it_works_with_real_data_scenarios()
    {
        // Create realistic test scenario
        $usd = Currency::factory()->usd()->create();
        $eur = Currency::factory()->eur()->create();
        $vnd = Currency::factory()->vnd()->create();

        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'EUR',
            'rate' => 0.85,
        ]);

        ExchangeRate::factory()->create([
            'base_currency' => 'USD',
            'target_currency' => 'VND',
            'rate' => 23000,
        ]);

        // Test various facade operations
        $currencies = CurrencyFacade::getActiveCurrencies();
        $this->assertCount(3, $currencies);

        $usdToEur = CurrencyFacade::convertAmount(100, 'USD', 'EUR');
        $this->assertEquals(85.0, $usdToEur);

        $usdToVnd = CurrencyFacade::convertAmount(100, 'USD', 'VND');
        $this->assertEquals(2300000.0, $usdToVnd);

        $formattedUsd = CurrencyFacade::formatAmount(1234.56, 'USD');
        $this->assertEquals('$ 1,234.56', $formattedUsd);

        $formattedVnd = CurrencyFacade::formatAmount(1234567, 'VND');
        $this->assertEquals('₫ 1,234,567', $formattedVnd);
    }
}
