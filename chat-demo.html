<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProcMS Chat Widget - Live Demo</title>
    <link rel="stylesheet" href="dist/widget/procms-chatbot.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .hero {
            text-align: center;
            padding: 60px 20px;
        }
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        .hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: start;
        }
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .demo-card h3 {
            margin-top: 0;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .widget-container {
            width: 100%;
            height: 600px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            margin: 20px 0;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }
        .status.success { 
            background: rgba(34, 197, 94, 0.2); 
            border: 1px solid rgba(34, 197, 94, 0.4);
        }
        .status.info { 
            background: rgba(59, 130, 246, 0.2); 
            border: 1px solid rgba(59, 130, 246, 0.4);
        }
        .features {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            margin: 40px auto;
            max-width: 1200px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .features h3 {
            text-align: center;
            margin-bottom: 2rem;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .feature-item {
            text-align: center;
            padding: 20px;
        }
        .feature-item .icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        .feature-item h4 {
            margin: 0.5rem 0;
        }
        .feature-item p {
            opacity: 0.8;
            font-size: 0.9rem;
        }
        .floating-demo {
            text-align: center;
        }
        .floating-demo .placeholder {
            background: rgba(255, 255, 255, 0.1);
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 60px 20px;
            margin: 20px 0;
        }
        .floating-demo .placeholder .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        @media (max-width: 768px) {
            .demo-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .hero h1 {
                font-size: 2rem;
            }
            .demo-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="hero">
        <h1>💬 Live Chat Demo</h1>
        <p>Experience real-time AI conversations with our intelligent chatbot widget</p>
    </div>

    <div class="demo-container">
        <!-- Embedded Chat Demo -->
        <div class="demo-card">
            <h3>📱 Embedded Chat Widget</h3>
            <p>Full-featured chat interface embedded directly in your page.</p>
            
            <div class="controls">
                <button class="btn" onclick="loadEmbeddedChat()">Start Chat</button>
                <button class="btn" onclick="clearEmbeddedChat()">Clear Chat</button>
            </div>
            
            <div id="embedded-chat" class="widget-container"></div>
            <div id="embedded-status" class="status" style="display: none;"></div>
        </div>

        <!-- Floating Chat Demo -->
        <div class="demo-card floating-demo">
            <h3>🎈 Floating Chat Widget</h3>
            <p>Floating chat button that appears over your content.</p>
            
            <div class="controls">
                <button class="btn" onclick="createFloatingChat()">Launch Floating Chat</button>
                <button class="btn" onclick="removeFloatingChat()">Remove Chat</button>
            </div>
            
            <div class="placeholder">
                <div class="icon">💬</div>
                <div>Click "Launch Floating Chat" to see the widget appear in the bottom-right corner</div>
                <div style="font-size: 0.9rem; opacity: 0.7; margin-top: 1rem;">
                    The floating widget will overlay this content
                </div>
            </div>
            
            <div id="floating-status" class="status" style="display: none;"></div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="features">
        <h3>🚀 Chat Features</h3>
        <div class="feature-grid">
            <div class="feature-item">
                <div class="icon">🤖</div>
                <h4>Intelligent Responses</h4>
                <p>AI-powered bot that understands context and provides helpful answers</p>
            </div>
            <div class="feature-item">
                <div class="icon">⚡</div>
                <h4>Real-time Chat</h4>
                <p>Instant message sending with typing indicators and timestamps</p>
            </div>
            <div class="feature-item">
                <div class="icon">📱</div>
                <h4>Responsive Design</h4>
                <p>Works perfectly on desktop, tablet, and mobile devices</p>
            </div>
            <div class="feature-item">
                <div class="icon">🎨</div>
                <h4>Customizable</h4>
                <p>Multiple themes and easy customization options</p>
            </div>
            <div class="feature-item">
                <div class="icon">🔄</div>
                <h4>Minimize/Maximize</h4>
                <p>Click header to minimize, smooth animations</p>
            </div>
            <div class="feature-item">
                <div class="icon">💾</div>
                <h4>Conversation Memory</h4>
                <p>Bot remembers conversation context for better responses</p>
            </div>
        </div>
    </div>

    <!-- Try These Messages -->
    <div class="features">
        <h3>💡 Try These Messages</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; text-align: center;">
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <strong>"Hello"</strong><br>
                <small>Get a friendly greeting</small>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <strong>"Help me"</strong><br>
                <small>See available options</small>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <strong>"Product features"</strong><br>
                <small>Learn about features</small>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <strong>"Pricing plans"</strong><br>
                <small>Get pricing info</small>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <strong>"API documentation"</strong><br>
                <small>Technical information</small>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <strong>"Schedule demo"</strong><br>
                <small>Request a demo</small>
            </div>
        </div>
    </div>

    <!-- Load Widget Library -->
    <script src="dist/widget/procms-chatbot.umd.js"></script>
    <script>
        let embeddedWidget = null;
        let floatingWidget = null;

        function showStatus(elementId, message, type = 'info') {
            const statusEl = document.getElementById(elementId);
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            console.log(`[Chat Demo] ${message}`);
        }

        // Embedded Chat Functions
        async function loadEmbeddedChat() {
            try {
                if (embeddedWidget) {
                    embeddedWidget.unmount();
                }

                showStatus('embedded-status', 'Loading chat widget...', 'info');

                embeddedWidget = await ProcmsChatbot.create({
                    botUuid: 'chat-demo-bot-123',
                    apiKey: 'pk_live_chat_demo_key_12345678901234567890',
                    theme: 'light',
                    onReady: () => {
                        showStatus('embedded-status', 'Chat ready! Start typing to begin conversation.', 'success');
                    },
                    onMessage: (message) => {
                        console.log('Chat message:', message);
                    },
                    onError: (error) => {
                        showStatus('embedded-status', `Error: ${error.message}`, 'error');
                    }
                }, '#embedded-chat');

            } catch (error) {
                showStatus('embedded-status', `Failed to load chat: ${error.message}`, 'error');
                console.error('Chat load error:', error);
            }
        }

        function clearEmbeddedChat() {
            if (embeddedWidget) {
                embeddedWidget.unmount();
                embeddedWidget = null;
                showStatus('embedded-status', 'Chat cleared.', 'info');
            }
        }

        // Floating Chat Functions
        async function createFloatingChat() {
            try {
                if (floatingWidget) {
                    removeFloatingChat();
                }

                showStatus('floating-status', 'Creating floating chat...', 'info');

                floatingWidget = await ProcmsChatbot.createFloatingWidget({
                    botUuid: 'floating-chat-demo-123',
                    apiKey: 'pk_live_floating_chat_key_12345678901234567890',
                    theme: 'dark',
                    position: 'bottom-right',
                    onReady: () => {
                        showStatus('floating-status', 'Floating chat created! Check bottom-right corner.', 'success');
                    },
                    onMessage: (message) => {
                        console.log('Floating chat message:', message);
                    },
                    onError: (error) => {
                        showStatus('floating-status', `Error: ${error.message}`, 'error');
                    }
                });

            } catch (error) {
                showStatus('floating-status', `Failed to create floating chat: ${error.message}`, 'error');
                console.error('Floating chat error:', error);
            }
        }

        function removeFloatingChat() {
            if (floatingWidget) {
                floatingWidget.unmount();
                const container = document.getElementById('procms-floating-widget');
                if (container) {
                    container.remove();
                }
                floatingWidget = null;
                showStatus('floating-status', 'Floating chat removed.', 'info');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Chat Demo loaded');
            console.log('💡 Try these sample messages:');
            console.log('  - "Hello" - Get a greeting');
            console.log('  - "Help me" - See available options');
            console.log('  - "Product features" - Learn about features');
            console.log('  - "Pricing plans" - Get pricing info');
            console.log('  - "API documentation" - Technical info');
            console.log('  - "Schedule demo" - Request a demo');
            
            // Check if widget library is loaded
            if (typeof ProcmsChatbotWidget !== 'undefined') {
                console.log('✅ Chat widget library loaded successfully');
            } else {
                console.error('❌ Chat widget library failed to load');
            }
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (embeddedWidget) embeddedWidget.unmount();
            if (floatingWidget) floatingWidget.unmount();
        });

        // Auto-load embedded chat after 2 seconds for demo
        setTimeout(() => {
            if (!embeddedWidget) {
                loadEmbeddedChat();
            }
        }, 2000);
    </script>
</body>
</html>
