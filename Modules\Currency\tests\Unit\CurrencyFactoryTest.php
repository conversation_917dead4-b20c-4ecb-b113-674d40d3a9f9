<?php

namespace Modules\Currency\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Currency\Models\Currency;
use PHPUnit\Framework\Attributes\Test;

class CurrencyFactoryTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Currency module
        $this->artisan('migrate', ['--path' => 'Modules/Currency/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_can_create_a_currency_using_factory()
    {
        $currency = Currency::factory()->create();

        $this->assertInstanceOf(Currency::class, $currency);
        $this->assertDatabaseHas('currencies', [
            'id' => $currency->id,
        ]);
    }

    #[Test]
    public function it_generates_valid_currency_attributes()
    {
        $currency = Currency::factory()->create();

        // Check required attributes are present
        $this->assertNotNull($currency->code);
        $this->assertNotNull($currency->symbol);
        $this->assertNotNull($currency->name);
        $this->assertNotNull($currency->decimal_digits);
        $this->assertNotNull($currency->decimal_separator);
        $this->assertNotNull($currency->thousands_separator);
        $this->assertNotNull($currency->status);
        $this->assertNotNull($currency->sort_order);

        // Check attribute types and formats
        $this->assertIsString($currency->code);
        $this->assertEquals(3, strlen($currency->code));
        $this->assertIsString($currency->symbol);
        $this->assertIsString($currency->name);
        $this->assertIsInt($currency->decimal_digits);
        $this->assertIsString($currency->decimal_separator);
        $this->assertIsString($currency->thousands_separator);
        $this->assertIsString($currency->status);
        $this->assertIsInt($currency->sort_order);
    }

    #[Test]
    public function it_generates_realistic_currency_data()
    {
        $currency = Currency::factory()->create();

        // Check code is uppercase and 3 characters
        $this->assertEquals(strtoupper($currency->code), $currency->code);
        $this->assertEquals(3, strlen($currency->code));

        // Check decimal digits is within valid range
        $this->assertGreaterThanOrEqual(0, $currency->decimal_digits);
        $this->assertLessThanOrEqual(8, $currency->decimal_digits);

        // Check decimal separator is valid
        $this->assertContains($currency->decimal_separator, ['.', ',']);

        // Check thousands separator is valid
        $this->assertContains($currency->thousands_separator, [',', '.', ' ']);

        // Check status is valid
        $this->assertContains($currency->status, ['active', 'inactive']);

        // Check sort order is within valid range
        $this->assertGreaterThanOrEqual(1, $currency->sort_order);
        $this->assertLessThanOrEqual(100, $currency->sort_order);
    }

    #[Test]
    public function it_can_create_active_currency_state()
    {
        $currency = Currency::factory()->active()->create();

        $this->assertEquals('active', $currency->status);
        $this->assertTrue($currency->isActive());
    }

    #[Test]
    public function it_can_create_inactive_currency_state()
    {
        $currency = Currency::factory()->inactive()->create();

        $this->assertEquals('inactive', $currency->status);
        $this->assertFalse($currency->isActive());
    }

    #[Test]
    public function it_can_create_usd_currency_state()
    {
        $currency = Currency::factory()->usd()->create();

        $this->assertEquals('USD', $currency->code);
        $this->assertEquals('$', $currency->symbol);
        $this->assertEquals('US Dollar', $currency->name);
        $this->assertEquals(2, $currency->decimal_digits);
        $this->assertEquals('.', $currency->decimal_separator);
        $this->assertEquals(',', $currency->thousands_separator);
        $this->assertEquals('active', $currency->status);
    }

    #[Test]
    public function it_can_create_eur_currency_state()
    {
        $currency = Currency::factory()->eur()->create();

        $this->assertEquals('EUR', $currency->code);
        $this->assertEquals('€', $currency->symbol);
        $this->assertEquals('Euro', $currency->name);
        $this->assertEquals(2, $currency->decimal_digits);
        $this->assertEquals(',', $currency->decimal_separator);
        $this->assertEquals('.', $currency->thousands_separator);
        $this->assertEquals('active', $currency->status);
    }

    #[Test]
    public function it_can_create_vnd_currency_state()
    {
        $currency = Currency::factory()->vnd()->create();

        $this->assertEquals('VND', $currency->code);
        $this->assertEquals('₫', $currency->symbol);
        $this->assertEquals('Vietnamese Dong', $currency->name);
        $this->assertEquals(0, $currency->decimal_digits);
        $this->assertEquals('.', $currency->decimal_separator);
        $this->assertEquals(',', $currency->thousands_separator);
        $this->assertEquals('active', $currency->status);
    }

    #[Test]
    public function it_can_create_multiple_currencies()
    {
        $currencies = Currency::factory()->count(5)->create();

        $this->assertCount(5, $currencies);
        $currencies->each(function ($currency) {
            $this->assertInstanceOf(Currency::class, $currency);
        });
    }

    #[Test]
    public function it_can_override_factory_attributes()
    {
        $currency = Currency::factory()->create([
            'code' => 'GBP',
            'symbol' => '£',
            'name' => 'British Pound',
            'status' => 'active',
        ]);

        $this->assertEquals('GBP', $currency->code);
        $this->assertEquals('£', $currency->symbol);
        $this->assertEquals('British Pound', $currency->name);
        $this->assertEquals('active', $currency->status);
    }

    #[Test]
    public function it_can_make_currency_without_persisting()
    {
        $currency = Currency::factory()->make();

        $this->assertInstanceOf(Currency::class, $currency);
        $this->assertNull($currency->id);
        $this->assertDatabaseMissing('currencies', [
            'code' => $currency->code,
        ]);
    }

    #[Test]
    public function it_can_create_currency_with_specific_attributes()
    {
        $attributes = [
            'code' => 'JPY',
            'symbol' => '¥',
            'name' => 'Japanese Yen',
            'decimal_digits' => 0,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'status' => 'active',
            'sort_order' => 5,
        ];

        $currency = Currency::factory()->create($attributes);

        foreach ($attributes as $key => $value) {
            $this->assertEquals($value, $currency->$key);
        }
    }

    #[Test]
    public function it_generates_different_currencies_on_multiple_calls()
    {
        $currency1 = Currency::factory()->create();
        $currency2 = Currency::factory()->create();

        // They should be different instances
        $this->assertNotEquals($currency1->id, $currency2->id);
        
        // They might have different codes (depending on random selection)
        // But both should be valid currencies
        $this->assertNotNull($currency1->code);
        $this->assertNotNull($currency2->code);
    }

    #[Test]
    public function it_can_combine_states()
    {
        $currency = Currency::factory()->usd()->active()->create();

        $this->assertEquals('USD', $currency->code);
        $this->assertEquals('active', $currency->status);
    }

    #[Test]
    public function it_can_create_currencies_with_sequence()
    {
        $currencies = Currency::factory()
            ->count(3)
            ->sequence(
                ['code' => 'USD', 'name' => 'US Dollar'],
                ['code' => 'EUR', 'name' => 'Euro'],
                ['code' => 'GBP', 'name' => 'British Pound']
            )
            ->create();

        $this->assertEquals('USD', $currencies[0]->code);
        $this->assertEquals('EUR', $currencies[1]->code);
        $this->assertEquals('GBP', $currencies[2]->code);
    }

    #[Test]
    public function it_uses_realistic_currency_pool()
    {
        // Create multiple currencies and check they come from realistic pool
        $currencies = Currency::factory()->count(10)->create();

        $validCodes = [
            'USD', 'EUR', 'GBP', 'JPY', 'CNY', 'KRW', 'VND', 'THB', 'SGD', 'MYR',
            'IDR', 'PHP', 'INR', 'AUD', 'CAD', 'CHF', 'SEK', 'NOK', 'DKK', 'PLN',
            'CZK', 'HUF', 'RUB', 'TRY', 'ZAR', 'BRL', 'MXN', 'ARS', 'CLP', 'COP',
            'PEN', 'UYU', 'EGP', 'MAD', 'NGN', 'KES', 'GHS', 'XOF', 'XAF', 'AED',
            'SAR', 'QAR', 'KWD'
        ];

        $currencies->each(function ($currency) use ($validCodes) {
            $this->assertContains($currency->code, $validCodes);
        });
    }

    #[Test]
    public function it_generates_appropriate_symbols_for_currencies()
    {
        $usdCurrency = Currency::factory()->usd()->create();
        $eurCurrency = Currency::factory()->eur()->create();
        $vndCurrency = Currency::factory()->vnd()->create();

        $this->assertEquals('$', $usdCurrency->symbol);
        $this->assertEquals('€', $eurCurrency->symbol);
        $this->assertEquals('₫', $vndCurrency->symbol);
    }
}
